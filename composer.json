{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^7.1.3", "aws/aws-sdk-php": "^3.263", "doctrine/dbal": "^2.10", "fideloper/proxy": "^4.0", "google/apiclient": "^2.0", "laravel-notification-channels/fcm": "2.7", "laravel/framework": "5.7.*", "laravel/horizon": "^1.3", "laravel/socialite": "~2.0.0", "laravel/tinker": "^1.0", "laravelista/ekko": "^1.5", "maatwebsite/excel": "^3.1", "nao-pon/flysystem-google-drive": "~1.1", "predis/predis": "^1.1", "ramsey/uuid": "^3.8", "sentry/sentry-laravel": "^0.11.0", "spatie/laravel-backup": "^5.0.0", "taobig/apple-id-login": "^1.0", "tymon/jwt-auth": "0.5.*", "voku/anti-xss": "^4.1", "yajra/laravel-datatables-oracle": "^8.0"}, "require-dev": {"filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^2.0", "phpunit/phpunit": "^7.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": false}}, "minimum-stability": "dev", "prefer-stable": true}