FROM ghcr.io/wmt-web/laravel-7.4-fpm-alpine:main

WORKDIR /var/www

RUN apk --no-cache add pcre-dev ${PHPIZE_DEPS} libxml2-dev libressl-dev pkgconfig libevent-dev libzip-dev && apk del pcre-dev ${PHPIZE_DEPS}
RUN docker-php-ext-install zip exif soap

COPY composer.json composer.json
COPY composer.lock composer.lock

RUN composer install --prefer-dist --no-scripts --no-autoloader && rm -rf /root/.composer

RUN apk update && \
    apk add libxml2-dev

ADD conf/nginx/default.conf /etc/nginx/conf.d/

ADD . .

RUN cp .env.dev_server .env

RUN chmod a+trwx /tmp
RUN chown -R www-data:www-data \
        /var/www/storage \
        /var/www/bootstrap/cache

RUN echo '0  22  *  *  * rm /tmp/php*' >> /etc/crontabs/root

RUN composer dump-autoload --no-scripts --optimize
RUN ln -s /var/www/storage/app/public /var/www/public/storage
RUN chmod 777 -R storage/ bootstrap/cache
ENTRYPOINT [ "/usr/bin/supervisord" ]