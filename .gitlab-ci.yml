image: 'docker:19.03.5'
stages:
  - build
  - development-deploy
variables:
  DOCKER_DRIVER: overlay
  NGINX_IMAGE: nginx-web
  PHP_IMAGE: php-fpm
  DOCKER_DEV_URL: gitlab.webmobtech.biz:5050/mblion/mblion_backend/php-fpm
  DOCKER_PROD_URL: gitlab.webmobtech.biz:5050/mblion/mblion_backend/php-prod-fpm
services:
  - 'docker:19.03.5-dind'
before_script:
  - 'docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY'
build-php-fpm:
  stage: build
  script:
    - 'docker build --pull -t $DOCKER_DEV_URL:$CI_PIPELINE_ID -f php.dockerfile .'
    - 'docker push $DOCKER_DEV_URL:$CI_PIPELINE_ID'
  only:
    - master

deploy-to-k8s:
  stage: development-deploy
  before_script:
    - apk add --no-cache openssh sshpass
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
  script:
    - sed -i "s/{{CI_PIPELINE_ID}}/$CI_PIPELINE_ID/g" $CI_PROJECT_DIR/k8s-file/deployment.yml
    - cat $CI_PROJECT_DIR/k8s-file/deployment.yml
    - ssh-keyscan $PUB_IP_ADDRESS >> ~/.ssh/known_hosts # Add server fingerprint
    - test -f ~/.ssh/known_hosts && ssh-keygen -F $PUB_IP_ADDRESS > /dev/null 2>&1
    - sshpass -p "$SSH_PASSWORD" scp $CI_PROJECT_DIR/k8s-file/deployment.yml $EC2_USER_NAME@$PUB_IP_ADDRESS:/home/<USER>/project-cicd/mblion/deployment.yml
    - sshpass -p "$SSH_PASSWORD" ssh $EC2_USER_NAME@$PUB_IP_ADDRESS "echo '$SSH_PASSWORD' | sudo -S bash -c 'su - root -c \"cd /home/<USER>/project-cicd/mblion && kubectl apply -f deployment.yml\"'"
  only:
    - master
