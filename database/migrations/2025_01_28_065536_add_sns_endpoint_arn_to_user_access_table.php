<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSnsEndpointArnToUserAccessTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_access', function (Blueprint $table) {
            $table->string('sns_endpoint_arn')->nullable()->after('fcm_token')->comment('Stores AWS SNS endpoint ARN for push notifications');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_access', function (Blueprint $table) {
            $table->dropColumn('sns_endpoint_arn');
        });
    }
}

