<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid');
            $table->unsignedInteger('country_id')->nullable();
            $table->string('fullname');
            $table->string('email_id')->unique();
            $table->string('password')->nullable();
            $table->string('country_code')->nullable();
            $table->string('mobile_no')->nullable();
            $table->string('company_name')->nullable();
            $table->string('profile_pic')->nullable();
            $table->tinyInteger('is_social')->default(\App\Utils\AppConstant::STATUS_INACTIVE);
            $table->integer('otp_no')->nullable();
            $table->string('forgot_password_code')->nullable();
            $table->tinyInteger('is_email_verified')->default(\App\Utils\AppConstant::STATUS_INACTIVE);
            $table->tinyInteger('message_notifications')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->tinyInteger('favourite_option')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->tinyInteger('status')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->timestamps();
            $table->foreign('country_id')->references('id')->on('country')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user');
    }
}
