<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid');
            $table->unsignedInteger('category_id');
            $table->unsignedInteger('packing_id');
            $table->unsignedInteger('unit_id');
            $table->unsignedInteger('delivery_term_id');
            $table->unsignedInteger('currency_id');
            $table->string('name');
            $table->string('hs_code');
            $table->string('cas_number');
            $table->string('loading_port');
            $table->tinyInteger('marquee')->default(\App\Utils\AppConstant::STATUS_INACTIVE);
            $table->tinyInteger('status')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->timestamps();
            $table->foreign('category_id')->references('id')->on('category')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('packing_id')->references('id')->on('packing')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('unit_id')->references('id')->on('unit')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('delivery_term_id')->references('id')->on('delivery_term')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('currency_id')->references('id')->on('currency')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product');
    }
}
