<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCifQuotationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cif_quotation', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('packing_id');
            $table->unsignedInteger('unit_id');
            $table->string('email_id');
            $table->string('product_name');
            $table->string('destination_port');
            $table->string('trade_term');
            $table->string('country');
            $table->integer('quantity');
            $table->text('message');
            $table->tinyInteger('status')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->timestamps();
            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('packing_id')->references('id')->on('packing')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('unit_id')->references('id')->on('unit')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cif_quotation');
    }
}
