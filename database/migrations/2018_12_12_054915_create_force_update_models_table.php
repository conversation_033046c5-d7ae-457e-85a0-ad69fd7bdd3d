<?php

use App\Utils\AppConstant;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateForceUpdateModelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('force_update_models', function (Blueprint $table) {
            $table->increments('id');
            $table->string('version');
            $table->string('version_code');
            $table->string('device_type')->comment("android,ios");
            $table->tinyInteger('is_force_update')->default(AppConstant::STATUS_INACTIVE);
            $table->tinyInteger('status')->default(AppConstant::STATUS_ACTIVE);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('force_update_models');
    }
}
