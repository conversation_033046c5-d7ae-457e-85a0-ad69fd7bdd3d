<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->nullable();
            $table->unsignedInteger('product_id');
            $table->text('message');
            $table->float('min_price');
            $table->float('max_price')->nullable();
            $table->float('price_diff')->nullable();
            $table->tinyInteger('message_type')->comment('1 = All');
            $table->tinyInteger('is_read')->default(\App\Utils\AppConstant::STATUS_INACTIVE)->comment('0 = No, 1 = Yes');
            $table->tinyInteger('status')->default(\App\Utils\AppConstant::STATUS_ACTIVE);
            $table->timestamps();
            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('product_id')->references('id')->on('product')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}
