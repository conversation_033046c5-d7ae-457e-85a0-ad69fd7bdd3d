<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PackingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            'image_path' => 'assets/images/icon/heart_gradient.svg'
        ];

        $packings = array(

            [
                'packing_type' => 'Bulk Vessel',
                'image_path' => 'Bulk_Vessels.png'
            ],
            [
                'packing_type' => 'Steel Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => 'HDPE Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '250 KG Steel Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '250 KG HDPE Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '200 KG Steel Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '200 KG HDPE Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '180 KG HDPE Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '180 KG Steel Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '170 KG Steel Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '170 KG HDPE Drums',
                'image_path' => 'packing/Drums.png'
            ],
            [
                'packing_type' => '165 KG HDPE Drums',
                'image_path' => 'Drums.png'
            ],
            [
                'packing_type' => '1000 KG IBC',
                'image_path' => 'IBC_Tank.png'
            ],
            [
                'packing_type' => '20 MT IBC',
                'image_path' => 'IBC_Tank.png'
            ],
            [
                'packing_type' => '20 MT IBC',
                'image_path' => 'IBC_Tank.png'
            ],

            [
                'packing_type' => 'ISO Tank',
                'image_path' => 'Other_Tanks.png'
            ],

            [
                'packing_type' => '20 MT ISO Tank',
                'image_path' => 'Other_Tanks.png'
            ],

            [
                'packing_type' => 'Carton',
                'image_path' => 'Other_Tanks.png'
            ],

            [
                'packing_type' => 'Flexi Bag',
                'image_path' => 'Other_Tanks.png'
            ],

            [
                'packing_type' => 'Bulk Bags',
                'image_path' => 'Other_Tanks.png'
            ],

            [
                'packing_type' => '25 Kg Bags',
                'image_path' => 'Other_Tanks.png'
            ],
        );

        for ($i = 0; $i < count($packings); $i++) {

            DB::table('packing')->insert(array(
                'packing_type' => $packings[$i]['packing_type'],
                'image_path' => $packings[$i]['image_path'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));
        }
    }
}
