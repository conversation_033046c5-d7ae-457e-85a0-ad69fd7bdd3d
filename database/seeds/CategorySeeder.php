<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = array(
            [
                'name' => 'Palm Oil',
                'image_path' => 'category/Palm_Oil.png'
            ],
            [
                'name' => 'Glycerin',
                'image_path' => 'category/Glycerin.png'
            ],
            [
                'name' => 'Soap Noodles',
                'image_path' => 'category/Soap_Noodles.png'
            ],
            [
                'name' => 'Fatty Alcohol',
                'image_path' => 'category/Fatty_Alcohol.png'
            ],
            [
                'name' => 'Fatty Acid',
                'image_path' => 'category/Fatty_Acid.png'
            ],
            [
                'name' => 'Stearic Acid',
                'image_path' => 'category/Stearic_Acid.png'
            ],
            [
                'name' => 'Surfactant',
                'image_path' => 'category/Surfactant.png'
            ],
            [
                'name' => 'Bio Diesel',
                'image_path' => 'category/Bio_Diesel.png'
            ],
            [
                'name' => 'Petrochemicals',
                'image_path' => 'category/Petrochemicals.png'
            ]
        );

        for ($i = 0; $i < count($categories); $i++) {

            DB::table('category')->insert(array(
                'name' => $categories[$i]['name'],
                'image_path' => $categories[$i]['image_path'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));

        }
    }
}
