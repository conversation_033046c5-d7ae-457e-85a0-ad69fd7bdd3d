<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;

class DeliveryTermSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $terms = array(
            'FOB',
            'CFR',
            'CIF',
            'EXW',
            'FOB & CFR',
            'FOB & CIF'
        );

        for ($i = 0; $i < count($terms); $i++) {

            DB::table('delivery_term')->insert(array(
                'name' => $terms[$i],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));
        }
    }
}
