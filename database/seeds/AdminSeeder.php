<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Ramsey\Uuid\Uuid;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // $admin = array(
        //     [
        //         'name' => 'Admin',
        //         'email_id' => '<EMAIL>',
        //         'password' => Hash::make('@Mitul8828'),
        //     ]
        // );

        $admin = array(
            [
                'name' => 'MB_Admin',
                'email_id' => '<EMAIL>',
                'password' => Hash::make('mblionadmin@123'),
            ]
        );

        for ($i = 0; $i < count($admin); $i++) {
            $uuid4 = Uuid::uuid4();
            $uuid = $uuid4->toString();
            DB::table('admin')->insert([
                'uuid' => $uuid,
                'name' => $admin[$i]['name'],
                'email_id' => $admin[$i]['email_id'],
                'password' => $admin[$i]['password'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ]);
        }
    }
}
