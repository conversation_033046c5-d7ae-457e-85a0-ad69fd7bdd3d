<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = json_decode(file_get_contents("public/assets/json/country.json"), true);

        foreach ($data as $value) {
            DB::table('country')->insert(array(
                'country_name' => $value['name'],
                'country_code' => $value['dial_code'],
                'country_short_name' => $value['code'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));
        }
    }
}