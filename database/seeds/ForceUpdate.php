<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ForceUpdate extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = array(
            [
                'version' => 1,
                'version_code' => 1,
                'device_type' => \App\Utils\AppConstant::OS_ANDROID,
            ],
            [
                'version' => 1,
                'version_code' => 1,
                'device_type' => \App\Utils\AppConstant::OS_IOS,
            ]
        );

        for ($i = 0; $i < count($data); $i++) {
            DB::table('force_update_models')->insert([
                "version" => $data[$i]['version'],
                "version_code" => $data[$i]['version_code'],
                "device_type" => $data[$i]['device_type'],
                "created_at" => Carbon::now()->toDateTimeString(),
                "updated_at" => Carbon::now()->toDateTimeString()
            ]);
        }

    }
}
