<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;

class CreateCurrencyTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currencies = array(
            array('code' =>'USD' , 'name' => 'US Dollar', 'symbol' => '$' ),
            array('code' =>'MYR' , 'name' => 'Malaysian Ringgit', 'symbol' => 'RM' ),
            array('code' =>'EUR' , 'name' => 'Euro', 'symbol' => '€' ),
            array('code' =>'INR' , 'name' => 'Indian Rupee', 'symbol' => '₹' ),
            array('code' =>'RMB' , 'name' => 'Renminbi', 'symbol' => '¥' ),
        );

        for ($i = 0; $i < count($currencies); $i++) {

            DB::table('currency')->insert(array(
                'name' => $currencies[$i]['name'],
                'code' => $currencies[$i]['code'],
                'symbol' => $currencies[$i]['symbol'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));
        }
    }
}
