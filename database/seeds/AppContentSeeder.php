<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AppContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $type = array(
            '1',
            '2'
        );

        $names = array(
            'Privacy Policy',
            'Terms And Conditions',
        );

        $description = array(
            'MB Lion Oleochemicals built the MB Lion Oleochemicals app as a Freemium app. This SERVICE is provided by MB Lion Oleochemicals at no cost and is intended for use as is.',
            'This Service does not use these “cookies” explicitly. However, the app may use third party code and libraries that use “cookies” to collect information and improve their services. You have the option to either accept or refuse these cookies and know when a cookie is being sent to your device. If you choose to refuse our cookies, you may not be able to use some portions of this Service.

'
        );

        for ($i = 0; $i < count($type); $i++) {

            DB::table('app_content')->insert(array(
                'type' => $type[$i],
                'name' => $names[$i],
                'description' => $description[$i],
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ));
        }
    }
}
