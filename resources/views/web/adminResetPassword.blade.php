<html>
<head>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ secure_url('css/resetPassword.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ secure_url('css/common.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ secure_url('css/portal.css') }}" type="text/css">
    <title>Cashman & Wakefield</title>
</head>
<body>
<div class="container padd-top"> <!-- Container Start -->
    <div class="row">
        <div class="col-lg-64col-md-4 col-sm-4 col-lg-offset-4 col-md-offset-4 col-sm-offset-4 col-xs-12">
            <img src="{{ secure_url('images/logo/CW_Logo_Color.png') }}" class="theme-logo">
            <div class="title reset_title">Reset Password</div>
            <br>
            @if(Session::has('error'))
                <p class="alert {{ Session::get('alert-class', 'alert-danger') }}">{!! session('error') !!}</p>
            @endif
            <form method="POST" action="{{ secure_url('admin/update_reset_password') }}">
                {{ csrf_field() }}
                <div class="form-group">
                    <label>
                        Password <span class="required">*</span>
                    </label>
                    <input type="password" class="form-control" required id="password" value="{{ old('password') }}"
                           name="password" placeholder="Enter password">
                    <div class="password_note">
                        <strong>Note: </strong>
                        Password must be 8 characters, 1 uppercase, 1 lowercase, 1 numeric value, 1 special character
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        Confirm Password <span class="required">*</span>
                    </label>
                    <input type="password" class="form-control" required id="password" value="{{ old('re_password') }}"
                           name="password_confirmation" placeholder="Enter confirm password">
                </div>
                <div class="form-group">
                    <input type="submit" class="btn portal-btn" name="addProfile" value="Reset" id="addProfile">
                </div>

            </form>
        </div>
    </div>
</div>
</body>
</html>