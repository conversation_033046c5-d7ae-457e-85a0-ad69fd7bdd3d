<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    {{--    <link rel="icon" href="{{ url('images/favicon.png') }}" type="image/png">--}}
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!--    CSS   -->
    <link rel="stylesheet" href="{{ url('assets/bootstrap/dist/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url('assets/font-awesome/css/font-awesome.min.css') }}">
    <!-- Ionicons -->
    <link rel="stylesheet" href="{{ url('assets/Ionicons/css/ionicons.min.css') }}">
    <!-- Theme style -->
    <link rel="stylesheet" href="{{ url('assets/dist/css/AdminLTE.min.css') }}">
    <link rel="stylesheet" href="{{ url('assets/lightbox/lightbox.css') }}">


    <link rel="stylesheet" href="{{ url('css/customCss.css') }}">
    <link rel="stylesheet" href="{{ url('css/font.css') }}">
    <link rel="stylesheet" href="{{ url('css/lightbox.css') }}">
    <link rel="stylesheet" href="{{ url('css/w3.css') }}">

    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
    <title>{{ $data->name }}</title>
</head>
<body class="hold-transition skin-red sidebar-mini">
<div class="wrapper">
    <section class="content" style="padding: 0px !important;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 col-lg-offset-4 col-md-offset-4 col-sm-offset-4"
                     style="background-color:{{ ($data->theme != "") ? $data->theme->primary_color : \App\Utils\AppConstant::PRIMARY_COLOR }}; border: 1px solid {{ ($data->theme != "") ? $data->theme->primary_color : \App\Utils\AppConstant::PRIMARY_COLOR }}; padding:0;">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 graphics-basic">
                                @if(count($data->media) > 0)
                                    @if($data->media[0]->media_type == \App\Utils\AppConstant::CARD_MEDIA_TYPE_IMAGE)
                                        <a href="{{ $data->media[0]->media_url }}"
                                           data-lightbox="profilePic"><img
                                                    src="{{ $data->media[0]->media_url }}"
                                                    class="img-responsive  modalGraphics"></a>
                                    @elseif($data->media[0]->media_type == \App\Utils\AppConstant::CARD_MEDIA_TYPE_VIDEO)
                                        <video id="myVideo" class="modalGraphics" controls="true"
                                               style="margin-bottom: -10px ! important;" autoplay loop playsinline>
                                            <source src="{{ $data->media[0]->media_url }}"
                                                    type="video/mp4"/>
                                        </video>
                                    @else
                                        <a href="{{ url('images/logo.jpg') }}"
                                           data-lightbox="profilePic"><img
                                                    src="{{ url('images/logo.jpg') }}"
                                                    class="img-responsive  modalGraphics"></a>
                                    @endif
                                @else
                                    <a href="{{ url('images/logo.jpg') }}"
                                       data-lightbox="profilePic"><img
                                                src="{{ url('images/logo.jpg') }}"
                                                class="img-responsive  modalGraphics"></a>
                                @endif
                            </div>
                        </div>
                        <div class="row titleRow"
                             style=" background-color:{{ ($data->theme != "") ? $data->theme->secondary_color : \App\Utils\AppConstant::SECONDARY_COLOR }}; border: 1px solid {{ ($data->theme != "") ? $data->theme->secondary_color : \App\Utils\AppConstant::SECONDARY_COLOR }};">
                            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 cardContent titlebar">

                                <img class="img-responsive avatar img-circle"
                                     src="@if($data->user->profile_pic != "" || $data->user->profile_pic != null) {{ $data->user->profile_pic }} @else {{ url('images/avatar.png') }} @endif">

                                <h6 class="cardTitle heading-basic-20-top wrapping">
                                    {{ ($data->firstname != "" || $data->firstname != null) ? $data->firstname : "" }}
                                    {{ ($data->lastname != "" || $data->lastname != null) ? $data->lastname : "" }}
                                </h6>
                                <h6 class="cardTitle-semi-bold-title hack wrapping"
                                    style="font-family: poppins-regular;">
                                    @isset($data->title)
                                        {{ $data->title }}
                                    @endisset
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-bottom:2px;">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="cardDetails">
                                <div class="border-basic">
                                    @isset($data->company)
                                        <h6 class="heading-basic-10-top cardTitle-semi-bold wrapping">
                                            {{ $data->company }}
                                        </h6>
                                    @endisset
                                    <h6 class="heading-basic-top cardTitle wrapping"
                                        style="font-family: poppins-regular;">
                                        {{ ($data->street != "" || $data->street != null) ? $data->street : ""}}
                                    </h6>
                                    <h6 class="heading-basic-top cardTitle wrapping"
                                        style="font-family: poppins-regular;">
                                        {{ ($data->city != "" || $data->city != null) ? $data->city."," : "" }}
                                        {{ ($data->state != "" || $data->state != null) ? $data->state : "" }}
                                        {{ ($data->zipcode != "" || $data->zipcode != null) ? $data->zipcode : "" }}
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    @forelse($data->mobile_no as $mobile)
                        <div class="row contactRow">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="cardDetails">
                                    <div class="border-basic">

                                        <a class="tel_link" style="color: #ffffff;" target="_blank"
                                           href="tel: {{ $mobile->mobile_no }}">
                                            <img src="{{ url('images/vcardTest/icon_phone.png') }}"
                                                 class="imgIcons">
                                        </a>
                                        <h6 class="heading-basic-17-bottom cardTitle wrapping"
                                            style="padding-top:5px; pointer-events: none;">
                                            {{ $mobile->country_code .' '. substr($mobile->mobile_no, 0, 3).'-'.substr($mobile->mobile_no, 3, 3).'-'.substr($mobile->mobile_no,6)  }}
                                        </h6>

                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                    @endforelse
                    <div class="row" style="">
                        @forelse($data->email as $email)
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="cardDetails">
                                    <div class="border-basic">
                                        <img src="{{ url('images/vcardTest/icon_email.png') }}" class="imgIcons">
                                        <h6 class="heading-basic-17-bottom cardTitle-borders cardTitle wrapping"
                                            style="padding-top:5px;"><a
                                                    href="mailto: {{ $email->email }}">{{ $email->email }}</a>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        @empty
                        @endforelse
                    </div>
                    {{--@isset($data->web_address)--}}
                    @if(isset($data->web_address) && $data->web_address != '' && $data->web_address != null)
                        <div class="row" style="height: 34px;">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 switchItApp">
                                <div class="cardDetails">
                                    <div class="border-basic">
                                        <img src="{{ url('images/vcardTest/icon_web.png') }}" class="imgIcons">
                                        <h6 class="heading-basic-16-bottom cardTitle-borders cardTitle-medium wrapping"
                                            style="padding-top:5px;display: -webkit-box;">
                                            {{--<a href="{{ "http://" .preg_replace("/^http:/i", "https:", $data->web_address) }}"
                                               target="_blank">{{ "http://" .preg_replace("/^http:/i", "https:", $data->web_address) }}</a>--}}
                                            <a href="{{ "http://" . preg_replace("/^http:/i", "https:", $data->web_address) }}"
                                               target="_blank">{{ preg_replace("/^http:/i", "https:", $data->web_address) }}</a>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                    {{--@endisset--}}
                    <div class="row" style="margin-top:2px;">
                        @isset($data->social_url)
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="socialDetails">
                                    <div class="socialInfo">
                                        @foreach($data->social_url as $social)
                                            <div>
                                                @if($social->social->url_location == 1)
                                                    @php $link = $social->social->social_url.$social->url;  @endphp
                                                @else
                                                    @php $link = $social->url.$social->social->social_url;  @endphp
                                                @endif
                                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 socialLinks">
                                                    <img class="img-responsive"
                                                         src="{{ $social->social->icon }}"
                                                         style="float: left; padding: 10px; width: 50px;">
                                                    <h6 class="heading-social-basic-link wrapping">
                                                        <small>
                                                            <a type="text" target="_blank" name="FA"
                                                               href="{{ "https://".$link }}">{{ $link }}</a>
                                                        </small>
                                                    </h6>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endisset
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="text-center" style="padding-bottom: 15px;">
                                <input type="hidden" id="profileId" name="profileId" value="sa"/>
                                <h5 id="saveVcard" class="saveToPhone heading-basic"><a
                                            href="{{ url('/download_vcard/'.$data->card_uuid) }}">Save to Phone</a></h5>
                            </div>
                        </div>
                    </div>
                    <!--create your own cards section-->
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="itunesSection">
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        <div class="itunesBtn text-center">
                                            <h5 class="heading-basic">
                                                {{--<small>Create Your Own Cards! Download for Free.</small>--}}
                                            </h5>
                                            <a href="#" target="_blank">
                                                <img src="{{ url('images/vcardTest/cushwake_lower-graphic.png') }}"
                                                     style="width:80%" ;>
                                            </a>
                                            {{--<a href="https://itunes.apple.com/us/app/switchit-contact-sharing-network/id1125883020?mt=8"
                                               target="_blank"><img
                                                        src="{{ url('images/vcardTest/app-store.png') }}"
                                                        style="width:35%"
                                                        ;></a>
                                            <a href="https://play.google.com/store/apps/details?id=com.c7.android.switchit&hl=en"
                                               target="_blank"><img
                                                        src="{{ url('images/vcardTest/google-play-badge.png') }}"
                                                        style="width:35%" ;></a>--}}
                                            <h6 style="padding-top: 10px;">
                                                <small style="font-size:10px;">Copyright © {{ date('Y') }} Cushman &
                                                    Wakefield. All
                                                    rights reserved.
                                                </small>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>
<!--  Javascript   -->
<script src="{{ url('assets/jquery/dist/jquery.min.js') }}"></script>
<!-- jQuery UI 1.11.4 -->
<script src="{{ url('assets/jquery-ui/jquery-ui.min.js') }}"></script>
<!-- Bootstrap 3.3.7 -->
<script src="{{ url('assets/bootstrap/dist/js/bootstrap.min.js') }}"></script>
<script src="{{ url('assets/lightbox/lightbox.js') }}"></script>
<!-- Morris.js charts -->
<script type="text/javascript">
    $(document).ready(function () {

//$("#myVideo").prop('muted', false); //unmute
        $('.modal').on('hidden.bs.modal', function () {
            $('video').each(function () {
                this.pause();
            });
        })
        $('.modal').on('shown.bs.modal', function () {
            $('video').each(function () {
                this.play();
            });
        })
        $(document).bind('webkitfullscreenchange mozfullscreenchange fullscreenchange ', function (e) {
            var state = document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen;
            console.log(state)
            var event = state ? 'FullscreenOn' : 'FullscreenOff';
            console.log(event);

            if (event == "FullscreenOn") {

                $(".modalGraphics").css('object-fit', 'contain', 'important');
            }
            if (event == "FullscreenOff") {
                //alert('yo');
                $(".modalGraphics").css('object-fit', 'fill');
            }
        });
    });
</script>
</body>
</html>

