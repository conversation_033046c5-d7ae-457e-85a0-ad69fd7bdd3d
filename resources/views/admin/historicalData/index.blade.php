@extends('admin.layouts.master')

@section('title')
    Product List
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">@lang('labels.categories')</h5>
        </div>
        <table class="table datatable-basic table-bordered">
            <thead>
            <tr>
                <th>#</th>
                <th>#</th>
            </tr>
            </thead>
            <tbody>
            @foreach($data['products'] as $product)
                <tr>
                    <td class="srNO">{{ $loop->iteration }}</td>
                    <td><a href="javascript:void(0);">{{$product->name}}</a></td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter.js') }}"></script>
    <script>
        $(document).ready(function () {
            $('.datatable-basic').DataTable({
                "columnDefs": [
                    { "targets": [0,1,3], "searchable": false }
                ]
            });
        });
    </script>
@endsection