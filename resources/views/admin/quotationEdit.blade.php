@extends('admin.layouts.master')
@section('title')
    @lang('labels.editQuotation')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.editQuotation')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('product.QuotationUpdate', ['id' => $data['quotations']->id]) }}" id="editQoutation" method="post">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id" value="{{ $data['quotations']->id }}"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.email')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Email" name="email" value="{{ $data['quotations']->email_id }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.productName')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Destination Port" name="ProductName" value="{{ $data['quotations']-> product_name }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.destinationPort')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Destination Port" name="destinationPort" value="{{ $data['quotations']-> destination_port }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.country')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Country" name="country" value="{{ $data['quotations']-> country }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.quantity')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Quantity" name="quantity" value="{{ $data['quotations']-> quantity }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.message')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Message" name="message" value="{{ $data['quotations']-> message }}">
                    </div>

                    <div class="text-right">
                        <a href="{{ route('admin.categories') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.submit') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{--@include('admin.movement.floating_button')--}}
    <script>
        let rules = {
            email: {
                required: true,
                email: true,
            },
            ProductName: {
                required: true,
                minlength: 3,
                maxlength: 100,
                lettersAndSpace: true
            },
            destinationPort : {
                required: true,
                minlength: 2,
                maxlength: 5,
            },
            country : {
                required: true,
                minlength: 2,
                maxlength: 50,
            },
            quantity : {
                required: true,
            },
            message:{
                required: true,
                minlength: 3,
                maxlength: 150,
            }


        };
        validateForm('#editQoutation', rules);
    </script>
@endsection