@extends('admin.layouts.master')

@section('title')
    Terms and Conditions
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
@endsection


@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/editors/summernote/summernote.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/uploaders/fileinput.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-12" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.editTermsAndConditions')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('appContent.update', ['id' => $data['terms']->id]) }}" id="terms" method="post" enctype="multipart/form-data">
                    @method('PUT')
                    @csrf
                    <div class="form-group" id="sum">
                        <label class="control-label text-semibold">@lang('labels.description')<span
                                    class="text-danger"> *</span></label>
                        <div class="multi-select-full">
                            <textarea class="summernote" name="description" id="description" style="width: 100%">{{$data['terms']->description}}</textarea>
                        </div>
                    </div>
                    <div class="text-right">
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.submit') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="{{ url('assets/js/page/fileinput.js') }}"></script>
    <script src="{{ url('assets/js/page/summernote_init.js') }}"></script>
    <script>
        summernoteInit();
        $('#description').summernote(
            {
                toolbar: [
                    ["style", ["style"]],
                    ["font", ["bold", "underline", "clear"]],
                    ["color", ["color"]],
                    ["para", ["ul", "ol", "paragraph"]],
                    ['Insert', ['link']],
                ]
            }
        );
        let rules = {
            description: {
                required: true,
                minlength: 10,
            }
        };
        validateForm('#terms', rules);
    </script>
@endsection