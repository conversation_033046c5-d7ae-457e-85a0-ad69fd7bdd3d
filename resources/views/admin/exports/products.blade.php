<table>
    <thead>
    <tr>
        <th>Name</th>
        <th>Category</th>
        <th>Packing</th>
        <th>Unit</th>
        <th>Delivery Term</th>
        <th>Price</th>
        <th>HS Code</th>
        <th>CAS No.</th>
    </tr>
    </thead>
    <tbody>
    @foreach($products as $product)
        <tr>
            <td>{{ $product->name }}</td>
            <td>{{ $product->category->name }}</td>
            <td>{{ $product->packing->packing_type }}</td>
            <td>{{ $product->unit->name }}</td>
            <td>{{ $product->deliveryTerm->name }}</td>
            <td>{{ $product->productPrice->max_price ? " {$product->currency->symbol} {$product->productPrice->min_price} - {$product->productPrice->max_price} " : "{$product->currency->symbol} {$product->productPrice->min_price} "}}</td>
            <td>{{ $product->hs_code }}</td>
            <td>{{ $product->cas_number }}</td>
        </tr>
    @endforeach
    </tbody>
</table>