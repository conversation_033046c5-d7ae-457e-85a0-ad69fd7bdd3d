@extends('admin.layouts.master')
@section('title')
    @lang('labels.addProduct')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_multiselect.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_select.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.addProductPrice')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('category.addProductPrice', ['id' => $data['id']]) }}" id="addPrice" method="post">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.min_price')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control number" placeholder="Min Price" name="min_price">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.max_price')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control number" placeholder="Max Price" name="max_price">
                    </div>

                    <div class="text-right">
                        <a href="{{ route('category.categories') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.add_price') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="{{ asset('assets/js/jquery-alphanumeric-validation/jquery.alphanum.js') }}" type="text/javascript"></script>
    <script>
        $( document ).ready(function() {
            let rules = {
                min_price: {
                    required: true
                },
                max_price: {
                    required: true
                }
            };
            validateForm('#addPrice', rules);

            $('.number').numeric({
                allowMinus: false,
                allowThouSep: false,
                allowDecSep: false
            });
        });
    </script>
@endsection