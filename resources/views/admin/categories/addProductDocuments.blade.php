@extends('admin.layouts.master')
@section('title')
    @lang('labels.addProduct')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_multiselect.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_select.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.addProductDocuments')</h5>
                <small class="note">Note: Documents must be of PDF format.</small>
            </div>
            <div class="panel-body">
                <form action="{{ route('category.addProductDoc', ['id' => $data['id'], 'uuid' => $data['uuid']]) }}" id="docUpload" method="post" enctype="multipart/form-data">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id"/>
                    <input type="hidden" name="category_id" value="{{$data['category_id']}}"/>
                    <div class="form-group">
                        <input type="file" name="specifications" class="file" accept="application/pdf" id="specifications">
                        <div class="input-group col-xs-12">
                            <span class="input-group-addon"><i class="glyphicon glyphicon-file"></i></span>
                            <input type="text" class="form-control input-lg" disabled placeholder="Select Specifications">
                            <span class="input-group-btn">
        <button class="browse btn btn-color input-lg" type="button"><i class="glyphicon glyphicon-file"></i> Browse</button>
      </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="file" name="tds" class="file" accept="application/pdf">
                        <div class="input-group col-xs-12">
                            <span class="input-group-addon"><i class="glyphicon glyphicon-file"></i></span>
                            <input type="text" class="form-control input-lg" disabled placeholder="Select TDS-MSDS">
                            <span class="input-group-btn">
        <button class="browse btn btn-color input-lg" type="button"><i class="glyphicon glyphicon-file"></i> Browse</button>
      </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="file" name="coa" class="file" accept="application/pdf">
                        <div class="input-group col-xs-12">
                            <span class="input-group-addon"><i class="glyphicon glyphicon-file"></i></span>
                            <input type="text" class="form-control input-lg" disabled placeholder="Select COA">
                            <span class="input-group-btn">
        <button class="browse btn btn-color input-lg" type="button"><i class="glyphicon glyphicon-file"></i> Browse</button>
      </span>
                        </div>
                    </div>

                    <div class="text-right">
                        <a href="{{ route('category.product', ['id' => $data['category_id']]) }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.submit') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        /*$('#ff2').change(
            function () {
                var fileExtension = 'pdf';
                if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
                    alert("Only '.pdf' formats are allowed.");
                    return false; }
            });*/

        $(document).on('click', '.browse', function(){
            var file = $(this).parent().parent().parent().find('.file');
            file.trigger('click');
        });
        $(document).on('change', '.file', function(){
            $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
        });
        /*$('#specifications').change(function() {
            alert(this.files[0].size + " bytes");
        });*/

        $.validator.addMethod('filesize', function (value, element, param) {
            return this.optional(element) || (element.files[0].size <= (1048576 * param));
        }, 'File size must be less than {0} MB');

        let rules = {
            specifications: {
                required:true,
                extension: "pdf",
                filesize: 2
            },
            tds: {
                required:true,
                extension: "pdf",
                filesize: 2
            },
            coa: {
                required:true,
                extension: "pdf",
                filesize: 2
            },
        };
        let message = {
            specifications:{
                extension:"select valid input file format",
                filesize: "File size should not be more than 2MB"
            }
        };
        validateForm('#docUpload', rules, message);
    </script>
@endsection