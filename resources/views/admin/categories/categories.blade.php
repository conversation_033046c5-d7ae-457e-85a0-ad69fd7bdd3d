@extends('admin.layouts.master')

@section('title')
    Categories
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">@lang('labels.categories')</h5>
        </div>
        <table class="table datatable-basic table-bordered">
            <thead>
            <tr>
                <th>@lang('labels.SrNo')</th>
                <th>@lang('labels.categoryImage')</th>
                <th>@lang('labels.categoryName')</th>
                {{--<th>@lang('labels.status')</th>--}}
                <th>@lang('labels.actions')</th>
            </tr>
            </thead>
            <tbody>
            @foreach($data['categories'] as $category)
                <tr>
                    <td class="srNO">{{ $loop->iteration }}</td>
                    <td class="product-icon-row" align="center">
                        <img src="{{ $category->image_path }}" alt="product-img" class="img-responsive" width="50">
                    </td>
                    <td><a href="{{ route('category.product', ['id' => $category->id])}}">{{ $category->name }}</a></td>
                    {{--<td>
                        <span class="label {{$category->status === \App\Utils\AppConstant::STATUS_ACTIVE ? 'label-success' : 'label-danger'}}">
                            {{$category->status === \App\Utils\AppConstant::STATUS_ACTIVE ? __('labels.active') : __('labels.inactive')}}
                        </span>
                    </td>--}}
                    <td>
                        <ul class="icons-list">
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <i class="icon-menu9"></i>
                                </a>
                                {{--<ul class="dropdown-menu dropdown-menu-right">
                                    --}}{{--<li><a href="{{ route('category.edit', ['id' => $category->id]) }}"><i
                                                    class="icon-pencil6"></i> @lang('labels.edit')</a></li>--}}{{--
                                    @if($category->status === \App\Utils\AppConstant::STATUS_ACTIVE)
                                        <li>
                                            <a href="{{ route('category.inactive', ['id' => $category->id]) }}"
                                               data-url=""><i
                                                        class="icon-cross2"></i> @lang('labels.inactive')</a></li>
                                    @else
                                        <li><a href="{{ route('category.active', ['id' => $category->id]) }}"><i
                                                        class="icon-checkmark3"></i> @lang('labels.active')</a></li>
                                    @endif
                                </ul>--}}
                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li>
                                        <a href="{{ route('category.edit', ['id' => $category->id]) }}"
                                           data-url=""><i
                                                    class="icon-pencil6"></i> @lang('labels.edit')</a>
                                    </li>
                                    <li>
                                        <a href="{{ route('category.editPrice', ['id' => $category->id])}}"
                                           data-url=""><i
                                                    class="icon-coin-dollar"></i> @lang('labels.editPrices')</a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </td>


                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
    {{--@include('admin.categories.floating_button')--}}
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter.js') }}"></script>
    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});
        function deleteRecord()
        {
            $('.confirm').click(function () {
                let url = $(this).attr('data-url');
                let title = $(this).attr('data-title');
                let message = $(this).attr('data-message');
                alertPopup(url, title, message);
            });
        }
        $(document).ready(function () {
            $('.datatable-basic').DataTable({
                "columnDefs": [
                    { "targets": [0,1,3], "searchable": false }
                ]
            });
            deleteRecord();
        });
    </script>
@endsection