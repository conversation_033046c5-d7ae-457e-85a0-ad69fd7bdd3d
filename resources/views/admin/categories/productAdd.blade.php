@extends('admin.layouts.master')
@section('title')
    @lang('labels.addProduct')
@endsection
@section('js')
    <script src="{{ asset('assets/js/jquery-alphanumeric-validation/jquery.alphanum.js') }}"
            type="text/javascript"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/forms/styling/uniform.min.js') }}"></script>

    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_multiselect.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_select.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.addProduct')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('category.addProduct', ['id' => $data['category_id']]) }}" id="addProduct"
                      method="post">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.name')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Product Name" name="product_name">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.port')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Loading Port" name="loading_port">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.packingName')<span class="text-danger"> *</span></label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="packing_type">
                            @foreach($data['packings'] as $packing)
                                <option value="{{$packing->id}}">{{$packing->packing_type}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.unit')<span
                                    class="text-danger"> *</span></label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="unit_name">
                            @foreach($data['units'] as $unit)
                                <option value="{{$unit->id}}">{{$unit->name}}</option>
                            @endforeach
                        </select>
                    </div>


                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.deliveryTerm')<span
                                    class="text-danger"> *</span></label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="delivery_term">
                            @foreach($data['terms'] as $term)
                                <option value="{{$term->id}}">{{$term->name}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.currency')<span
                                    class="text-danger"> *</span></label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="currency">
                            @foreach($data['currencies'] as $currency)
                                <option value="{{$currency->id}}">{{$currency->name}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.hs_code')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="HS Code" name="hs_code">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.cas_no')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="CAS No." name="cas_number">
                    </div>

                    <div class="form-group">
                        <div class="row">
                            <div class="col-12" style="padding-left: 10px">
                                <label class="control-label text-semibold">@lang('labels.price')<span
                                            class="text-danger"> *</span></label>
                                <p><small class="text-danger">(Product price is required while adding product for the first time.)</small></p>
                            </div>
                        </div>
                        <label class="radio-inline">
                            <div class="choice">
                                <input type="radio" value="1" name="price" class="styled" id="single_price_radio">
                            </div>
                            @lang('labels.single_price')
                        </label>
                        <label class="radio-inline">
                            <div class="choice">
                                <input type="radio" value="2" name="price" class="styled" id="double_price_radio">
                            </div>
                            @lang('labels.price_range')
                        </label>

                        <div class="price" id="single">
                            <input type="text" class="form-control" placeholder="Price" name="min_price_single"
                                   id="min_price_single">
                        </div>

                        <div class="price" id="range">
                            <input type="text" class="form-control" placeholder="Min price" name="min_price"
                                   id="min_price">
                            <input type="text" class="form-control" placeholder="Max price" name="max_price"
                                   id="max_price">
                        </div>
                    </div>

                    <div class="text-right">
                        <a href="{{ route('category.product',['id' => $data['category_id']]) }}"
                           class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.addProduct') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script>
        $(document).ready(function () {
            $(".styled").uniform({
                radioClass: 'choice'
            });

            let rules = {
                product_name: {
                    required: true,
                    minlength: 3,
                    maxlength: 50
                },
                hs_code: {
                    required: true,
                },
                cas_number: {
                    required: true
                },
                loading_port: {
                    required: true,
                    minlength: 3
                }
            };
            validateForm('#addProduct', rules);

            $("div.price").hide();
            $("input[name$='price']").click(function () {
                var value = $(this).val ();
                console.log(value);
                if (value == 1) {
                    $("#single").show('slow');
                    $("#range").hide('slow');
                    $('#min_price').rules('remove');
                    $('#max_price').rules('remove');
                    $('#max_price-error').remove();
                    $('#min_price-error').remove();
                    $('#min_price_single').rules('add', {
                        required:true
                    });
                } else {
                    if (value == 2) {
                        $("#single").hide('slow');
                        $("#range").show('slow');
                        $('#min_price_single').rules('remove');
                        $('#min_price_single-error').remove();
                        $('#min_price').rules('add', {
                            required:true
                        });
                        $('#max_price').rules('add', {
                            required: true,
                            gt: 'input[id="min_price"]'
                        });
                    }
                }
            });
            $('#min_price_single').numeric({
                allowMinus: false,
                allowThouSep: false,
                min: 1,
                max: 9000000000
            });
            $('#min_price').numeric({
                allowMinus: false,
                allowThouSep: false,
                min: 1,
                max: 9000000000
            });
            $('#max_price').numeric({
                allowMinus: false,
                allowThouSep: false,
                min: 1,
                max: 9000000000,
            });
        });
    </script>
@endsection