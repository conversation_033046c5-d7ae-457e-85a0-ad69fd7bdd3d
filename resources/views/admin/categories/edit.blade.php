@extends('admin.layouts.master')
@section('title')
    @lang('labels.editCategory')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.editCategory')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('category.update', ['id' => $data['categories']->id]) }}" id="editCategory" method="post" enctype="multipart/form-data">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id" value="{{ $data['categories']->id }}"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.name')<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Name" name="category_name" value="{{ $data['categories']->name }}">
                    </div>

                    <div class="form-group">
                        <input type="file" name="category_image" class="file">
                        <div class="input-group col-xs-12">
                            <span class="input-group-addon"><i class="glyphicon glyphicon-camera"></i></span>
                            <input type="text" class="form-control input-lg" disabled placeholder="Select Category Image">
                            <span class="input-group-btn">
        <button class="browse btn btn-color input-lg" type="button"><i class="glyphicon glyphicon-camera"></i> Browse</button>
      </span>
                        </div>
                    </div>
                    <div class="text-right">
                        <a href="{{ route('category.categories') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.submit') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        $(document).on('click', '.browse', function(){
            var file = $(this).parent().parent().parent().find('.file');
            file.trigger('click');
        });
        $(document).on('change', '.file', function(){
            $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
        });

        let rules = {
            category_name: {
                required: true,
                minlength: 3,
                maxlength: 50,
                lettersAndSpace: true,
            },
            /*category_image : {
                required: true
            }*/
        };
        let message = {
            name: {
                remote: '{{ __('messages.CategoryAlreadyUse') }}'
            },
            category_image : {
                remote: '{{ __('messages.CategoryImageRequired') }}'
            }
        };
        validateForm('#editCategory', rules, message);
    </script>
@endsection