@extends('admin.layouts.master')
@section('title')
    @lang('labels.addMessage')
@endsection
@section('css')

@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/editors/summernote/summernote.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/uploaders/fileinput.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/forms/styling/uniform.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_multiselect.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_select.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.addMessage')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('message.addMessage') }}" id="addMessage" method="post">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.msgTitle')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Message Title" name="title">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.messageSubTitle')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Message subtitle" name="subtitle">
                    </div>

                    <div class="checkbox">
                        <label class="general_category">
                            <input type="checkbox" class="styled" name="general_category">
                            General Message
                        </label>
                    </div>
                    <div class="form-group category-group">
                        <label class="control-label text-semibold">@lang('labels.msgCategory')</label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="category_id"
                                aria-invalid="false">
                            <option value="">--Select Category--</option>
                            @foreach($data['category'] as $category)
                                <option value="{{$loop->iteration}}">{{$category->name}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group" id="sum">
                        <label class="control-label text-semibold">@lang('labels.msgBody')<span
                                    class="text-danger"> *</span></label>
                        <textarea class="summernote form-control msg-textarea" name="body" id="description"
                                  style="width: 100%" placeholder="Message Body"></textarea>
                    </div>

                    <div class="text-right">
                        <a href="{{ route('message.messages') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.addMessage') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="{{ url('assets/js/page/fileinput.js') }}"></script>
    <script src="{{ url('assets/js/page/summernote_init.js') }}"></script>
    <script>
        $(".styled, .multiselect-container input").uniform({
            radioClass: 'choice'
        });


        $("input[name$='general_category']").click(function () {
            let value = $(this).prop('checked');
            if (value === true) {
                $(".category-group").hide('slow')
            } else {
                $(".category-group").show('slow')
            }
        });

        let rules = {
            title: {
                required: true,
                minlength: 3,
                maxlength: 50
            },
            subtitle: {
                required: true,
                minlength: 3,
                maxlength: 50
            },
            body: {
                required: true,
                minlength: 10
            }
        };
        validateForm('#addMessage', rules);
    </script>
    <script>
        var trap = false;

        var TableButton = function (context) {
            var ui = $.summernote.ui;

            // create button
            var button = ui.button({
                contents: '<i class="fa fa-child"/>Price Table',
                tooltip: 'Insert template table',
                click: function () {
                    // invoke insertText method with 'hello' on editor module.

                    var tableCode = '<table class="roundedCorners" style="font-size: 16px; font-family: Verdana; border-collapse: separate;"><tbody style="font-size: 1em;"><tr style="font-size: 1em;"><th style="font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;">Product</th><th style="font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;">Price</th></tr><tr style="font-size: 1em;"><td style="font-size: 1em; border: 1px solid darkorange; padding: 5px;"><br></td><td style="font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;"></td></tr><tr><td style="font-size: 1em; border: 1px solid darkorange; padding: 5px;"><br></td><td style="font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;"><br></td></tr><tr style="font-size: 1em;"><td style="font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;"></td><td style="font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;"><br></td></tr></tbody></table></div><br>';

                    $('#description').summernote('pasteHTML', tableCode);
                }
            });

            return button.render();   // return button as jquery object
        }
        summernoteInit();
        $('#description').summernote(
            {
                toolbar: [
                    ["style", ["style"]],
                    ['TableButton', ['TableButton']],
                    ["font", ["bold", "underline", "clear"]],
                    ["color", ["color"]],
                    ["para", ["ul", "ol", "paragraph"]],
                    ['Insert', ['link']],
                    // ['table', ['table']],
                ],
                disableResizeEditor: true,
                buttons: {
                    TableButton: TableButton
                },
            },
        );
    </script>
@endsection