@extends('admin.layouts.master')

@section('title')
    Products
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
    <style>
        table .datatable .dataTable .no-footer .fixedHeader-floating {
            top: 0;
            width: 100% !important;
            display: block;
            overflow-x: auto;
        }
        .btn-color:focus {
            color: #ffffff
        }
        .icon-list:before {
            content: none
        }
        .price-col {
            min-width: 120px
        }
        .overflow{
            overflow-y: hidden;
        }
        .table>thead:first-child>tr:first-child>th {
            border-top: 1px solid #bbb;
        }
    </style>
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/forms/styling/uniform.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title">@lang('labels.Filter')</h5>
        </div>
        <div class="panel-body">
            <form action="{{route('product.data', ['id' => $data['category_id']])}}" method="post" id="export_products">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="display-block text-semibold">@lang('labels.status')</label>
                            <label class="radio-inline">
                                <input type="radio" value="0" name="status" class="styled" checked="checked">
                                @lang('labels.all')
                            </label>
                            <label class="radio-inline">
                                <input type="radio" value="1" name="status" class="styled">
                                @lang('labels.active')
                            </label>
                            <label class="radio-inline">
                                <input type="radio" value="2" name="status" class="styled">
                                @lang('labels.inactive')
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6">
                            </div>

                            <div class="col-md-6">
                                <div class="form-group" style="padding-left: 46px">
                                    <label class="control-label text-bold">@lang('labels.format')</label>
                                    <select class="bootstrap-select form-control editSelect" data-width="100%"
                                            name="format_id" id="format">
                                        <option value="0">--Select Format--</option>
                                        <option value="1">CSV</option>
                                        <option value="2">XLSX</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">

                    </div>
                </div>
            </form>
            <div class="text-right">
                <button type="submit" class="btn btn-color btn-font-color" id="export_btn">@lang('buttons.export')
                    <i
                            class="icon-arrow-right14 position-right"></i></button>
                {{--<a href="{{ route('products.export', ['id' => $data['category_id']]) }}"
                   class="btn btn-primary btn-color">
                    <i class="position-right"></i> @lang('buttons.export')
                </a>--}}
                <button id="filter" class="btn btn-primary btn-color">@lang('labels.Filter') <i
                            class="icon-filter3 position-right"></i></button>
            </div>
        </div>
    </div>
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">
            {{$data['category_name']->name}} @lang('labels.products')
            </h5>
        </div>
        <div class="panel-body overflow">
            <table class="table datatable-basic table-bordered" width="100%">
                <thead>
                <tr>
                    <th class="sr_no">@lang('labels.SrNo')</th>
                    <th>@lang('labels.name')</th>
                    {{--<th>@lang('labels.category')</th>--}}
                    <th>@lang('labels.packing')</th>
                    <th>@lang('labels.unit')</th>
                    <th>@lang('labels.deliveryTerm')</th>
                    <th>@lang('labels.port')</th>
                    <th class="price-col">@lang('labels.price')</th>
                    <th>@lang('labels.hs_code')</th>
                    <th>@lang('labels.cas_no')</th>
                    <th>@lang('labels.marquee')</th>
                    <th>@lang('labels.status')</th>
                    <th>@lang('labels.actions')</th>
                </tr>
                </thead>
                <tbody>
                @foreach($data['products'] as $product)
                    <tr>
                        <td class="srNO">{{ $loop->iteration }}</td>
                        <td>{{$product->name}}</td>
                        {{--<td>{{$product->category->name}}</td>--}}
                        <td>{{$product->packing->packing_type}}</td>
                        <td>{{$product->unit->name}}</td>
                        <td>{{$product->deliveryTerm->name}}</td>
                        <td>{{$product->loading_port}}</td>
                        {{--@if($product->productPrice->status == \App\Utils\AppConstant::STATUS_ACTIVE)--}}
                        <td>
                            @if(!$product->productPrice->max_price)
                                {{$product->currency->symbol}} {{$product->productPrice->min_price or 'not available'}}
                            @else
                                {{$product->currency->symbol}} {{$product->productPrice->min_price}} {{'-'}} {{$product->productPrice->max_price}}
                            @endif
                        </td>
                        {{--@endif--}}
                        <td>{{$product->hs_code}}</td>
                        <td>{{$product->cas_number}}</td>
                        <td>
                        <span class="label {{$product->marquee === \App\Utils\AppConstant::STATUS_ACTIVE ? 'label-success' : 'label-danger'}}">
                            {{$product->marquee === \App\Utils\AppConstant::STATUS_ACTIVE ? __('labels.show') : __('labels.hide')}}
                        </span>
                        </td>
                        <td>
                        <span class="label {{$product->status === \App\Utils\AppConstant::STATUS_ACTIVE ? 'label-success' : 'label-danger'}}">
                            {{$product->status === \App\Utils\AppConstant::STATUS_ACTIVE ? __('labels.active') : __('labels.inactive')}}
                        </span>
                        </td>
                        <td>
                            <ul class="icons-list">
                                <li class="dropdown">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        <i class="icon-menu9"></i>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        <li><a href="{{ route('category.editProduct', ['id' => $product->id]) }}"><i
                                                        class="icon-pencil6"></i> @lang('labels.edit')</a></li>
                                        @if($product->status === \App\Utils\AppConstant::STATUS_ACTIVE)
                                            <li>
                                                <a href="{{ route('categoryProduct.inactive', ['id' => $product->id]) }}"
                                                   data-url=""><i
                                                            class="icon-cross2"></i> @lang('labels.inactive')</a></li>
                                        @else
                                            <li><a href="{{ route('categoryProduct.active', ['id' => $product->id]) }}"><i
                                                            class="icon-checkmark3"></i> @lang('labels.active')</a></li>
                                        @endif

                                        @if($product->marquee === \App\Utils\AppConstant::STATUS_ACTIVE)
                                            <li>
                                                <a href="{{ route('categoryProduct.activeMarquee', ['id' => $product->id]) }}"
                                                   data-url=""><i
                                                            class="icon-cross2"></i> @lang('labels.marqueeHide')</a></li>
                                        @else
                                            <li>
                                                <a href="{{ route('categoryProduct.inactiveMarquee', ['id' => $product->id]) }}"><i
                                                            class="icon-checkmark3"></i> @lang('labels.marqueeShow')</a>
                                            </li>
                                        @endif

                                        {{--<li><a href="{{ route('category.productPrice', ['id' => $product->id]) }}"><i
                                                        class="icon-coin-dollar"></i> @lang('labels.addPrice')</a></li>--}}

                                        <li>
                                            <a href="{{ route('category.addProductDocuments', ['id' => $product->id, 'uuid' => $product->uuid, 'category_id' => $data['category_id']]) }}"><i
                                                        class="icon-folder-open"></i> @lang('labels.addDocuments')</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @include('admin.categories.floating_button_product')
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter.js') }}"></script>
    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});
        $(document).ready(function () {
            $('.datatable-basic').DataTable(
                {
                    /*to removing the sorting arrows in the Data Table pass the column no to the array below*/
                    "aoColumnDefs": [{"bSortable": false, "aTargets": [1, 2, 3, 4, 5, 6, 8, 9]}],
                    "columnDefs": [
                        { "targets": [9,10,11], "searchable": false }
                    ],
                    stateSave: true

                    // "scrollX": true,
                    // responsive: true,
                }
            );
            $('.dataTables_scrollBody').scroll(function(){
                $('.fixedHeader-floating').scrollLeft($(this).scrollLeft());
            });
            /*$(window).bind('resize', function () {
                oTable.fnAdjustColumnSizing();
            });
            jQuery('.dataTable').wrap('');*/
            $(".styled").uniform({
                radioClass: 'choice'
            });

            $('#filter').click(function () {
                filter('{{ route('product.filter') }}', {
                    'status': $('input[name="status"]:checked').val(),
                    'category_id': '{{$data['category_id']}}'
                });
            });

            $('#export_btn').click(function () {
                $('#export_products').submit();
            });
        });
    </script>
@endsection