@extends('admin.layouts.master')

@section('title')
    User Feedback
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection

@section('content')
    {{--{{dd($data['feedbacks'])}}--}}
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">@lang('labels.userFeedback')</h5>
        </div>
        <table class="table datatable-basic table-bordered" style="width: 100%">
            <thead>
            <tr>
                <th class="srNO">@lang('labels.SrNo')</th>
                <th>@lang('labels.email')</th>
                <th>@lang('labels.message')</th>
                <th>@lang('labels.date')</th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <div id="show_feedback" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary bg-header-master">
                    <h1 class="modal-title text-center">User Feedback</h1>
                    <small>From: <span class="user_email"></span></small>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12 col-md-12 col-lg-12">
                            <p class="feedback_here"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-color btn-font-color"
                            data-dismiss="modal">@lang('buttons.close')</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/moment/moment.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/plugins/tables/datatables/DatatableInit.js') }}"></script>

    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});

        function deleteRecord() {
            $('.confirm').click(function () {
                let url = $(this).attr('data-url');
                let title = $(this).attr('data-title');
                let message = $(this).attr('data-message');
                alertPopup(url, title, message);
            });
        }

        function messagePopup() {
            $(document).on('click','.openFeedback', function () {
                $('#show_feedback').modal('show');
                let id = $(this).attr('data-value');
                axios({
                    method: 'GET',
                    url: '{{ url('admin/feedback/feedbackPreview') }}/' + id,
                }).then(function (response) {
                    if (response.data) {
                        $('#show_feedback .feedback_here').html(response.data.message);
                        $('#show_feedback .user_email').html(response.data.email);
                        $('#show_feedback').modal('show');
                    } else {
                        new PNotify({
                            text: '{{ __('Record Not Found') }}',
                            addclass: 'top-error bg-danger'
                        });
                    }
                }).catch(function (error) {
                    new PNotify({
                        text: '{{ __('Temporary server error. Try again.') }}',
                        addclass: 'top-error bg-danger'
                    });
                });
            });
        }

        $(document).ready(function () {

            datatable();

            function datatable() {
                let route = "{{ route('feedback_fetch') }}";
                let data = function (data) {
                    let status = $("input[name='status']:checked").val();
                    data.status = status;
                };

                let columns = [
                        {
                            "data": null,
                            render: function (data, type, row, meta) {
                                return serialNo(meta);
                            }
                        },
                        {data: 'email_id'},
                        {data: 'message'},
                        {data: 'created_at'},
                    ]
                ;
                dt = new DatatableInit(route, data, columns);
                return dt;
            }

            messagePopup();
            $('.datatable-basic').DataTable();
            /*$(".styled").uniform({
                radioClass: 'choice'
            });*/
            deleteRecord();
            $(".timezone").each(function (i, item) {
                $(item).text(moment.unix($(item).data('time')).format("DD/MM/YYYY hh:mm A"));
            });
        });

    </script>

@endsection