@extends('admin.layouts.master')
@section('title')
    @lang('labels.changeAdminPassword')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.changeAdminPassword')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('user.ChangeAdminPassword', ['id' => $admin->uuid]) }}" id="changeAdminPassword" method="post">
                    @method('PUT')
                    @csrf
                    <input type="hidden" name="id" value=""/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.currentPass')<span class="text-danger"> *</span></label>
                        <input type="password" class="form-control" placeholder="Current Password" name="current_password" value="">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.newPass')<span class="text-danger"> *</span></label>
                        <input type="password" class="form-control" id="new_password" placeholder="New Password" name="new_password" value="">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.confirmNewPass')<span class="text-danger"> *</span></label>
                        <input type="password" class="form-control" placeholder="Confirm New Password" name="confirm_new_password" value="">
                    </div>

                    <div class="text-right">
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.changeAdminPass') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{--@include('admin.movement.floating_button')--}}
    <script>
        let rules = {
            current_password: {
                required: true,
            },
            new_password : {
                required: true,
                minlength: 8,
                maxlength : 50
            },
            confirm_new_password : {
                required: true,
                equalTo: "#new_password"
            }

        };
        validateForm('#changeAdminPassword', rules);
    </script>
@endsection