@extends('admin.layouts.master')
@section('title')
    @lang('labels.pushNotifications')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_multiselect.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/selects/bootstrap_select.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-6 col-md-offset-3" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.sendPushNotification')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('message.sendPushNotification') }}" id="pushNotifications" method="post">
                    @csrf
                    <input type="hidden" name="id"/>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.notificationCategory')<span class="text-danger"> *</span></label>
                        <select class="bootstrap-select form-control editSelect" data-width="100%" name="category" aria-invalid="false">
                            <option value="">--Select Category--</option>
                            @foreach($data['category'] as $category)
                            <option value="{{$loop->iteration}}">{{$category->name}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.notificationContent')<span class="text-danger"> *</span></label>
                        <textarea type="text" class="form-control msg-textarea" placeholder="Notification Text" name="notification_text" ></textarea>
                    </div>

                    <div class="text-right">
                        <a href="{{ route('message.messages') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.send') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        let rules = {
            title: {
                required: true,
                minlength: 3,
                maxlength: 50
            },
            body:{
                required: true,
                minlength: 10,
                maxlength: 191
            }
        };
        validateForm('#addMessage', rules);
    </script>
@endsection