@extends('admin.layouts.master')
@section('title')
    @lang('labels.editUser')
@endsection
@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/editors/summernote/summernote.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/uploaders/fileinput.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/jquery-validation.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/validation/validate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection
@section('content')
    <div class="col-md-8 col-md-offset-2" style="margin-top: 4% ">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">@lang('labels.editUser')</h5>
            </div>
            <div class="panel-body">
                <form action="{{ route('user.update', ['id' => $data['users']->uuid]) }}" id="edituser" method="post" enctype="multipart/form-data">
                    @method('PUT')
                    @csrf
                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.name')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Name" name="fullname"
                               value="{{ $data['users']->fullname }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.email')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Email" name="email_id"
                               value="{{ $data['users']->email_id }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.countryCode')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Country Code" name="country_code"
                               value="{{ $data['users']->country_code }}">
                    </div>


                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.mobile')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Mobile" name="mobile_no"
                               value="{{ $data['users']->mobile_no }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.company')<span
                                    class="text-danger"> *</span></label>
                        <input type="text" class="form-control" placeholder="Company" name="company"
                               value="{{ $data['users']->company_name }}">
                    </div>

                    <div class="form-group">
                        <label class="control-label text-semibold">@lang('labels.profilePicture')<span
                                    class="text-danger"> *</span></label>
                        <input type="file" class="file-input" data-show-caption="false" name="profilePic"
                               data-show-upload="false" accept="image/jpg,image/png,image/jpeg">
                        <span class="help-block">Accepted formats: png, jpg, jpeg. Max file size 2Mb</span>
                    </div>
                    <div class="text-right">
                        <a href="{{ route('user.users') }}" class="btn btn-default">
                            <i class="icon-arrow-left13 position-right"></i> @lang('buttons.back')
                        </a>
                        <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.submit') <i
                                    class="icon-arrow-right14 position-right"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="{{ url('assets/js/page/fileinput.js') }}"></script>
    <script src="{{ url('assets/js/page/summernote_init.js') }}"></script>
    <script>
        {{--summernoteInit('{{ route('editor_icon.fetch') }}');--}}
        $('.file-input').fileinput({
            browseLabel: 'Browse',
            browseIcon: '<i class="icon-file-plus"></i>',
            uploadIcon: '<i class="icon-file-upload2"></i>',
            removeIcon: '<i class="icon-cross3"></i>',
            layoutTemplates: {
                icon: '<i class="icon-file-check"></i>',
                modal: modalTemplate
            },
            allowedFileExtensions: ["png", "jpg", "jpeg"],
            initialCaption: "No file selected",
            initialPreview: '{{ $data['users']->image_path }}',
            initialPreviewAsData: true,
            previewZoomButtonClasses: previewZoomButtonClasses,
            previewZoomButtonIcons: previewZoomButtonIcons,
            fileActionSettings: fileActionSettings
        });
        let rules = {
            fullname: {
                required: true,
                minlength: 3,
                maxlength: 100,
                lettersAndSpace: true
            },
            email_id: {
                required: true,
                email: true
            },
            country_code:{
              required: true
            },
            mobile_no:{
                required: true
            },
            company:{
                required: true
            }

        };
        validateForm('#edituser', rules);
    </script>
@endsection