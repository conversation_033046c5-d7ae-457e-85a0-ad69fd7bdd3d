@extends('admin.layouts.master')

@section('title')
    Notifications
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">@lang('labels.notifications')</h5>
        </div>
        <table class="table datatable-basic table-bordered"  style="width: 100%">
            <thead>
            <tr>
                <th class="srNO">@lang('labels.SrNo')</th>
                <th>@lang('labels.userName')</th>
                <th>@lang('labels.categoryName')</th>
                {{--<th>@lang('labels.actions')</th>--}}
            </tr>
            </thead>
            <tbody>
            {{--{{$data['user'][1]->userNotifications[0]->category->name}}--}}
            {{--{{exit}}--}}
            @foreach($data['user'] as $user)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{$user->fullname}}</td>
                    <td>
                        @php
                            $notificationName = '';
                        @endphp
                        @foreach($user->userNotifications as $notification)
                            @if($notificationName == "")
                                @php $notificationName .= $notification->category->name @endphp
                            @else
                                @php $notificationName .= ', ' . $notification->category->name @endphp
                            @endif

                        @endforeach
                        {{ $notificationName }}
                    </td>
                    {{--<td>
                        <ul class="icons-list">
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <i class="icon-menu9"></i>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li><a href=""><i
                                                    class="icon-pencil6"></i> @lang('labels.edit')</a></li>
                                    @if($notification->status === \App\Utils\AppConstant::STATUS_ACTIVE)
                                        <li>
                                            <a href=""
                                               data-url=""><i
                                                        class="icon-cross2"></i> @lang('labels.inactive')</a></li>
                                    @else
                                        <li><a href=""><i
                                                        class="icon-checkmark3"></i> @lang('labels.active')</a></li>
                                    @endif
                                </ul>
                            </li>
                        </ul>
                    </td>--}}
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>

    {{--@include('admin.categories.floating_button')--}}
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter.js') }}"></script>
    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});

        function deleteRecord() {
            $('.confirm').click(function () {
                let url = $(this).attr('data-url');
                let title = $(this).attr('data-title');
                let message = $(this).attr('data-message');
                alertPopup(url, title, message);
            });
        }

        $(document).ready(function () {
            $('.datatable-basic').DataTable({
                "aoColumnDefs": [{"bSortable": false, "aTargets": [1,2]}],
            });
            /*$(".styled").uniform({
                radioClass: 'choice'
            });*/
            deleteRecord();
        });

    </script>

@endsection