@extends('admin.layouts.master')

@section('title')
    Messages
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
    <style>
        .btn-color:focus {
            color: #ffffff
        }

        .icon-list:before {
            content: none
        }

        .overflow {
            overflow-y: hidden;
        }
    </style>
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/forms/styling/uniform.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title">@lang('labels.Filter')</h5>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="display-block text-semibold">@lang('labels.status')</label>
                        <label class="radio-inline">
                            <input type="radio" value="" name="status" class="styled" checked="checked">
                            @lang('labels.all')
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="1" name="status" class="styled">
                            @lang('labels.active')
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="2" name="status" class="styled">
                            @lang('labels.inactive')
                        </label>
                    </div>
                </div>
                <div class="col-md-6">

                </div>
            </div>
            <div class="text-right">
                <button id="filter" class="btn btn-primary btn-color">@lang('labels.Filter') <i
                            class="icon-filter3 position-right"></i></button>
            </div>
        </div>
    </div>
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title">@lang('labels.messages').</h5>
        </div>
        <div class="panel-body overflow">
            <table class="table datatable-basic table-bordered" width="100%">
                <thead>
                <tr>
                    <th class="srNO">@lang('labels.SrNo')</th>
                    <th>@lang('labels.category')</th>
                    <th>@lang('labels.messageTitle')</th>
                    <th>@lang('labels.messageSubTitle')</th>
                    {{--<th>@lang('labels.messageBody')</th>--}}
                    <th>@lang('labels.publishedOn')</th>
                    <th>@lang('labels.status')</th>
                    <th>@lang('labels.actions')</th>
                </tr>
                </thead>

                <tbody>
                {{--@foreach($data['messages'] as $message)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $message->category ? $message->category->name : 'General Message' }}</td>
                        <td><a href="javascript:void(0)" class="openMessage"
                               data-value="{{ $message->id }}">{{ $message->title }}</a></td>
                        <td>{{ $message->subtitle }}</td>
                        --}}{{--<td>{{strip_tags(str_limit($message->body, 50)) }}</td>--}}{{--
                        <td>
                            <span class="timezone" data-time="{{$message->created_at}}"></span>
                        </td>
                        <td>
                        <span class="label {{$message->status === \App\Utils\AppConstant::STATUS_ACTIVE ? 'label-success' : 'label-danger'}}">
                            {{$message->status === \App\Utils\AppConstant::STATUS_ACTIVE ? __('labels.active') : __('labels.inactive')}}
                        </span>
                        </td>
                        <td>
                            <ul class="icons-list">
                                <li class="dropdown">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        <i class="icon-menu9"></i>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        --}}{{--<li><a href="{{ route('message.edit', ['id' => $message->id]) }}"><i
                                                        class="icon-pencil6"></i> @lang('labels.edit')</a></li>--}}{{--
                                        @if($message->status === \App\Utils\AppConstant::STATUS_ACTIVE)
                                            <li>
                                                <a href="{{ route('message.inactive', ['id' => $message->id]) }}"
                                                   data-url=""><i
                                                            class="icon-cross2"></i> @lang('labels.inactive')</a></li>
                                        @else
                                            <li><a href="{{ route('message.active', ['id' => $message->id]) }}"><i
                                                            class="icon-checkmark3"></i> @lang('labels.active')</a></li>
                                        @endif
                                    </ul>
                                </li>
                            </ul>
                        </td>
                    </tr>
                @endforeach--}}
                </tbody>
            </table>
        </div>
    </div>

    <div id="show_message" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary bg-header-master">
                    <h1 class="modal-title text-center"></h1>
                    <small>Published On: <span class="msg-date"></span></small>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12 col-md-12 col-lg-12">
                            <h6 class="text-bold">@lang('labels.messageSubTitle')</h6>
                            <p class="subtitle"></p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-12 col-md-12 col-lg-12">
                            <h6 class="text-bold">@lang('labels.messageBody')</h6>
                            <p class="body"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-color btn-font-color"
                            data-dismiss="modal">@lang('buttons.close')</button>
                </div>
            </div>
        </div>
    </div>


    @include('admin.categories.floating_button_message')
    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter_messages.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/moment/moment.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/plugins/tables/datatables/DatatableInit.js') }}"></script>

    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});

        function messagePopup() {
            $(document).on('click', '.openMessage', function () {
                $('#show_message').modal('show');
                let id = $(this).attr('data-value');
                axios({
                    method: 'GET',
                    url: '{{ url('admin/messages/messagePreview') }}/' + id,
                }).then(function (response) {
                    if (response.data) {
                        $('#show_message .modal-title').html(response.data.title);
                        $('#show_message .subtitle').html(response.data.subtitle);
                        $('#show_message .body').html(response.data.body);
                        $('#show_message .msg-date').html(moment.unix(response.data.created_at).format("DD/MM/YYYY hh:mm A"));
                        $('#show_message').modal('show');
                    } else {
                        new PNotify({
                            text: '{{ __('Record Not Found') }}',
                            addclass: 'top-error bg-danger'
                        });
                    }
                }).catch(function (error) {
                    new PNotify({
                        text: '{{ __('Temporary server error. Try again.') }}',
                        addclass: 'top-error bg-danger'
                    });
                });
            });
        }

        $(document).ready(function () {

            datatable();

            function datatable() {
                let route = "{{ route('message.fetch') }}";
                let data = function (data) {
                    let status = $("input[name='status']:checked").val();
                    data.status = status;
                }

                let columns = [
                    {
                        "data": null,
                        class: "text-center",
                        render: function (data, type, row, meta) {
                            return serialNo(meta);
                        }
                    },
                    {
                        data: 'category',
                        'orderable': false,
                        render: function (data) {
                            return data.category
                        }
                    },
                    {data: 'title', 'orderable': false,},
                    {data: 'subtitle', 'orderable': false,},
                    {
                        data: 'created_at', 'orderable': false,

                        render: function (data) {

                            return data.created_at
                        }
                    },
                    {
                        data: 'status',
                        render: function (data) {
                            return statusSpan(data.status);
                        }
                    },
                    {
                        'data': 'action',
                        'orderable': false, 'class': 'text-center', render: (data) => {
                            return actionList(data);
                        }
                    }
                ];
                dt = new DatatableInit(route, data, columns);
                return dt;
            }

            function actionList(data) {
                let action = '';
                action += '<ul class="icons-list"><li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown">' +
                    '<i class="icon-menu9"></i></a><ul class="dropdown-menu dropdown-menu-right">';
                // Active / Inactive Option
                if (parseInt(data.status) == "{{\App\Utils\AppConstant::STATUS_ACTIVE}}") {
                    url = '{{ route('message.inactive', ['id' => ':uuid'])}}';
                    className = 'icon-cross2';
                    title = '{{ __('labels.inactive') }}';
                } else {
                    url = '{{ route('message.active', ['id' => ':uuid'])}}';
                    className = 'icon-checkmark3';
                    title = '{{ __('labels.active') }}';
                }
                action += '<li><a href="' + url.replace(':uuid', data.uuid) + '"><i class="' + className + '"></i>' + title + '</a></li>';

                action += '</ul></li></ul>';
                return action;
            }

            messagePopup();
            // $('.datatable-basic').DataTable({
            //     "aoColumnDefs": [{"bSortable": false, "aTargets": [1, 2, 3, 4, 6]}],
            //     "columnDefs": [
            //         {"targets": [4, 6], "searchable": false}
            //     ],
            //     stateSave: true
            // });
            $(".styled").uniform({
                radioClass: 'choice'
            });

            function setTimezoneLocal() {
                $(".timezone").each(function (i, item) {
                    $(item).text(moment.unix($(item).data('time')).format("DD/MM/YYYY hh:mm A"));
                });
            }

            setTimezoneLocal();

            $('input[type="search"]').on('keyup', function () {
                setTimezoneLocal()
            })

            $(document).on('click', '.paginate_button', function () {
                setTimezoneLocal()
            });
        });

        $('#filter').click(function () {

            filter('{{ route('message.filter') }}', {
                'status': $('input[name="status"]:checked').val()
            });

        });

    </script>

@endsection