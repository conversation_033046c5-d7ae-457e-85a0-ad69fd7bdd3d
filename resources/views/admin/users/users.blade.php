@extends('admin.layouts.master')

@section('title')
    Users
@endsection

@section('css')
    <link href="{{ asset('assets/css/pages/categories.css') }}" rel="stylesheet" type="text/css">
    <style>
        .btn-color:focus {
            color: #ffffff
        }

        .icon-list:before {
            content: none
        }

        .country_row {
            padding: 0;
        }

        .overflow {
            overflow-y: hidden;
        }

        .table > thead:first-child > tr:first-child > th {
            border-top: 1px solid #bbb;
        }
    </style>
@endsection

@section('js')
    <script type="text/javascript" src="{{ asset('assets/js/plugins/ui/initials.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/datatables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/tables/datatables/custom.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/notifications/bootbox.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/forms/styling/uniform.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/axios/axios.min.js') }}"></script>
@endsection

@section('content')
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title">@lang('labels.Filter')</h5>
        </div>
        <div class="panel-body">
            <form action="{{ route('users.data') }}"
                  id="export_data"
                  method="post">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="display-block text-bold">@lang('labels.status')</label>
                                    <label class="radio-inline">
                                        <input type="radio" value="" name="status" class="styled" checked="checked">
                                        @lang('labels.all')
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" value="1" name="status" class="styled">
                                        @lang('labels.active')
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" value="2" name="status" class="styled">
                                        @lang('labels.inactive')
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label text-bold">@lang('labels.countryFilter')</label>
                                    <select class="bootstrap-select form-control editSelect" data-width="100%"
                                            name="country_filter_id" id="country_filter">
                                        <option value="">-- All Countries --</option>
                                        @foreach($data['countries'] as $country)
                                            <option value="{{$country->id}}"
                                                    id="country_filter_id">{{$country->country_name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6">
                                {{--<div class="form-group">
                                    <label class="control-label text-semibold">@lang('labels.format')</label>
                                    <select class="bootstrap-select form-control editSelect" data-width="100%"
                                            name="format_id" id="format">
                                        <option value="0">--Select Format--</option>
                                        <option value="1">CSV</option>
                                        <option value="2">XLSX</option>
                                    </select>
                                </div>

                                <div class="text-right">
                                    <button type="submit" class="btn btn-color btn-font-color">@lang('buttons.export') <i
                                                class="icon-arrow-right14 position-right"></i></button>
                                    <button id="filter" class="btn btn-primary btn-color">@lang('labels.Filter') <i
                                                class="icon-filter3 position-right"></i></button>
                                </div>--}}
                            </div>

                            <div class="col-md-6">
                                <div class="form-group" style="padding-left: 46px">
                                    <label class="control-label text-bold">@lang('labels.format')</label>
                                    <select class="bootstrap-select form-control editSelect" data-width="100%"
                                            name="format_id" id="format">
                                        <option value="0">--Select Format--</option>
                                        <option value="1">CSV</option>
                                        <option value="2">XLSX</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="text-right">
                <div>
                    <button type="submit" class="btn btn-color btn-font-color" id="export_btn">@lang('buttons.export')
                        <i
                                class="icon-arrow-right14 position-right"></i></button>
                    <button id="filter" class="btn btn-primary btn-color">@lang('labels.Filter') <i
                                class="icon-filter3 position-right"></i></button>
                </div>
            </div>
        </div>
    </div>
    <div class="panel panel-flat">
        <div class="panel-heading">
            <h5 class="panel-title text-center">@lang('labels.usersList')</h5>
        </div>
        <div class="panel-body overflow">
            <table class="table datatable-basic table-bordered" width="100%">
                <thead>
                <tr>
                    <th>@lang('labels.SrNo')</th>
                    <th>@lang('labels.userImage')</th>
                    <th>@lang('labels.name')</th>
                    <th>@lang('labels.email')</th>
                    <th>@lang('labels.company')</th>
                    <th>@lang('labels.mobile')</th>
                    <th>@lang('labels.memberSince')</th>
                    <th>@lang('labels.country')</th>
                    <th>@lang('labels.status')</th>
                    <th>@lang('labels.actions')</th>
                </tr>
                </thead>

                <tbody id="sortable-list-basic"></tbody>
            </table>
        </div>
    </div>

    <div id="show_aboutus" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary bg-header-master">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h2 class="modal-title"></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12 col-md-12 col-lg-12">
                            <div class="text-center">
                                <div class="thumbnail" style="margin-bottom:0;">
                                    <div class="thumb thumb-rounded">
                                        <img src="" class="instructorImage"
                                             alt="" style="height: 150px;width: 150px;">
                                    </div>
                                    <a class="caption text-center openAboutus" data-value="">
                                        <h3 class="text-semibold no-margin modal-title"></h3>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-md-12 col-lg-12">
                            <h6 class="text-semibold">@lang('labels.aboutus')</h6>
                            <p class="aboutus"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="" class="editButton">
                        <button type="button" class="btn btn-link">@lang('labels.edit')</button>
                    </a>
                    <button type="button" class="btn btn-link" data-dismiss="modal">@lang('labels.close')</button>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript" src="{{ url('assets/js/page/alert.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/page/filter_users.js') }}"></script>
    <script type="text/javascript" src="{{ asset('assets/js/plugins/moment/moment.js') }}"></script>
    <script type="text/javascript" src="{{ url('assets/js/plugins/tables/datatables/DatatableInit.js') }}"></script>
    <script>
        $('.profile').initial({charCount: 1, fontSize: 40});
        $(document).ready(function () {
            $(".styled").uniform({
                radioClass: 'choice'
            });

            $('#filter').click(function () {
                let country_id = $('#country_filter option:selected').val();
                filter('{{ route('users.filter') }}', {
                    'status': $('input[name="status"]:checked').val(),
                    'country_id': country_id
                });
            });
            datatable();

            $('#export_btn').click(function () {
                $('#export_data').submit();
            });

            function timezoneLoad() {
                $(".timezone").each(function (i, item) {
                    $(item).text(moment.unix($(item).data('time')).format("DD/MM/YYYY hh:mm A"));
                });
            }

            timezoneLoad();

            $(document).on('click', '.paginate_button', function () {
                timezoneLoad();
            })

            $(document).on('click', '#filter', function () {
                dt.draw();
            });

        });

        function datatable() {
            let route = "{{ route('users.fetch') }}";
            let data = function (data) {
                let status = $("input[name='status']:checked").val();
                data.status = status;

            }

            let columns = [
                    {
                        "data": null,
                        class: "text-center",
                        render: function (data, type, row, meta) {
                            return serialNo(meta);
                        }
                    },
                    {
                        data: 'profile_pic',
                        class: 'text-center',
                        'orderable': false,
                        render: function (data) {
                            return '<img class="card-img-basic img-circle" src="' + data.profile_pic + '" width="50" alt="User Image" onerror="this.onerror=null;this.src=\'{{ asset("assets/images/global/default-user.png") }}\';"/>';
                        }
                    },
                    {
                        data: 'fullname','orderable': false,
                    },
                    {data: 'email_id','orderable': false,},
                    {
                        data: 'company_name',
                        'orderable': false,
                        render: function (data) {
                            return data.company_name
                        }
                    },
                    {
                        data: 'mobile_no',
                        'orderable': false,
                        render: function (data) {
                            return data.mobile_no
                        }
                    },
                    {
                        data: 'created_at',
                        'orderable': false,
                    },
                    {
                        data: 'country',
                        'orderable': false,
                        render: function (data) {
                            return data.country
                        }
                    },

                    {
                        data: 'status',
                        render: function (data) {
                            return statusSpan(data.status);
                        }
                    },
                    {
                        'data': 'action',
                        'orderable': false, 'class': 'text-center', render: (data) => {
                            return actionList(data);
                        }
                    }
                ]
            ;
            dt = new DatatableInit(route, data, columns);
            return dt;
        }

        function actionList(data) {
            let action = '';
            action += '<ul class="icons-list"><li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown">' +
                '<i class="icon-menu9"></i></a><ul class="dropdown-menu dropdown-menu-right">';

            // Active / Inactive Option
            if (parseInt(data.status) == '{{\App\Utils\AppConstant::STATUS_ACTIVE}}') {
                url = '{{ route('user.inactive', ['id' => ':uuid'])}}';
                className = 'icon-cross2';
                title = '{{ __('labels.inactive') }}';
            } else {
                url = '{{ route('user.active', ['id' => ':uuid'])}}';
                className = 'icon-checkmark3';
                title = '{{ __('labels.active') }}';
            }
            action += '<li><a href="' + url.replace(':uuid', data.uuid) + '"><i class="' + className + '"></i>' + title + '</a></li>';

            action += '</ul></li></ul>';
            return action;
        }
    </script>

@endsection