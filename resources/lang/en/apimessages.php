<?php

return [
    'RecordNotFound' => 'Record Not Found.',
    'NotFound' => 'Not Found.',
    'serverError' => 'Temporary server error. Try Again.',
    'alertMessage' => 'Are You Sure?',
    'deleteMessage' => 'You will not be able to recover this :name record!',
    'unProcessRequest' => 'Invalid Request.',
    'uuidInvalid' => 'invalid uuid.',
    'NoAuthorizeForAction' => 'Your are not authorize for perform this action.',
    'NotificationsON' => 'Notifications ON',
    'NotificationsOFF' => 'Notifications OFF',
    'TickerON' => 'Ticker ON',
    'TickerOFF' => 'Ticker OFF',
    'NotificationsUpdated' => 'Notification Status updated',
    'tickerStatusUpdated' => 'Ticker status updated',
    'messageStatusUpdated' => 'Message status updated',
    'messageStatusON' => 'Message status ON',
    'messageStatusOFF' => 'Message status OFF',
    'favouriteStatusOFF' => 'Favourite status OFF',
    'favouriteStatusON' => 'Favourite status ON',
    'wrongPassword' => 'Old password is wrong.',
    'passwordChanged' => 'Password Changed Successfully.',
    'userLogout' => 'Successfully Logged out.',
    'profilePictureChanged' => 'Profile Picture Changed.',
    'quotationSaved' => 'Quotation request for this product is saved.',
    'messagesFetched' => 'All messages fetched successfully.',
    'forgot_password_mail_success' => 'Instructions to reset your password sent to your registered email id.',
    'emailNotFound' => 'Email address not found.',
    'productsFetched' => 'All products from this category fetched successfully.',
    'noProducts' => 'There are no products in this category.',
    'productFavoriteAdded' => 'Product added to favorite list.',
    'productFavoriteRemoved' => 'Product removed from favorite list.',
    'productDetailsFetched' => 'Product details fetched successfully.',
    'productPriceAndProductDetails' => 'Product details and price fetched successfully.',
    'categoriesSuccess' => 'Categories fetched successfully.',
    'message_notifications_off' => 'Message notifications are turned off by user.',
    'guestUserSuccess' => 'Guest User Added successful.',
    'profilePictureRemoved'=>'Profile Picture removed successfully.',
    'userSignupSuccess' => ':name registration successful.',
    'userSigninSuccess' => ':name logged in successfully.',
    'userSignInFailed' => 'These credentials do not match our records.',
    'userEmailNotVerified' => 'Your Email id is not verified.',
    'userDeleteSuccess' => ':name account has been deleted.',
    'userAccountTerminated' => 'Your Account has been Terminated.',
    'RecordFound' => 'Record fetched successfully.',
    'incorrectOTP' => 'The OTP you entered is incorrect.',
    'userEmailVerified' => 'Email ID successfully verified.',
    'userNotfound' => 'User not found.',
    'resendOTP' => 'OTP resend request successful.',
    'userGreeting' => 'Hello, :name',
    'quotationListSuccess' => 'Quotation List fetched successfully.',
    'ContentUpdateSuccess' => 'App content updated successfully.',
    'productListSuccess' => 'Product list fetched successfully.',
    'productGraphSuccess' => 'Product Graph fetched successfully.',
    'invalidGraphType' => 'Invalid Graph Type.',
    'userDetailsFetched' => 'User profile picture fetched successfully.',
    'profileUpdateSuccess' => 'User profile updated successfully.',
    'pushNotificationsFetched' => 'Push Notifications fetched successfully.',
    'messageDetailsFetched' => 'Message Details fetched successfully.',

    /*social login*/
    'email_required' => 'Email id is required.',
    'facebook_success' => 'Sign up success.',
    'social_login_success' => 'Login successful.',
    'social_already_register' => 'User already registered using facebook.',
    'token_mismatch' => 'Token does not match.',

    /*feedbacks*/
    'feedback' => 'Feedback submitted.',

];