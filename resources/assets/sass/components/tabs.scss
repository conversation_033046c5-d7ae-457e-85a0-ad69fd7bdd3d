.tab-content {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
  background-color: #FFF;
  box-shadow: 0px 5px 7px 0px rgba(0, 0, 0, 0.04);
  padding: 3rem;
  p {
    line-height: 1.8;
  }
  h2 {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 992px) {
  .tab-content {
    padding: 1.5rem;
  }
}

.nav-tabs {
  border-bottom: 0;
  .nav-item .nav-link {
    padding: 1rem 1rem;
    border-color: #faf6fb #faf6fb #FFF;
    font-size: 19px;
    color: #ffffff;
    background: #fdcb6e;
  }
  .nav-link {
    &:focus, &:hover {
      padding: 1rem 1rem;
      border-color: #faf6fb #faf6fb #FFF;
      font-size: 19px;
      color: #ffffff;
      background: #fdcb6e;
    }
    &.active {
      background: #FFF;
      border-top-width: 3px;
      border-color: #FCBA3A #faf6fb #FFF;
      color: #000000;
    }
  }
}