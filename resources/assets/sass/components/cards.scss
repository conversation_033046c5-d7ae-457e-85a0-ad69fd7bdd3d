.gradient-fill:before {
  color: #fc73b4;
  background: -moz-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background: -webkit-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background: $app-gradient-right;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card.features {
  border: 0;
  border-radius: 3px;
  box-shadow: 0 5px 7px 0 rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  &:before {
    content: "";
    position: absolute;
    width: 3px;
    color: #fc73b4;
    background: -moz-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
    background: -webkit-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
    background: $app-gradient-right;
    top: 0;
    bottom: 0;
    left: 0;
  }
}

@media (max-width: 991px) {
  .card.features {
    margin-bottom: 2rem;
  }
  [class^="col-"]:last-child .card.features {
    margin-bottom: 0;
  }
}

.card-text {
  font-size: 14px;
}

.card.features:hover {
  transform: translateY(-3px);
  -moz-box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.08);
}

.box-icon {
  /*box-shadow: 0px 0px 43px 0px rgba(0, 0, 0, 0.14);*/
  padding: 10px;
  width: 70px;
  border-radius: 3px;
  margin-bottom: 1.5rem;
  background-color: #FFF;
}

.circle-icon {
  box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.07);
  padding: 10px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 1.5rem;
  background-color: #FFF;
  color: #000000;
  font-size: 48px;
  text-align: center;
  line-height: 80px;
  font-weight: 300;
  transition: all 0.3s ease;
}

@media (max-width: 992px) {
  .circle-icon {
    width: 70px;
    height: 70px;
    font-size: 28px;
    line-height: 50px;
  }
}

.ui-steps li {
  &:hover .circle-icon {
    background-image: -moz-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
    background-image: -webkit-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
    background-image: -ms-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
    background-image: $app-gradient-right;
    box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.09);
    color: #FFF;
  }
  padding: 15px 0;
  &:not(:last-child) {
    border-bottom: 1px solid #f8e3f0;
  }
}

.perspective-phone {
  position: relative;
  z-index: -1;
}

@media (min-width: 992px) {
  .perspective-phone {
    margin-top: -150px;
  }
}