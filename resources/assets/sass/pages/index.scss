header {
  padding: 100px 0 0;
  text-align: center;
  color: #FFF;
}

.header_logo {
  width: 50px;
}

.bg-gradient {
  background-image: -moz-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background-image: -webkit-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background-image: -ms-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background-image: $app-gradient-right;
}

.tagline {
  font-size: 23px;
  font-weight: 300;
  color: #ffffff;
  max-width: 800px;
  margin: 0 auto;
}


.img-holder {
  height: 0;
  padding-bottom: 33%;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .img-holder {
    padding-bottom: 50%;
  }
}

@media (max-width: 767px) {
  .tagline {
    font-size: 17px;
  }
  .img-holder {
    padding-bottom: 100%;
  }
}

.base-slider {
  width: 60%;
  margin-left: auto;
  margin-right: auto;
  display: block;
  &.header-slider{
    width: 40%;
  }
}

.header_list_wrap {
  margin-left: auto;
  margin-right: auto;
  display: block;
  padding-top: 50px;
}

.mb_base_image{
  width: 60%;
}

//mobile devices media query
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
  .header_logo {
    width: 40px;
  }
  .base-slider {
    width: 100% !important;
  }
  .mb_base_image{
    width: 100%;
  }
}

//bootstrap carousel indicators
.carousel-indicators {
  padding: 30px;
}

#myCarousel-indicators > li {
  border-radius: 12px;
  width: 12px;
  height: 12px;
  background-color: #404040;
}

.store_icons {
  height: 50px;
  width: auto;
  /*&.play_store{
    @media screen and (min-width: 320px) and (max-width: 767px) {
      height: 54px !important;
    }
  }*/
}

.palm-oil-text {
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

//media for desktop
@media only screen and (min-width: 992px) {
  .desk_mockup {
    width: 40% !important;
  }
}

.main_color{
  background-color: #fdcb6e !important;
}

.card_height{
  min-height: 475px !important;
}

.overlap_img{
  width: 60%;
  height: auto;
}

.category_img{
  width: 30%;
  height: auto;
  margin: 20px auto 0 auto;
  /*margin-left: auto;
  margin-right: auto;*/
}

.category_card{
  border: 1px solid #FCBA3A !important;
  .card-body{
    //font-size: 13px;
    @media screen and (max-width:1280px) {
      //font-size: 12px;
    }
  }
  //padding: 2px 0 !important;
}

.home-container{
  min-height: 600px;
  //background: url("../../../../public/assets/images/website/palm_oil.jpg") no-repeat;
  .minimal-logo{
    width: 45px;
  }
  .home-mockups-wrapper{
    position: relative;
    .mockup-center{
      @media screen and (max-width:767px) {
        left: 0;
        position: relative;
      }
      width: 240px;
      position: absolute;
      left: 33.33%;
      z-index: 2;
    }
    .mockup-left{
      width: 220px;
      position: absolute;
      left: 5%;
      top: 20px;
      z-index: 0;
    }
    .mockup-right{
      width: 220px;
      position: absolute;
      left: 65%;
      top: 20px;
      z-index: 0;
    }
  }
  @media (max-width:320px)  {
    .hero-title{
      margin-top: 0;
    }
  }
}

.video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}

.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.black_text{
  color: black !important;
  //font-weight: bold !important;
}

.dark_nav{
  background: #343a40!important;

}

.white_nav{
  background: #ffffff!important;

}

.nav-menu{
  padding: 0 !important;
}

.gradient-text {
  font-size: 18px !important;
  background: -webkit-linear-gradient(#00AA62, #9ECE5B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text-black {
  font-size: 18px !important;
  background: -webkit-linear-gradient(#000000, #000000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-outline-gradient{
  border: 1px solid #fe8b07 !important;
}

.navbar-toggler {
  border-color: #fe8b07 !important;
}
.navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,<svg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'><path stroke='rgb(254, 139, 7)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

//override default
.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle{
  background-color: black !important;
}

.nav-link.active:after{
  border-bottom: 2px solid #fe8b07 !important;
}

.text-black{
  color: black !important;
}

//validation error label color
.validation-error-label{
  color: red !important;
}


#card-lists{
  font-family: arial;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .card{
    width: 19.7368%;
    border: 1px solid #ccc;
    display: flex;
    margin-left: 1.75438%;
    overflow: hidden;

    &:first-child{
      margin-left: 0;
    }
    figure{
      width: 100%;
      padding: 0;
      margin: 0;
      display: block;
      img{
        width: 100%;
        height: auto;
      }
      figcaption{
        padding: 10px;
        margin: 0;
        display: block;
      }

    }

  }
}

.qr-code-image{
  width: 20%;
  height: auto;
  @media screen and (min-width: 320px) and (max-width: 767px) {
    width: 40% !important;
    height: auto !important;
  }

  &.qr-code-image-right{
    margin-left: 8%;
    @media screen and (min-width: 320px) and (max-width: 767px) {
      margin-left: 10% !important;
    }
  }
}
