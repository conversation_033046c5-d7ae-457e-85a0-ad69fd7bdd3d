.btn {
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  padding: 0.375rem 1.35rem;
  transition: all 0.3s ease;
}

.btn-primary {
  border-radius: 3px;
  background: #FCBA3A;
  background-image: -webkit-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background-image: -ms-linear-gradient(to right, rgba(254, 139, 7, 0.8), rgba(254, 214, 22, 0.8));
  background-image: $app-gradient-right;
  box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.2);
  font-weight: 500;
  padding: 0.6rem 2rem;
  border: 0;
  &:hover, &:focus, &:active {
    background-image: $app-gradient-right;
    box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.3);
    color: #FFF;
  }
  &:not([disabled]):not(.disabled) {
    &.active, &:active {
      background-image: $app-gradient-right;
      box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.3);
      color: #FFF;
    }
  }
}

.show > .btn-primary.dropdown-toggle {
  background-image: $app-gradient-right;
  box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.3);
  color: #FFF;
}

.btn-light {
  border-radius: 3px;
  background: #FFF;
  box-shadow: 0 9px 32px 0 rgba(0, 0, 0, 0.26);
  font-size: 14px;
  font-weight: 500;
  color: #633991;
  margin: 0.5rem;
  padding: 0.7rem 1.6rem;
  line-height: 1.8;
}

.btn-group-lg > .btn, .btn-lg {
  padding: 0.8rem 1rem;
  font-size: 15px;
}