.nav-menu {
  padding: 1rem 0;
  transition: all 0.3s ease;
  &.is-scrolling, &.menu-is-open {
    background-color: #FCBA3A;
    -webkit-box-shadow: 0 5px 23px 0 rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 5px 23px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 5px 23px 0 rgba(0, 0, 0, 0.1);
  }
  &.is-scrolling {
    padding: 0;
  }
}


.navbar-nav .nav-link {
  position: relative;
}

@media (min-width: 992px) {
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem;
    font-size: 14px;
  }

  .navbar-nav > .nav-item > .nav-link.active:after {
    content: "";
    border-bottom: 2px solid #ffffff;
    left: 1rem;
    right: 1rem;
    bottom: 5px;
    height: 1px;
    position: absolute;
  }
}


@media (max-width: 991px) {
  .navbar-nav {
    &.is-scrolling {
      padding-bottom: 1rem;
    }
    .nav-item {
      text-align: center;
    }
  }
}