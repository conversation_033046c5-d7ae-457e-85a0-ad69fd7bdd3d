<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Mail;
use App\Mail\VerifyOTPMail;
use Illuminate\Support\Facades\Route;

Route::get('email', function(){
    $user = \App\Models\UserModel::find(85);

    $email = new VerifyOTPMail($user);
    Mail::to($user->email_id)->send($email);

    // VerifyOTP::dispatch($user)->onQueue(AppConstant::APP_QUEUE);
});


// Route::get('/test-sns/{userId}', function ($userId) {
//     $snsService = new SNSNotificationService();

//     $title = "Test Notification";
//     $message = "This is a test notification sent via AWS SNS.";

//     $result = $snsService->sendPushNotification($userId, $title, $message);
//     return response()->json([
//         'success' => $result ? true : false,
//         'message' => $result ? "Notification sent successfully!" : "Failed to send notification.",
//         'data' => $result
//     ]);
// });
// Route::get('/test-email', function () {
//     $userMessage = (object) [
//         'name' => 'John Doe',
//         'email' => '<EMAIL>',
//         'message' => 'Test message to verify SES email sending!'
//     ];
//     Mail::to('<EMAIL>')->send(new WebsiteFeedbackMail($userMessage));

//     return 'Test email sent!';
// });

//Route::domain('xyz.com')->group(function () {

Route::group(['prefix' => 'admin', 'namespace' => 'Admin'], function () {
    Route::group(['namespace' => 'Auth'], function () {
        Route::get('/', 'AuthController@showLoginForm');
        Route::post('signin', 'AuthController@login');
        Route::get('login', 'AuthController@showLoginForm');
    });

    Route::group(['middleware' => ['auth.check.admin']], function () {
        Route::post('logout', 'Auth\AuthController@logout');
        Route::get('/dashboard', 'Dashboard\DashboardController@dashboard')->name('admin.dashboard');
        Route::view('changePassword', 'admin.change_password')->name('admin.changePassword');
        Route::resource('admin', 'Auth\AuthController');

        Route::group(['prefix' => 'categories', 'namespace' => 'Categories'], function () {
            Route::get('/', 'CategoryController@index')->name('category.categories');
            Route::get('/exportProducts/{id}', 'CategoryController@exportProducts')->name('products.export');
            Route::get('category/active/{id}', 'CategoryController@activeInactive')->name('category.active');
            Route::get('category/inactive/{id}', 'CategoryController@activeInactive')->name('category.inactive');
            Route::get('category/edit/{id}', 'CategoryController@edit')->name('category.edit');
            Route::put('category/update', 'CategoryController@updateCategory')->name('category.update');
            Route::get('product/edit/{id}', 'CategoryController@editProduct')->name('category.editProduct');
            Route::get('product/price/{id}', 'CategoryController@productPrice')->name('category.productPrice');
            Route::get('product/documents/{id}/{category_id}', 'CategoryController@addProductDocuments')->name('category.addProductDocuments');
            Route::put('product/update/{id}', 'CategoryController@productUpdate')->name('category.productUpdate');
            Route::post('category/checkname', 'CategoryController@checkCategoryName')->name('category.checkname');
            Route::get('{id}/products', 'CategoryController@product')->name('category.product');
            Route::get('{id}/editPrice', 'CategoryController@editPrice')->name('category.editPrice');
            Route::put('edit/multiplePrice', 'CategoryController@multiplePrice')->name('category.multiplePrice');
            Route::get('{id}/addProduct', 'CategoryController@productAdd')->name('category.productAdd');
            /*Route::get('addMessage/', 'CategoryController@MessageAdd')->name('category.messageAdd');
            Route::put('addMsg/', 'CategoryController@addMessage')->name('category.addMessage');*/
            Route::put('product/add/{id}', 'CategoryController@addProduct')->name('category.addProduct');
            Route::put('product/add/docs/{id}', 'CategoryController@addProductDoc')->name('category.addProductDoc');
            Route::put('product/add/price/{id}', 'CategoryController@addProductPrice')->name('category.addProductPrice');
            Route::get('product/active/{id}', 'CategoryController@productActiveInactive')->name('categoryProduct.active');
            Route::get('product/inactive/{id}', 'CategoryController@productActiveInactive')->name('categoryProduct.inactive');
            Route::get('product/activeMarquee/{id}', 'CategoryController@marqueeActiveInactive')->name('categoryProduct.activeMarquee');
            Route::get('product/inactiveMarquee/{id}', 'CategoryController@marqueeActiveInactive')->name('categoryProduct.inactiveMarquee');
            Route::resource('category', 'CategoryController');

        });

        Route::group(['prefix' => 'users', 'namespace' => 'Users'], function () {
            Route::group(['middleware' => 'verifyAdmin'], function(){
                Route::get('/', 'UserController@index')->name('user.users');
                Route::get('/export', 'UserController@export')->name('user.export');
                Route::post('/data', 'UserController@userData')->name('users.data');
            });
//                Route::post('/fetch', 'UserController@get')->name('users.fetch');
            Route::post('/fetch', 'UserController@usersAjax')->name('users.fetch');
            Route::post('user/filter', 'UserController@filter')->name('users.filter');
            Route::post('user/countryFilter', 'UserController@countryFilter')->name('users.countryFilter');
            Route::get('user/active/{id}', 'UserController@activeInactive')->name('user.active');
            Route::get('user/inactive/{id}', 'UserController@activeInactive')->name('user.inactive');
            Route::get('user/destroy/{id}', 'UserController@destroy')->name('user.destroy');
            Route::put('changePassword/{id}', 'UserController@ChangeAdminPassword')->name('user.ChangeAdminPassword');
            Route::resource('user', 'UserController');
        });

        Route::group(['prefix' => 'messages', 'namespace' => 'Messages'], function () {
            Route::get('/', 'MessageController@messages')->name('message.messages');
            Route::post('message/filter', 'MessageController@filter')->name('message.filter');
            Route::post('message/fetch', 'MessageController@fetchMessage')->name('message.fetch');
            Route::get('message/active/{id}', 'MessageController@activeInactive')->name('message.active');
            Route::get('message/inactive/{id}', 'MessageController@activeInactive')->name('message.inactive');
            Route::get('message/edit/{id}', 'MessageController@edit')->name('message.edit');
            // Route::get('/notifications', 'MessageController@notifications')->name('message.notifications');
            // Route::get('/pushNotifications', 'MessageController@pushNotifications')->name('message.pushNotifications');
            // Route::post('/sendPushNotification', 'MessageController@sendPushNotification')->name('message.sendPushNotification');
            Route::get('messagePreview/{id}', 'MessageController@messageDetail')->name('messageDetail');
            Route::get('addMessage/', 'MessageController@MessageAdd')->name('message.messageAdd');
            Route::put('addMsg/', 'MessageController@addMessage')->name('message.addMessage');
            Route::resource('message', 'MessageController');
        });

        Route::group(['prefix' => 'products', 'namespace' => 'Product'], function () {
            Route::post('product/filter', 'ProductController@filter')->name('product.filter');
            Route::post('/data', 'ProductController@productData')->name('product.data');
            Route::resource('product', 'ProductController');
            // Route::get('/quotations', 'ProductController@cifQuotation')->name('product.quotations');
            // Route::post('/quotations_fetch', 'ProductController@cifQuotationFetch')->name('product.quotations_fetch');
            // Route::get('quotation/active/{id}', 'ProductController@QuotationactiveInactive')->name('product.active');
            // Route::get('quotation/inactive/{id}', 'ProductController@QuotationactiveInactive')->name('product.inactive');
            // Route::get('quotation/edit/{id}', 'ProductController@editquotation')->name('product.edit');
            // Route::get('quotationPreview/{id}', 'ProductController@quotationDetail')->name('quotationDetail');
            // Route::post('/export/quotation', 'ProductController@quotationExport')->name('quotation.data');
        });

        Route::group(['prefix' => 'appContent', 'namespace' => 'AppContent'], function () {
            Route::get('/privacy', 'AppContentController@privacy')->name('appContent.privacy');
            Route::get('/terms', 'AppContentController@terms')->name('appContent.terms');
            Route::resource('appContent', 'AppContentController');
        });

        // Route::group(['prefix' => 'feedback', 'namespace' => 'UserFeedback'], function () {
        //     Route::get('/userFeedback', 'UserFeedbackController@index')->name('feedback');
        //     Route::get('/user_contact_us', 'UserFeedbackController@user_contact_us')->name('user_contact_us');
        //     Route::post('/fetch_user_contact_us', 'UserFeedbackController@user_contact_us_ajax')->name('fetch_user_contact_us');
        //     Route::post('/feedback_fetch', 'UserFeedbackController@userFeedbackAjax')->name('feedback_fetch');
        //     Route::get('feedbackPreview/{id}', 'UserFeedbackController@feedbackDetail')->name('feedbackDetail');
        //     Route::get('user_contact_us_preview/{id}', 'UserFeedbackController@userContactUsDetail')->name('userContactUsDetail');
        // });

        Route::group(['prefix' => 'notification', 'namespace' => 'Notifications', 'middleware' => 'verifyAdmin'], function () {
            Route::get('/userNotifications', 'NotificationController@notifications')->name('notification');
            // Route::get('feedbackPreview/{id}', 'UserFeedbackController@feedbackDetail')->name('feedbackDetail');
        });

        Route::group(['prefix' => 'historical_data', 'namespace' => 'HistoricalData'], function () {
            Route::get('/historical_data', 'HistoricalDataController@index')->name('historical.index');
        });

        Route::get('notification-test', function () {
            // $user = \App\Models\UserModel::find(2);
            // $user->notify(new \App\Notifications\TestNorification('Test Notification New'));
        });
    });
});

Route::get('/', function () {
    return view('website.new');
});


//contact us form data store website
Route::group(['namespace' => 'Website'], function () {
    // Route::post('contact_us', 'UserContactUs@contactUsUser')->name('contact_us');

    Route::group(['middleware' => ['auth.check.admin']], function () {
        Route::get('contact_us_list', 'UserContactUs@index')->name('contact_us.index');
    });
});

Route::get('/privacy-policy', 'Admin\AppContent\AppContentController@privacyPolicy');

Route::get('/reset_password/{uuid}/{forgotPasswordCode}', 'Common\AuthController@resetPasswordView')->name('forgotPassword')->middleware('signed');
Route::post('/update_reset_password', 'Common\AuthController@resetPassword');
Route::get('reset_success', function () {
    return view('web.passwordUpdateSuccess');
});


//});