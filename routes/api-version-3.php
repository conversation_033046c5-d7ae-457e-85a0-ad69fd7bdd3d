<?php
Route::group(['prefix' => 'v3', 'namespace' => 'Api\v3'], function () {
    Route::group(['namespace' => 'Authentication'], function () {
        Route::post('signup', 'AuthController@signup');
        Route::post('signin', 'Auth<PERSON><PERSON>roller@signin');
        Route::post('verifyOTP', 'AuthController@verifyOTP');
        Route::post('resendOTP', 'AuthController@resendOTP');
        Route::post('forgotPassword', 'AuthController@forgotPassword');
        Route::post('socialLogin', 'AuthController@socialLogin');
        Route::post('socialSignUp', 'AuthController@socialSignUp');
        Route::post('editUserProfile', 'AuthController@editUserProfile');
    });

    Route::group(['namespace' => 'ForceUpdate'], function () {
        Route::post('forceUpdate', 'ForceUpdate@checkAppVersion');
    });

    Route::group(['namespace' => 'AppContent'], function () {
        Route::post('fetchcontent', 'AppContentController@appContent');
        Route::get('fetchcountry', 'AppContentController@fetchCountry');
    });

    Route::group(['namespace' => 'Category'], function () {
        Route::post('fetchGuestProducts', 'CategoryController@fetchGuestProducts');
    });

    Route::group(['namespace' => 'Product'], function () {
        Route::get('get_marquee_guest', 'ProductController@get_marquee_guest');
    });

    Route::group(['middleware' => ['VerifyJWTToken']], function () {

        Route::group(['namespace' => 'Category'], function () {
            Route::post('fetchProducts', 'CategoryController@fetchProducts');
        });

        Route::group(['namespace' => 'Authentication'], function () {
            Route::post('logout', 'AuthController@logout');
        });
        Route::group(['namespace' => 'Product'], function () {
            Route::post('cif_quotation', 'ProductController@cifQuotation');
//            Route::get('get_messages', 'ProductController@get_messages');
//            Route::post('message_details', 'ProductController@message_details');
            Route::post('product_favorite', 'ProductController@product_favorite');
            Route::post('product_details', 'ProductController@product_details');
            Route::post('product_graph', 'ProductController@product_graph');
            Route::get('get_marquee', 'ProductController@marquee');
        });
        Route::group(['namespace' => 'User'], function () {
            Route::get('notifications_options', 'UserController@notifications_options');
            Route::get('ticker_options', 'UserController@ticker_options');
            Route::post('notifications_toggle', 'UserController@notifications_toggle');
            Route::post('ticker_toggle', 'UserController@ticker_toggle');
            Route::get('message_status', 'UserController@message_status_update');
            Route::get('favourite_status', 'UserController@favourite_status_update');
            Route::post('change_password', 'UserController@change_password');
            Route::post('change_profile_picture', 'UserController@change_profile_picture');
            Route::post('remove_profile_picture', 'UserController@remove_profile_picture');
//            Route::get('dashboardOptions', 'UserController@dashboardOptions');
            Route::get('quotationList', 'UserController@quotationList');
            Route::get('favouriteProductList', 'UserController@favouriteProdList');
//            Route::post('product_list', 'UserController@product_list');
            Route::get('user_details', 'UserController@user_details');
            Route::get('push_notifications', 'UserController@push_notifications');
            Route::post('deleteUser', 'UserController@deleteUser');
        });
        Route::group(['namespace' => 'UserFeedback'], function () {
            Route::post('storeFeedback', 'UserFeedbackController@store');
        });
    });

    Route::group(['namespace' => 'User'], function () {
        Route::get('dashboardOptions', 'UserController@dashboardOptions');
        Route::post('product_list', 'UserController@product_list');
        Route::post('guest_user', 'UserController@guest_user');
        Route::get('guest_push_notifications', 'UserController@guest_push_notifications');
    });

    Route::group(['namespace' => 'Product'], function () {
        Route::get('get_messages', 'ProductController@get_messages');
        Route::post('message_details', 'ProductController@message_details');
    });
});