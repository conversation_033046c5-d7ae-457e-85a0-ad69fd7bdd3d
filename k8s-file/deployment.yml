apiVersion: apps/v1
kind: Deployment
metadata:
  name: mblion-backend-service
  namespace: client-project
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mblion-backend-service
  template:
    metadata:
      labels:
        app: mblion-backend-service
    spec:
      containers:
      - name: mblion-backend-service
        image: gitlab.webmobtech.biz:5050/mblion/mblion_backend/php-fpm:{{CI_PIPELINE_ID}}
        ports:
        - containerPort: 80
        env:
        - name: MYSQL_HOST
          value: mysql5-0.mysql5.database.svc.cluster.local
        - name: MYSQL_PORT
          value: "3306"
        - name: MYSQL_USER
          value: "mblion_dev"
        - name: MYSQL_PASSWORD
          value: "WKtcJ31(U*LmSPgf"
        - name: MYSQL_DATABASE
          value: "mblion_oleochemicals"
        volumeMounts:
        - name: backend-storage
          mountPath: /var/www/storage
          subPath: mblion-backend  
        securityContext:
          runAsUser: 0  
          runAsGroup: 0  
        resources:
          requests:
            cpu: "250m"
            memory: "300Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
      volumes:
      - name: backend-storage
        persistentVolumeClaim:
          claimName: pvc-client-project  
      imagePullSecrets:
      - name: client-project-registry
