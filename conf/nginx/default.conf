upstream laravel {
    server localhost:9000;

}
server {
    server_name _ default_server;
    listen 80;

    client_max_body_size 0;
    client_body_buffer_size 32k;
    client_header_buffer_size 8k;
    large_client_header_buffers 8 64k;
    proxy_buffer_size 32k;
    proxy_buffers 4 32k;


    index index.php index.html;
    root /var/www/public;

    gzip on;
    gzip_comp_level 4;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;


    location / {
        try_files $uri /index.php?$query_string;
    }

     location ~ \.php$ {
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
              fastcgi_pass laravel;
              fastcgi_index index.php;
              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
              fastcgi_param PATH_INFO $fastcgi_path_info;
            }
}

