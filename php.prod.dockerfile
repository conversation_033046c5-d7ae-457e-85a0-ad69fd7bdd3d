FROM kamerk22/laravel-alpine:7.2-mysql-nginx

COPY composer.json composer.json
COPY composer.lock composer.lock
RUN composer global require hirak/prestissimo
RUN composer install --prefer-dist --no-scripts --no-autoloader && rm -rf /root/.composer

ADD conf/nginx/default.conf /etc/nginx/conf.d/

ADD . .

RUN cp .env.prod_server .env && cp conf/supervisor/services.ini /etc/supervisor.d/

RUN chmod a+trwx /tmp

RUN chmod 777 -R storage/

RUN chown -R www-data:www-data \
        /var/www/storage \
        /var/www/bootstrap/cache

RUN composer dump-autoload --no-scripts --optimize

RUN ln -s /var/www/storage/app/ /var/www/public/storage
RUN chmod 777 -R storage bootstrap/cache