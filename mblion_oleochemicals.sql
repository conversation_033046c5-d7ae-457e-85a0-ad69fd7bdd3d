-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.4
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 16, 2019 at 08:43 AM
-- Server version: 10.1.37-MariaDB
-- PHP Version: 7.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `mblion_oleochemicals`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `id` int(10) UNSIGNED NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`id`, `uuid`, `name`, `email_id`, `password`, `remember_token`, `status`, `created_at`, `updated_at`) VALUES
(1, '634a0c72-f64a-4352-9d19-5a7f92b4fe30', 'Admin', '<EMAIL>', '$2y$10$q6KRRjj130LU7Av0LzwpBuP3wUXttxJmgEvJosGBinL88jERO7r92', 'NdHTNHjKjfm8zya17dfHmBdayfIKEgHJvvXv6EyXlceA3eF6RpsP90Plk9Xy', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `app_content`
--

CREATE TABLE `app_content` (
  `id` int(10) UNSIGNED NOT NULL,
  `type` tinyint(4) NOT NULL COMMENT '1:privacy_policy; 2:terms_and_conditions',
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `app_content`
--

INSERT INTO `app_content` (`id`, `type`, `name`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'Privacy Policy', 'MB Lion Oleochemicals built the MB Lion Oleochemicals app as a Freemium app. This SERVICE is provided by MB Lion Oleochemicals at no cost and is intended for use as is.', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 2, 'Terms And Conditions', 'This Service does not use these “cookies” explicitly. However, the app may use third party code and libraries that use “cookies” to collect information and improve their services. You have the option to either accept or refuse these cookies and know when a cookie is being sent to your device. If you choose to refuse our cookies, you may not be able to use some portions of this Service.\n\n', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE `category` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `category`
--

INSERT INTO `category` (`id`, `name`, `image_path`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Palm Oil', 'category/Palm Oil5d1373f0d4e93.png', 1, '2019-04-30 11:04:14', '2019-06-26 08:02:32'),
(2, 'Glycerin', 'category/Glycerin5d1373d9c5332.png', 1, '2019-04-30 11:04:14', '2019-06-26 08:02:09'),
(3, 'Soap Noodles', 'category/Soap Noodles5d1373e5ac130.png', 1, '2019-04-30 11:04:14', '2019-06-26 08:02:21'),
(4, 'Fatty Alcohol', 'category/Fatty Alcohol5cc837ce7c0f2.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:55:58'),
(5, 'Fatty Acid', 'category/Fatty Acid5cc837d858712.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:56:08'),
(6, 'Stearic Acid', 'category/Stearic Acid5cc837e200d7f.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:56:18'),
(7, 'Surfactant', 'category/Surfactant5cc837ec52e8a.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:56:28'),
(8, 'Bio Diesel', 'category/Bio Diesel5cc837fb80706.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:56:43'),
(9, 'Petrochemicals', 'category/Petrochemicals5cc8380594181.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:56:53');

-- --------------------------------------------------------

--
-- Table structure for table `cif_quotation`
--

CREATE TABLE `cif_quotation` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `packing_id` int(10) UNSIGNED NOT NULL,
  `unit_id` int(10) UNSIGNED NOT NULL,
  `email_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `destination_port` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `trade_term` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int(11) NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cif_quotation`
--

INSERT INTO `cif_quotation` (`id`, `user_id`, `packing_id`, `unit_id`, `email_id`, `product_name`, `destination_port`, `trade_term`, `country`, `quantity`, `message`, `status`, `created_at`, `updated_at`) VALUES
(1, 4, 2, 1, '<EMAIL>', 'RBD Palm Olein', 'aqaba', 'FOB', 'India', 200, 'hi', 1, '2019-04-30 14:22:32', '2019-04-30 14:22:32'),
(2, 4, 3, 1, '<EMAIL>', 'RBD Palm Oil', 'mundra', 'CIF', 'Albania', 500, 'hi', 1, '2019-05-01 06:21:32', '2019-05-01 06:21:32'),
(3, 4, 3, 1, '<EMAIL>', 'Refined Glycerine 99.7 USP', 'aqaba', 'EXW', 'Anguilla', 55, 'hello', 1, '2019-05-01 15:37:57', '2019-05-01 15:37:57'),
(4, 10, 2, 1, '<EMAIL>', 'Crude Palm Kernel Oil - CPKO', 'asd', 'FOB', 'India', 25, 'testmsg', 1, '2019-06-13 05:54:38', '2019-06-13 05:54:38');

-- --------------------------------------------------------

--
-- Table structure for table `country`
--

CREATE TABLE `country` (
  `id` int(10) UNSIGNED NOT NULL,
  `country_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country_short_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `country`
--

INSERT INTO `country` (`id`, `country_name`, `country_code`, `country_short_name`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Afghanistan', '+93', 'AF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 'Aland Islands', '+358', 'AX', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(3, 'Albania', '+355', 'AL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(4, 'Algeria', '+213', 'DZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(5, 'AmericanSamoa', '+1684', 'AS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(6, 'Andorra', '+376', 'AD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(7, 'Angola', '+244', 'AO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(8, 'Anguilla', '+1264', 'AI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(9, 'Antarctica', '+672', 'AQ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(10, 'Antigua and Barbuda', '+1268', 'AG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(11, 'Argentina', '+54', 'AR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(12, 'Armenia', '+374', 'AM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(13, 'Aruba', '+297', 'AW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(14, 'Australia', '+61', 'AU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(15, 'Austria', '+43', 'AT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(16, 'Azerbaijan', '+994', 'AZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(17, 'Bahamas', '+1242', 'BS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(18, 'Bahrain', '+973', 'BH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(19, 'Bangladesh', '+880', 'BD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(20, 'Barbados', '+1246', 'BB', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(21, 'Belarus', '+375', 'BY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(22, 'Belgium', '+32', 'BE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(23, 'Belize', '+501', 'BZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(24, 'Benin', '+229', 'BJ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(25, 'Bermuda', '+1441', 'BM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(26, 'Bhutan', '+975', 'BT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(27, 'Bolivia, Plurinational State of', '+591', 'BO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(28, 'Bosnia and Herzegovina', '+387', 'BA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(29, 'Botswana', '+267', 'BW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(30, 'Brazil', '+55', 'BR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(31, 'British Indian Ocean Territory', '+246', 'IO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(32, 'Brunei Darussalam', '+673', 'BN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(33, 'Bulgaria', '+359', 'BG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(34, 'Burkina Faso', '+226', 'BF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(35, 'Burundi', '+257', 'BI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(36, 'Cambodia', '+855', 'KH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(37, 'Cameroon', '+237', 'CM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(38, 'Canada', '+1', 'CA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(39, 'Cape Verde', '+238', 'CV', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(40, 'Cayman Islands', '+ 345', 'KY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(41, 'Central African Republic', '+236', 'CF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(42, 'Chad', '+235', 'TD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(43, 'Chile', '+56', 'CL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(44, 'China', '+86', 'CN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(45, 'Christmas Island', '+61', 'CX', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(46, 'Cocos (Keeling) Islands', '+61', 'CC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(47, 'Colombia', '+57', 'CO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(48, 'Comoros', '+269', 'KM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(49, 'Congo', '+242', 'CG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(50, 'Congo, The Democratic Republic of the Congo', '+243', 'CD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(51, 'Cook Islands', '+682', 'CK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(52, 'Costa Rica', '+506', 'CR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(53, 'Cote d\'Ivoire', '+225', 'CI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(54, 'Croatia', '+385', 'HR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(55, 'Cuba', '+53', 'CU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(56, 'Cyprus', '+357', 'CY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(57, 'Czech Republic', '+420', 'CZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(58, 'Denmark', '+45', 'DK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(59, 'Djibouti', '+253', 'DJ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(60, 'Dominica', '+1767', 'DM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(61, 'Dominican Republic', '+1849', 'DO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(62, 'Ecuador', '+593', 'EC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(63, 'Egypt', '+20', 'EG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(64, 'El Salvador', '+503', 'SV', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(65, 'Equatorial Guinea', '+240', 'GQ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(66, 'Eritrea', '+291', 'ER', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(67, 'Estonia', '+372', 'EE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(68, 'Ethiopia', '+251', 'ET', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(69, 'Falkland Islands (Malvinas)', '+500', 'FK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(70, 'Faroe Islands', '+298', 'FO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(71, 'Fiji', '+679', 'FJ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(72, 'Finland', '+358', 'FI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(73, 'France', '+33', 'FR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(74, 'French Guiana', '+594', 'GF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(75, 'French Polynesia', '+689', 'PF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(76, 'Gabon', '+241', 'GA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(77, 'Gambia', '+220', 'GM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(78, 'Georgia', '+995', 'GE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(79, 'Germany', '+49', 'DE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(80, 'Ghana', '+233', 'GH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(81, 'Gibraltar', '+350', 'GI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(82, 'Greece', '+30', 'GR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(83, 'Greenland', '+299', 'GL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(84, 'Grenada', '+1473', 'GD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(85, 'Guadeloupe', '+590', 'GP', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(86, 'Guam', '+1671', 'GU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(87, 'Guatemala', '+502', 'GT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(88, 'Guernsey', '+44', 'GG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(89, 'Guinea', '+224', 'GN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(90, 'Guinea-Bissau', '+245', 'GW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(91, 'Guyana', '+595', 'GY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(92, 'Haiti', '+509', 'HT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(93, 'Holy See (Vatican City State)', '+379', 'VA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(94, 'Honduras', '+504', 'HN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(95, 'Hong Kong', '+852', 'HK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(96, 'Hungary', '+36', 'HU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(97, 'Iceland', '+354', 'IS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(98, 'India', '+91', 'IN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(99, 'Indonesia', '+62', 'ID', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(100, 'Iran, Islamic Republic of Persian Gulf', '+98', 'IR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(101, 'Iraq', '+964', 'IQ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(102, 'Ireland', '+353', 'IE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(103, 'Isle of Man', '+44', 'IM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(104, 'Israel', '+972', 'IL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(105, 'Italy', '+39', 'IT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(106, 'Jamaica', '+1876', 'JM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(107, 'Japan', '+81', 'JP', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(108, 'Jersey', '+44', 'JE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(109, 'Jordan', '+962', 'JO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(110, 'Kazakhstan', '+77', 'KZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(111, 'Kenya', '+254', 'KE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(112, 'Kiribati', '+686', 'KI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(113, 'Korea, Democratic People\'s Republic of Korea', '+850', 'KP', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(114, 'Korea, Republic of South Korea', '+82', 'KR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(115, 'Kuwait', '+965', 'KW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(116, 'Kyrgyzstan', '+996', 'KG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(117, 'Laos', '+856', 'LA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(118, 'Latvia', '+371', 'LV', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(119, 'Lebanon', '+961', 'LB', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(120, 'Lesotho', '+266', 'LS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(121, 'Liberia', '+231', 'LR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(122, 'Libyan Arab Jamahiriya', '+218', 'LY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(123, 'Liechtenstein', '+423', 'LI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(124, 'Lithuania', '+370', 'LT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(125, 'Luxembourg', '+352', 'LU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(126, 'Macao', '+853', 'MO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(127, 'Macedonia', '+389', 'MK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(128, 'Madagascar', '+261', 'MG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(129, 'Malawi', '+265', 'MW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(130, 'Malaysia', '+60', 'MY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(131, 'Maldives', '+960', 'MV', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(132, 'Mali', '+223', 'ML', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(133, 'Malta', '+356', 'MT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(134, 'Marshall Islands', '+692', 'MH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(135, 'Martinique', '+596', 'MQ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(136, 'Mauritania', '+222', 'MR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(137, 'Mauritius', '+230', 'MU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(138, 'Mayotte', '+262', 'YT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(139, 'Mexico', '+52', 'MX', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(140, 'Micronesia, Federated States of Micronesia', '+691', 'FM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(141, 'Moldova', '+373', 'MD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(142, 'Monaco', '+377', 'MC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(143, 'Mongolia', '+976', 'MN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(144, 'Montenegro', '+382', 'ME', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(145, 'Montserrat', '+1664', 'MS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(146, 'Morocco', '+212', 'MA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(147, 'Mozambique', '+258', 'MZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(148, 'Myanmar', '+95', 'MM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(149, 'Namibia', '+264', 'NA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(150, 'Nauru', '+674', 'NR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(151, 'Nepal', '+977', 'NP', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(152, 'Netherlands', '+31', 'NL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(153, 'Netherlands Antilles', '+599', 'AN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(154, 'New Caledonia', '+687', 'NC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(155, 'New Zealand', '+64', 'NZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(156, 'Nicaragua', '+505', 'NI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(157, 'Niger', '+227', 'NE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(158, 'Nigeria', '+234', 'NG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(159, 'Niue', '+683', 'NU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(160, 'Norfolk Island', '+672', 'NF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(161, 'Northern Mariana Islands', '+1670', 'MP', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(162, 'Norway', '+47', 'NO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(163, 'Oman', '+968', 'OM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(164, 'Pakistan', '+92', 'PK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(165, 'Palau', '+680', 'PW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(166, 'Palestinian Territory, Occupied', '+970', 'PS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(167, 'Panama', '+507', 'PA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(168, 'Papua New Guinea', '+675', 'PG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(169, 'Paraguay', '+595', 'PY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(170, 'Peru', '+51', 'PE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(171, 'Philippines', '+63', 'PH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(172, 'Pitcairn', '+872', 'PN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(173, 'Poland', '+48', 'PL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(174, 'Portugal', '+351', 'PT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(175, 'Puerto Rico', '+1939', 'PR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(176, 'Qatar', '+974', 'QA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(177, 'Romania', '+40', 'RO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(178, 'Russia', '+7', 'RU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(179, 'Rwanda', '+250', 'RW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(180, 'Reunion', '+262', 'RE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(181, 'Saint Barthelemy', '+590', 'BL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(182, 'Saint Helena, Ascension and Tristan Da Cunha', '+290', 'SH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(183, 'Saint Kitts and Nevis', '+1869', 'KN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(184, 'Saint Lucia', '+1758', 'LC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(185, 'Saint Martin', '+590', 'MF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(186, 'Saint Pierre and Miquelon', '+508', 'PM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(187, 'Saint Vincent and the Grenadines', '+1784', 'VC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(188, 'Samoa', '+685', 'WS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(189, 'San Marino', '+378', 'SM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(190, 'Sao Tome and Principe', '+239', 'ST', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(191, 'Saudi Arabia', '+966', 'SA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(192, 'Senegal', '+221', 'SN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(193, 'Serbia', '+381', 'RS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(194, 'Seychelles', '+248', 'SC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(195, 'Sierra Leone', '+232', 'SL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(196, 'Singapore', '+65', 'SG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(197, 'Slovakia', '+421', 'SK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(198, 'Slovenia', '+386', 'SI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(199, 'Solomon Islands', '+677', 'SB', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(200, 'Somalia', '+252', 'SO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(201, 'South Africa', '+27', 'ZA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(202, 'South Sudan', '+211', 'SS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(203, 'South Georgia and the South Sandwich Islands', '+500', 'GS', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(204, 'Spain', '+34', 'ES', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(205, 'Sri Lanka', '+94', 'LK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(206, 'Sudan', '+249', 'SD', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(207, 'Suriname', '+597', 'SR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(208, 'Svalbard and Jan Mayen', '+47', 'SJ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(209, 'Swaziland', '+268', 'SZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(210, 'Sweden', '+46', 'SE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(211, 'Switzerland', '+41', 'CH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(212, 'Syrian Arab Republic', '+963', 'SY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(213, 'Taiwan', '+886', 'TW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(214, 'Tajikistan', '+992', 'TJ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(215, 'Tanzania, United Republic of Tanzania', '+255', 'TZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(216, 'Thailand', '+66', 'TH', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(217, 'Timor-Leste', '+670', 'TL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(218, 'Togo', '+228', 'TG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(219, 'Tokelau', '+690', 'TK', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(220, 'Tonga', '+676', 'TO', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(221, 'Trinidad and Tobago', '+1868', 'TT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(222, 'Tunisia', '+216', 'TN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(223, 'Turkey', '+90', 'TR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(224, 'Turkmenistan', '+993', 'TM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(225, 'Turks and Caicos Islands', '+1649', 'TC', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(226, 'Tuvalu', '+688', 'TV', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(227, 'Uganda', '+256', 'UG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(228, 'Ukraine', '+380', 'UA', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(229, 'United Arab Emirates', '+971', 'AE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(230, 'United Kingdom', '+44', 'GB', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(231, 'United States', '+1', 'US', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(232, 'Uruguay', '+598', 'UY', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(233, 'Uzbekistan', '+998', 'UZ', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(234, 'Vanuatu', '+678', 'VU', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(235, 'Venezuela, Bolivarian Republic of Venezuela', '+58', 'VE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(236, 'Vietnam', '+84', 'VN', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(237, 'Virgin Islands, British', '+1284', 'VG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(238, 'Virgin Islands, U.S.', '+1340', 'VI', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(239, 'Wallis and Futuna', '+681', 'WF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(240, 'Yemen', '+967', 'YE', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(241, 'Zambia', '+260', 'ZM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(242, 'Zimbabwe', '+263', 'ZW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `currency`
--

CREATE TABLE `currency` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `currency`
--

INSERT INTO `currency` (`id`, `name`, `code`, `symbol`, `status`, `created_at`, `updated_at`) VALUES
(1, 'US Dollar', 'USD', '$', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 'Malaysian Ringgit', 'MYR', 'RM', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(3, 'Euro', 'EUR', '€', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(4, 'Indian Rupee', 'INR', '₹', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(5, 'Renminbi', 'RMB', '¥', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `delivery_term`
--

CREATE TABLE `delivery_term` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `delivery_term`
--

INSERT INTO `delivery_term` (`id`, `name`, `status`, `created_at`, `updated_at`) VALUES
(1, 'FOB', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 'CFR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(3, 'CIF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(4, 'EXW', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(5, 'FOB & CFR', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(6, 'FOB & CIF', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `force_update_models`
--

CREATE TABLE `force_update_models` (
  `id` int(10) UNSIGNED NOT NULL,
  `version` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'android,ios',
  `is_force_update` tinyint(4) NOT NULL DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `force_update_models`
--

INSERT INTO `force_update_models` (`id`, `version`, `version_code`, `device_type`, `is_force_update`, `status`, `created_at`, `updated_at`) VALUES
(1, '1', '1', 'android', 0, 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, '1', '1', 'ios', 0, 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `message`
--

CREATE TABLE `message` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_id` int(10) UNSIGNED DEFAULT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subtitle` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `message`
--

INSERT INTO `message` (`id`, `category_id`, `title`, `subtitle`, `body`, `status`, `created_at`, `updated_at`) VALUES
(1, NULL, 'Palm Oil Price - Closing', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 13:22:01', '2019-04-30 13:22:01'),
(2, 2, 'Palm Oil Price - Closing', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 13:30:41', '2019-04-30 13:30:41'),
(3, 3, 'Palm Oil Price - Closing', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 13:33:32', '2019-04-30 13:33:32'),
(4, 4, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 14:20:53', '2019-04-30 14:20:53'),
(5, 6, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 16:30:01', '2019-04-30 16:30:01'),
(6, NULL, 'Palm Oil Price - Closing', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 16:31:20', '2019-04-30 16:31:20'),
(7, 7, 'Glycerin Price Update', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-04-30 17:02:16', '2019-04-30 17:02:16'),
(8, 8, 'Glycerin Price Update', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 05:50:07', '2019-05-01 05:50:07'),
(9, 5, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 05:50:53', '2019-05-01 05:50:53'),
(10, 7, 'Glycerin Price Update', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 05:52:02', '2019-05-01 05:52:02'),
(11, NULL, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 05:52:38', '2019-05-01 05:52:38');
INSERT INTO `message` (`id`, `category_id`, `title`, `subtitle`, `body`, `status`, `created_at`, `updated_at`) VALUES
(12, 1, 'test', 'test', '<p><br></p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">test</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">12</td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">test</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">12</td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\">test</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\">12</td></tr></tbody></table><p><br><br></p>', 1, '2019-05-01 06:06:25', '2019-05-01 06:06:25'),
(13, 4, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 06:42:17', '2019-05-01 06:42:17'),
(14, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-01 10:11:49', '2019-05-01 10:11:49'),
(15, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-01 10:13:08', '2019-05-01 10:13:08'),
(16, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-01 10:14:07', '2019-05-01 10:14:07'),
(17, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-01 10:15:43', '2019-05-01 10:15:43'),
(18, NULL, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:28:27', '2019-05-01 15:28:27'),
(19, 1, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:29:06', '2019-05-01 15:29:06'),
(20, 2, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:29:23', '2019-05-01 15:29:23'),
(21, 3, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:29:43', '2019-05-01 15:29:43'),
(22, 4, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:30:04', '2019-05-01 15:30:04'),
(23, 5, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:30:24', '2019-05-01 15:30:24'),
(24, 5, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:30:42', '2019-05-01 15:30:42'),
(25, 5, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:31:00', '2019-05-01 15:31:00'),
(26, 6, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:31:13', '2019-05-01 15:31:13');
INSERT INTO `message` (`id`, `category_id`, `title`, `subtitle`, `body`, `status`, `created_at`, `updated_at`) VALUES
(27, 7, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:31:29', '2019-05-01 15:31:29'),
(28, 8, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:31:47', '2019-05-01 15:31:47'),
(29, 9, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 15:32:00', '2019-05-01 15:32:00'),
(30, 2, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 17:10:36', '2019-05-01 17:10:36'),
(31, 3, 'Glycerin Price Update', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-01 17:11:22', '2019-05-01 17:11:22'),
(32, 1, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-02 04:40:52', '2019-05-02 04:40:52'),
(33, 3, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-02 05:01:46', '2019-05-02 05:01:46'),
(34, 4, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-02 05:34:30', '2019-05-02 05:34:30'),
(35, 6, 'Glycerin Price Update', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-02 05:35:51', '2019-05-02 05:35:51'),
(36, 6, 'Palm Oil Price - Mid Day', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-02 05:43:52', '2019-05-02 05:43:52'),
(37, NULL, 'debug message', 'debug message', '<p>debug message from amol!<br></p>', 1, '2019-05-02 12:01:50', '2019-05-02 12:01:50'),
(38, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-03 10:21:46', '2019-05-03 10:21:46'),
(39, NULL, 'test1', 'test2', '<p>test3</p>', 1, '2019-05-03 10:22:24', '2019-05-03 10:22:24'),
(40, NULL, 'test1', 'test1', '<p>test</p>', 1, '2019-05-03 10:32:40', '2019-05-03 10:32:40'),
(41, NULL, 'testing 👋', 'testing', '<p>testing&nbsp;</p>', 1, '2019-05-03 10:46:34', '2019-05-03 10:46:34'),
(42, NULL, 'test', 'test', '<p>test</p>', 1, '2019-05-03 10:47:23', '2019-05-03 10:47:23'),
(43, 4, 'Palm Oil Price - Closing', 'FOB Malaysia', '<p><span style=\"color: rgb(0, 0, 0); font-family: Verdana; font-size: 16px;\">Closing Market - 30/04/2019</span></p><table class=\"roundedCorners\" style=\"border-collapse: separate; background-color: rgb(255, 255, 255); font-family: Verdana; font-size: 16px; color: rgb(0, 0, 0);\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"padding: 5px; text-align: center; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"padding: 5px; text-align: center; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; background: rgb(251, 157, 49); color: rgb(255, 255, 255); border-top-right-radius: 6px;\">May\' 19</th></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">525 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">520 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">485 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Palm Fatty Acid Distillates</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">435 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">2030 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">Crude Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">140 MYR</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">660 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Olein</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">570 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange;\">RBD Palm Kernel Stearin</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange;\">810 USD</td></tr><tr style=\"font-size: 1em;\"><td style=\"padding: 5px; font-size: 1em; border-width: 1px; border-style: solid; border-color: darkorange; border-bottom-left-radius: 6px;\">Indonesia Crude Palm Oil</td><td style=\"padding: 5px; font-size: 1em; border-right: 1px solid darkorange; border-top: 1px solid darkorange; border-bottom: 1px solid darkorange; border-bottom-right-radius: 6px;\">485 USD</td></tr></tbody></table>', 1, '2019-05-08 10:12:15', '2019-05-08 10:12:15');
INSERT INTO `message` (`id`, `category_id`, `title`, `subtitle`, `body`, `status`, `created_at`, `updated_at`) VALUES
(44, NULL, 'Test message', 'testing staging server message', '<p><div style=\"margin: 0px 28.7969px 0px 14.3906px; padding: 0px; width: 436.797px; text-align: left; float: right; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-style: initial; text-decoration-color: initial;\"></div></p><div style=\"margin: 0px 14.3906px 0px 28.7969px; padding: 0px; width: 436.797px; text-align: left; float: left; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-style: initial; text-decoration-color: initial;\"><p style=\"margin: 0px 0px 15px; padding: 0px; text-align: justify;\"><strong style=\"margin: 0px; padding: 0px;\">Lorem Ipsum</strong><span>&nbsp;</span>is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p></div>', 1, '2019-05-10 06:05:50', '2019-05-10 06:05:50'),
(45, NULL, 'Test MEssage', 'Testing message for chemical', '<p><div style=\"margin: 0px 28.7969px 0px 14.3906px; padding: 0px; width: 436.797px; text-align: left; float: right; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-style: initial; text-decoration-color: initial;\"></div></p><div style=\"margin: 0px 14.3906px 0px 28.7969px; padding: 0px; width: 436.797px; text-align: left; float: left; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; white-space: normal; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-style: initial; text-decoration-color: initial;\"><p style=\"margin: 0px 0px 15px; padding: 0px; text-align: justify;\"><strong style=\"margin: 0px; padding: 0px;\">Lorem Ipsum</strong><span>&nbsp;</span>is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p></div>', 1, '2019-05-10 06:07:41', '2019-05-10 06:07:41'),
(46, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-08 06:27:52', '2019-06-08 06:27:52'),
(47, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-08 06:28:09', '2019-06-08 06:28:09'),
(48, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-11 09:29:43', '2019-06-11 09:29:43'),
(49, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 09:42:23', '2019-06-12 09:42:23'),
(50, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-12 09:54:04', '2019-06-12 09:54:04'),
(51, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 09:54:30', '2019-06-12 09:54:30'),
(52, NULL, 'test', 'tese', '<p>test</p>', 1, '2019-06-12 10:48:45', '2019-06-12 10:48:45'),
(53, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 10:49:51', '2019-06-12 10:49:51'),
(54, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 10:53:26', '2019-06-12 10:53:26'),
(55, NULL, 'testtt', 'testt', '<p>testt</p>', 1, '2019-06-12 10:55:08', '2019-06-12 10:55:08'),
(56, NULL, 'tse', 'test', '<p>test</p>', 1, '2019-06-12 10:55:35', '2019-06-12 10:55:35'),
(57, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 10:55:54', '2019-06-12 10:55:54'),
(58, NULL, 'test', 'test', '<p>testt</p>', 1, '2019-06-12 10:57:10', '2019-06-12 10:57:10'),
(59, NULL, 'testtest', 'testtest', '<p>tesetst</p>', 1, '2019-06-12 10:58:13', '2019-06-12 10:58:13'),
(60, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:05:27', '2019-06-12 11:05:27'),
(61, NULL, 'testtset', 'teset', '<p>tsetset</p>', 1, '2019-06-12 11:05:55', '2019-06-12 11:05:55'),
(62, NULL, 'test1', 'test1test', '<p>testtesttest</p>', 1, '2019-06-12 11:06:57', '2019-06-12 11:06:57'),
(63, NULL, 'test', 'teste', '<p>test</p>', 1, '2019-06-12 11:08:37', '2019-06-12 11:08:37'),
(64, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:09:31', '2019-06-12 11:09:31'),
(65, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:09:59', '2019-06-12 11:09:59'),
(66, NULL, 'test', 'testtest', '<p>testtest</p>', 1, '2019-06-12 11:10:57', '2019-06-12 11:10:57'),
(67, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:11:31', '2019-06-12 11:11:31'),
(68, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:11:54', '2019-06-12 11:11:54'),
(69, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:12:36', '2019-06-12 11:12:36'),
(70, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:12:53', '2019-06-12 11:12:53'),
(71, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:13:18', '2019-06-12 11:13:18'),
(72, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:14:04', '2019-06-12 11:14:04'),
(73, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:14:23', '2019-06-12 11:14:23'),
(74, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:14:51', '2019-06-12 11:14:51'),
(75, NULL, 'test', 'test', '<p>teste</p>', 1, '2019-06-12 11:15:51', '2019-06-12 11:15:51'),
(76, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:17:34', '2019-06-12 11:17:34'),
(77, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:17:51', '2019-06-12 11:17:51'),
(78, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:20:18', '2019-06-12 11:20:18'),
(79, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:22:03', '2019-06-12 11:22:03'),
(80, NULL, 'test', 'test', '<p>tse</p>', 1, '2019-06-12 11:23:44', '2019-06-12 11:23:44'),
(81, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:39:03', '2019-06-12 11:39:03'),
(82, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:42:38', '2019-06-12 11:42:38'),
(83, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 11:43:32', '2019-06-12 11:43:32'),
(92, NULL, 'demo', 'demo', '<p>demo</p><p><br></p>', 1, '2019-06-12 12:29:17', '2019-06-12 12:29:17'),
(93, NULL, 'demo', 'demo', '<p>demo</p>', 1, '2019-06-12 12:51:05', '2019-06-12 12:51:05'),
(94, NULL, 'new', 'new', '<p><span style=\"background-color: rgb(255, 255, 0);\">new</span></p>', 1, '2019-06-12 13:13:47', '2019-06-12 13:13:47'),
(95, NULL, 'test new', 'test new', '<p><span style=\"background-color: rgb(0, 255, 255);\">gdgdgdgdg</span></p>', 1, '2019-06-12 13:16:06', '2019-06-12 13:16:06'),
(96, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 13:25:16', '2019-06-12 13:25:16'),
(97, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-12 13:25:47', '2019-06-12 13:25:47'),
(98, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 13:26:48', '2019-06-12 13:26:48'),
(99, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 13:32:55', '2019-06-12 13:32:55'),
(100, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 13:34:55', '2019-06-12 13:34:55'),
(101, NULL, 'test', 'test', '<p>tst</p>', 1, '2019-06-12 13:37:21', '2019-06-12 13:37:21'),
(102, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-12 13:37:41', '2019-06-12 13:37:41'),
(103, NULL, 'test msg', 'test dev msg', '<p>lorem ipsum dummy text</p>', 1, '2019-06-12 13:55:25', '2019-06-12 13:55:25'),
(104, 1, 'Test message', 'Test Messag Sub title', '<p>lorem ipsum dummy text</p>', 1, '2019-06-12 13:56:20', '2019-06-13 05:28:18'),
(105, NULL, 'Test web msg', 'Test web msg subtitle', '<p>lorem ipsum Dummy text</p>', 1, '2019-06-13 05:23:31', '2019-06-13 05:28:25'),
(106, 1, 'test msg', 'test subtitle', '<p>asdasdasd</p>', 1, '2019-06-13 06:01:09', '2019-06-13 06:01:09'),
(107, NULL, 'test general', 'test general subtitle', '<p><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><br></p>', 1, '2019-06-13 06:05:19', '2019-06-13 06:05:19'),
(108, NULL, 'test general one', 'test general one subtitle', '<p><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</span><br></p>', 1, '2019-06-13 06:06:24', '2019-06-13 06:06:24'),
(109, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:04:53', '2019-06-24 07:04:53'),
(110, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:05:40', '2019-06-24 07:05:40'),
(111, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:08:55', '2019-06-24 07:08:55'),
(112, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:42:55', '2019-06-24 07:42:55'),
(113, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:43:47', '2019-06-24 07:43:47'),
(114, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:44:09', '2019-06-24 07:44:09'),
(115, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:44:42', '2019-06-24 07:44:42'),
(116, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:45:16', '2019-06-24 07:45:16'),
(117, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:57:05', '2019-06-24 07:57:05'),
(118, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:58:34', '2019-06-24 07:58:34'),
(119, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 07:59:15', '2019-06-24 07:59:15'),
(120, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:00:59', '2019-06-24 08:00:59'),
(121, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:06:29', '2019-06-24 08:06:29'),
(122, NULL, 'test', 'tst', '<p>test</p>', 1, '2019-06-24 08:06:42', '2019-06-24 08:30:43'),
(123, NULL, 'testtest', 'testtest', '<p>testetst</p>', 1, '2019-06-24 08:06:57', '2019-06-24 08:06:57'),
(124, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:07:30', '2019-06-24 08:07:30'),
(125, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:14:04', '2019-06-24 08:14:04'),
(126, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:14:47', '2019-06-24 08:14:47'),
(127, NULL, 'testtest', 'testtest', '<p>testtest</p>', 1, '2019-06-24 08:21:14', '2019-06-24 08:21:14'),
(128, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:22:05', '2019-06-24 08:22:05'),
(129, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:24:35', '2019-06-24 08:24:35'),
(130, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 08:25:37', '2019-06-24 08:25:37'),
(131, NULL, 'testetst', 'test', '<p>testtest</p>', 1, '2019-06-24 08:25:49', '2019-06-24 08:25:49'),
(132, NULL, 'testets', 'test', '<p>testetst</p>', 1, '2019-06-24 08:28:34', '2019-06-24 08:28:34'),
(133, NULL, 'testtest', 'testetst', '<p>testetst</p>', 1, '2019-06-24 08:30:53', '2019-06-24 08:30:53'),
(134, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-24 10:30:29', '2019-06-24 10:30:29'),
(135, NULL, 'test', 'etst', '<p>test</p>', 1, '2019-06-24 10:31:15', '2019-06-24 10:31:15'),
(136, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 10:35:00', '2019-06-24 10:35:00'),
(137, NULL, 'test', 'testteset', '<p>test</p>', 1, '2019-06-24 10:44:49', '2019-06-24 10:44:49'),
(138, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-24 11:02:37', '2019-06-24 11:02:37'),
(139, NULL, 'test', 'testtest', '<p>testest</p>', 1, '2019-06-24 11:02:59', '2019-06-24 11:02:59'),
(140, NULL, 'test', 'test', '<p>testest</p>', 1, '2019-06-24 11:03:45', '2019-06-24 11:03:45'),
(141, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 11:04:09', '2019-06-24 11:04:09'),
(142, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 11:38:53', '2019-06-24 11:38:53'),
(143, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-24 11:39:23', '2019-06-24 11:39:23'),
(144, NULL, 'test', 'test', '<p>testest</p>', 1, '2019-06-24 11:39:42', '2019-06-24 11:39:42'),
(145, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 06:55:35', '2019-06-25 06:55:35'),
(146, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:09:39', '2019-06-25 07:09:39'),
(147, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:12:27', '2019-06-25 07:12:27'),
(148, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:20:49', '2019-06-25 07:20:49'),
(149, NULL, 'test', 'test', '<p>testteste</p>', 1, '2019-06-25 07:23:24', '2019-06-25 07:23:24'),
(150, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-25 07:24:07', '2019-06-25 07:24:07'),
(151, NULL, 'test', 'teet', '<p>testetst</p>', 1, '2019-06-25 07:24:25', '2019-06-25 07:24:25'),
(152, NULL, 'test', 'testetse', '<p>tesste</p>', 1, '2019-06-25 07:32:04', '2019-06-25 07:32:04'),
(153, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:35:40', '2019-06-25 07:35:40'),
(154, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:36:38', '2019-06-25 07:36:38'),
(155, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:36:52', '2019-06-25 07:36:52'),
(156, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:40:06', '2019-06-25 07:40:06'),
(157, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:41:10', '2019-06-25 07:41:10'),
(158, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 07:41:32', '2019-06-25 07:41:32'),
(159, NULL, 'test', 'test', '<p>testtest</p>', 1, '2019-06-25 07:52:43', '2019-06-25 07:52:43'),
(160, NULL, 'Test Title', 'Test Message Subtitle', '<p><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s</span><br></p>', 1, '2019-06-25 09:25:59', '2019-06-25 09:25:59'),
(161, NULL, 'Test Title', 'Test Message Subtitle', '<h1 class=\"modal-title text-center\" style=\"font-size: 25px; font-family: &quot;Open Sans&quot;, sans-serif; line-height: 1.53846; color: rgb(255, 255, 255);\"><span style=\"font-weight: 700; letter-spacing: normal; margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</span><span style=\"letter-spacing: normal; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummying text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s</span><br></h1>', 1, '2019-06-25 09:30:00', '2019-06-25 09:30:00'),
(162, NULL, 'Test Message', 'Test Message Subtitle', '<p><span style=\"font-weight: 700; margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</span><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s</span><br></p>', 1, '2019-06-25 09:31:23', '2019-06-25 09:31:23'),
(163, NULL, 'Test Message', 'Test Message Subtitle', '<p><span style=\"font-weight: 700; margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</span><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500</span><br></p>', 1, '2019-06-25 09:32:53', '2019-06-25 09:32:53'),
(164, NULL, 'test table', 'test table Message', '<p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;\nline-height:normal\"><span style=\"font-size:12.0pt;font-family:&quot;Times New Roman&quot;,&quot;serif&quot;;\nmso-fareast-font-family:&quot;Times New Roman&quot;\">&nbsp;</span></p><table class=\"MsoNormalTable\" cellpadding=\"0\" border=\"0\">\n <tbody><tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  background:#FB9D31;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;\n  text-align:center;line-height:normal\" align=\"center\"><b><span style=\"font-size:12.0pt;\n  font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;mso-fareast-font-family:&quot;Times New Roman&quot;;\n  mso-bidi-font-family:&quot;Times New Roman&quot;;color:white\">Product</span></b></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;background:#FB9D31;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;\n  text-align:center;line-height:normal\" align=\"center\"><b><span style=\"font-size:12.0pt;\n  font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;mso-fareast-font-family:&quot;Times New Roman&quot;;\n  mso-bidi-font-family:&quot;Times New Roman&quot;;color:white\">Price</span></b></p>\n  </td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test1</td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">100</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test1<br></td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">101</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test1<br></td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">102</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\"><p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"background-color: transparent;\">test1</span><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;</span></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;103</span></p>\n  </td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\"><p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"background-color: transparent;\">test1</span><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;</span></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;104</span></p>\n  </td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\"><p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"background-color: transparent;\">test1</span><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;</span></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\">&nbsp;105</span></p>\n  </td>\n </tr>\n</tbody></table><p>\n\n\n\n</p><p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:12.0pt;\nline-height:normal\"><span style=\"font-size:12.0pt;font-family:&quot;Times New Roman&quot;,&quot;serif&quot;;\nmso-fareast-font-family:&quot;Times New Roman&quot;\">&nbsp;</span></p>', 1, '2019-06-25 09:43:41', '2019-06-25 09:43:41'),
(165, 1, 'Test Message', 'Test Message Subtitle', '<p><strong style=\"margin: 0px; padding: 0px; color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">Lorem Ipsum</strong><span style=\"color: rgb(0, 0, 0); font-family: &quot;Open Sans&quot;, Arial, sans-serif; font-size: 14px; text-align: justify;\">&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s</span><br></p>', 1, '2019-06-25 10:30:10', '2019-06-25 10:30:10'),
(166, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 10:45:22', '2019-06-25 10:45:22'),
(167, NULL, 'test', 'testtese', '<p>testtest</p>', 1, '2019-06-25 10:46:01', '2019-06-25 10:46:01'),
(168, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 10:50:05', '2019-06-25 10:50:05'),
(169, NULL, 'time', 'Change timezone push', '<p>Change timezone push body<br></p>', 1, '2019-06-25 10:52:16', '2019-06-25 10:52:16'),
(170, NULL, 'Test message', 'Test message', '<p>Test message<br></p>', 1, '2019-06-25 11:08:44', '2019-06-25 11:08:44'),
(171, 1, 'Title', 'Message Subtitle', '<p>Message Subtitle<br></p>', 1, '2019-06-25 11:11:25', '2019-06-25 11:11:25'),
(172, 1, 'test message', 'message title', '<p>lorem ipsum&nbsp;</p>', 1, '2019-06-25 11:13:15', '2019-06-25 11:13:15'),
(173, NULL, 'asdasd', 'asdasda', '<p>asdasdasdasd</p>', 1, '2019-06-25 11:18:56', '2019-06-25 11:18:56'),
(174, NULL, 'general', 'general one', '<p>general one<br></p>', 1, '2019-06-25 11:19:48', '2019-06-25 11:19:48'),
(175, NULL, 'test push', 'message sub title', '<p>lorem ipsum</p>', 1, '2019-06-25 11:22:37', '2019-06-25 11:22:37'),
(176, NULL, 'test general', 'test general message', '<p>test general message<br></p>', 1, '2019-06-25 11:24:03', '2019-06-25 11:24:03'),
(177, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 11:29:33', '2019-06-25 11:29:33'),
(178, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 11:33:02', '2019-06-25 11:33:02'),
(179, NULL, 'test', 'test', '<p>test</p>', 1, '2019-06-25 11:33:55', '2019-06-25 11:33:55'),
(180, NULL, 'test1', 'test2', '<p>test3</p>', 1, '2019-06-25 11:36:43', '2019-06-25 11:36:43'),
(181, NULL, 'test', 'tet', '<p>testtest</p>', 1, '2019-06-25 11:37:08', '2019-06-25 11:37:08');
INSERT INTO `message` (`id`, `category_id`, `title`, `subtitle`, `body`, `status`, `created_at`, `updated_at`) VALUES
(182, NULL, 'asdas', 'asdasda', '<table class=\"MsoNormalTable\" border=\"0\" cellpadding=\"0\">\n <tbody><tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  background:#FB9D31;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom:0in;margin-bottom:.0001pt;\n  text-align:center;line-height:normal\"><b><span style=\"font-size:12.0pt;\n  font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;mso-fareast-font-family:&quot;Times New Roman&quot;;\n  mso-bidi-font-family:&quot;Times New Roman&quot;;color:white\">Product<o:p></o:p></span></b></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;background:#FB9D31;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom:0in;margin-bottom:.0001pt;\n  text-align:center;line-height:normal\"><b><span style=\"font-size:12.0pt;\n  font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;mso-fareast-font-family:&quot;Times New Roman&quot;;\n  mso-bidi-font-family:&quot;Times New Roman&quot;;color:white\">Price<o:p></o:p></span></b></p>\n  </td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test1</td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">1</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test2</td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">23</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">test3</td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">4</td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\">test4&nbsp;&nbsp;&nbsp;&nbsp;</p></td><td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"></p></td></tr><tr><td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\"><p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><br></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\"><o:p>&nbsp;</o:p></span></p>\n  </td>\n </tr>\n <tr>\n  <td style=\"border:solid darkorange 1.0pt;mso-border-alt:solid darkorange .75pt;\n  padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\"><o:p>&nbsp;</o:p></span></p>\n  </td>\n  <td style=\"border:solid darkorange 1.0pt;border-left:none;mso-border-top-alt:\n  solid darkorange .75pt;mso-border-bottom-alt:solid darkorange .75pt;\n  mso-border-right-alt:solid darkorange .75pt;padding:3.75pt 3.75pt 3.75pt 3.75pt\">\n  <p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\n  normal\"><span style=\"font-size:12.0pt;font-family:&quot;Verdana&quot;,&quot;sans-serif&quot;;\n  mso-fareast-font-family:&quot;Times New Roman&quot;;mso-bidi-font-family:&quot;Times New Roman&quot;\"><o:p>&nbsp;</o:p></span></p>\n  </td>\n </tr>\n</tbody></table>', 1, '2019-06-25 12:32:16', '2019-06-25 12:32:16'),
(183, NULL, 'fdsadsa', 'asdasdasd', '<table class=\"MsoNormalTable\" border=\"0\" cellpadding=\"0\" style=\"background-color: rgb(255, 255, 255);\"><tbody><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange; background: rgb(251, 157, 49);\"><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0.0001pt; text-align: center; line-height: normal;\"><span style=\"font-weight: 700;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif; color: white;\">Product<o:p></o:p></span></span></p></td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none; background: rgb(251, 157, 49);\"><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0.0001pt; text-align: center; line-height: normal;\"><span style=\"font-weight: 700;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif; color: white;\">Price<o:p></o:p></span></span></p></td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\">test1</td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\">5</td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\">asdas</td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\">4</td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\">asdasd</td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\">3</td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;asdasd</o:p></span></p></td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;5</o:p></span></p></td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;asdasd</o:p></span></p></td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;2</o:p></span></p></td></tr><tr><td style=\"padding: 3.75pt; border: 1pt solid darkorange;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;asdas</o:p></span></p></td><td style=\"padding: 3.75pt; border-top: 1pt solid darkorange; border-right: 1pt solid darkorange; border-bottom: 1pt solid darkorange; border-image: initial; border-left: none;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 12pt; font-family: Verdana, sans-serif;\"><o:p>&nbsp;2</o:p></span></p></td></tr></tbody></table>', 1, '2019-06-25 12:32:53', '2019-06-25 12:32:53'),
(184, NULL, 'demo', 'demo', '<p>asasadadf</p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">sds</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">1</td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">sed</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">2</td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\">sd</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\">4</td></tr></tbody></table><p><br></p>', 1, '2019-06-25 12:56:44', '2019-06-25 12:56:44'),
(185, NULL, 'new', 'new1', '<p>new1</p><p><br></p>', 1, '2019-06-25 13:28:25', '2019-06-25 08:36:44'),
(186, NULL, 'new', 'new', '<p>as</p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">ss</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">1</td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\"><br></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\"><br></td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\"></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\"><br></td></tr></tbody></table><p><br></p>', 1, '2019-06-25 08:34:38', '2019-06-25 08:34:38'),
(187, NULL, 'demo', 'demo', '<p>hfhfh</p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">11</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\"></td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\"><br></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\"><br></td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\"></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\"><br></td></tr></tbody></table><p><br></p>', 1, '2019-06-25 08:55:50', '2019-06-25 08:55:50'),
(188, NULL, 'demo5', 'demo5', '<p>rer</p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">11</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">1</td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\"><br></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\"><br></td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\"></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\"><br></td></tr></tbody></table><p><br><br></p>', 1, '2019-06-26 00:03:08', '2019-06-26 00:03:08'),
(189, NULL, 'new message', 'new message', '<p><br></p><table class=\"roundedCorners\" style=\"font-size: 16px; font-family: Verdana; border-collapse: separate;\"><tbody style=\"font-size: 1em;\"><tr style=\"font-size: 1em;\"><th style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-left-radius: 6px;\">Product</th><th style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; background-color: rgb(251, 157, 49); text-align: center; color: rgb(255, 255, 255); border-top-right-radius: 6px;\">Price</th></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\">qq1q</td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\">1</td></tr><tr><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px;\"><br></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px;\"><br></td></tr><tr style=\"font-size: 1em;\"><td style=\"font-size: 1em; border: 1px solid darkorange; padding: 5px; border-bottom-left-radius: 6px;\"></td><td style=\"font-size: 1em; border-right-width: 1px; border-right-style: solid; border-right-color: darkorange; border-top-width: 1px; border-top-style: solid; border-top-color: darkorange; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: darkorange; padding: 5px; border-bottom-right-radius: 6px;\"><br></td></tr></tbody></table><p><br><br></p>', 1, '2019-06-26 02:27:16', '2019-06-26 02:27:16');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2018_08_02_065855_create_country_table', 1),
(2, '2018_08_02_065856_create_user_table', 1),
(3, '2018_08_02_091625_create_packing_table', 1),
(4, '2018_08_02_091834_create_unit_table', 1),
(5, '2018_08_02_092052_create_delivery_term_table', 1),
(6, '2018_08_02_092201_create_category_table', 1),
(7, '2018_08_02_093217_create_currency_table', 1),
(8, '2018_08_02_093429_create_admin_table', 1),
(9, '2018_08_02_093906_create_app_content_table', 1),
(10, '2018_08_02_095812_create_user_notifications_table', 1),
(11, '2018_08_02_101919_create_social_login_table', 1),
(12, '2018_08_02_102559_create_cif_quotation_table', 1),
(13, '2018_08_02_110340_create_product_table', 1),
(14, '2018_08_02_111031_create_product_loading_port_table', 1),
(15, '2018_08_02_111640_create_ticker_table', 1),
(16, '2018_08_02_111945_create_product_documents_table', 1),
(17, '2018_08_02_112334_create_product_favourite_table', 1),
(18, '2018_08_02_112632_create_product_price_table', 1),
(19, '2018_09_06_112519_create_social_user_table', 1),
(20, '2018_09_14_133946_create_notifications_table', 1),
(21, '2018_10_08_070855_create_user_accesses_table', 1),
(22, '2018_10_27_054731_create_messages_table', 1),
(23, '2018_11_20_094728_create_user_feedbacks_table', 1),
(24, '2018_12_12_054915_create_force_update_models_table', 1),
(25, '2019_01_12_111126_create_failed_jobs_table', 1),
(26, '2019_03_23_055644_user_contact_us', 1);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `min_price` double(8,2) NOT NULL,
  `max_price` double(8,2) DEFAULT NULL,
  `price_diff` double(8,2) DEFAULT NULL,
  `message_type` tinyint(4) NOT NULL COMMENT '1 = All',
  `is_read` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 = No, 1 = Yes',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `product_id`, `message`, `min_price`, `max_price`, `price_diff`, `message_type`, `is_read`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'Product Price Updated!', 525.00, NULL, -1.00, 1, 0, 1, '2019-04-30 14:20:18', '2019-04-30 14:20:18'),
(2, 1, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:29:03', '2019-04-30 16:29:03'),
(3, 4, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:29:03', '2019-04-30 16:29:03'),
(4, 4, 2, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:29:04', '2019-04-30 16:29:04'),
(5, 4, 5, 'Product Price Updated!', 610.00, 660.00, 5.00, 1, 0, 1, '2019-04-30 16:32:09', '2019-04-30 16:32:09'),
(6, 1, 1, 'Product Price Updated!', 520.00, NULL, -10.00, 1, 0, 1, '2019-04-30 16:35:25', '2019-04-30 16:35:25'),
(7, 4, 1, 'Product Price Updated!', 520.00, NULL, -10.00, 1, 0, 1, '2019-04-30 16:35:25', '2019-04-30 16:35:25'),
(8, 4, 2, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:35:26', '2019-04-30 16:35:26'),
(9, 4, 3, 'Product Price Updated!', 480.00, NULL, -5.00, 1, 0, 1, '2019-04-30 16:35:26', '2019-04-30 16:35:26'),
(10, 4, 7, 'Product Price Updated!', 430.00, NULL, -7.50, 1, 0, 1, '2019-04-30 16:35:27', '2019-04-30 16:35:27'),
(11, 4, 8, 'Product Price Updated!', 670.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:35:27', '2019-04-30 16:35:27'),
(12, 4, 10, 'Product Price Updated!', 140.00, NULL, -1.00, 1, 0, 1, '2019-04-30 16:35:27', '2019-04-30 16:35:27'),
(13, 4, 11, 'Product Price Updated!', 580.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:35:28', '2019-04-30 16:35:28'),
(14, 4, 12, 'Product Price Updated!', 800.00, NULL, -10.00, 1, 0, 1, '2019-04-30 16:35:28', '2019-04-30 16:35:28'),
(15, 4, 13, 'Product Price Updated!', 490.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:35:28', '2019-04-30 16:35:28'),
(16, 1, 1, 'Product Price Updated!', 525.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:38:32', '2019-04-30 16:38:32'),
(17, 4, 1, 'Product Price Updated!', 525.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:38:32', '2019-04-30 16:38:32'),
(18, 4, 2, 'Product Price Updated!', 530.00, NULL, -10.00, 1, 0, 1, '2019-04-30 16:38:33', '2019-04-30 16:38:33'),
(19, 4, 3, 'Product Price Updated!', 485.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:38:33', '2019-04-30 16:38:33'),
(20, 4, 7, 'Product Price Updated!', 440.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:38:33', '2019-04-30 16:38:33'),
(21, 4, 8, 'Product Price Updated!', 680.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:38:34', '2019-04-30 16:38:34'),
(22, 4, 10, 'Product Price Updated!', 145.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:38:34', '2019-04-30 16:38:34'),
(23, 4, 11, 'Product Price Updated!', 585.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:38:34', '2019-04-30 16:38:34'),
(24, 4, 12, 'Product Price Updated!', 810.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:38:35', '2019-04-30 16:38:35'),
(25, 4, 13, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:38:35', '2019-04-30 16:38:35'),
(26, 4, 10, 'Product Price Updated!', 140.00, NULL, -5.00, 1, 0, 1, '2019-04-30 16:42:21', '2019-04-30 16:42:21'),
(27, 4, 12, 'Product Price Updated!', 800.00, NULL, -10.00, 1, 0, 1, '2019-04-30 16:42:43', '2019-04-30 16:42:43'),
(28, 4, 2, 'Product Price Updated!', 525.00, NULL, -5.00, 1, 0, 1, '2019-04-30 16:42:57', '2019-04-30 16:42:57'),
(29, 4, 3, 'Product Price Updated!', 490.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:43:14', '2019-04-30 16:43:14'),
(30, 1, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:43:26', '2019-04-30 16:43:26'),
(31, 4, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:43:26', '2019-04-30 16:43:26'),
(32, 4, 7, 'Product Price Updated!', 445.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:44:35', '2019-04-30 16:44:35'),
(33, 4, 13, 'Product Price Updated!', 495.00, NULL, -5.00, 1, 0, 1, '2019-04-30 16:44:59', '2019-04-30 16:44:59'),
(34, 4, 11, 'Product Price Updated!', 590.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:45:15', '2019-04-30 16:45:15'),
(35, 4, 8, 'Product Price Updated!', 685.00, NULL, 5.00, 1, 0, 1, '2019-04-30 16:45:29', '2019-04-30 16:45:29'),
(36, 4, 5, 'Product Price Updated!', 610.00, 630.00, -15.00, 1, 0, 1, '2019-04-30 16:45:54', '2019-04-30 16:45:54'),
(37, 4, 5, 'Product Price Updated!', 610.00, 640.00, 5.00, 1, 0, 1, '2019-04-30 16:46:13', '2019-04-30 16:46:13'),
(38, 1, 1, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:46:54', '2019-04-30 16:46:54'),
(39, 4, 1, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-04-30 16:46:54', '2019-04-30 16:46:54'),
(40, 4, 6, 'Product Price Updated!', 620.00, 640.00, 15.00, 1, 0, 1, '2019-04-30 16:47:46', '2019-04-30 16:47:46'),
(41, 4, 6, 'Product Price Updated!', 630.00, 650.00, 10.00, 1, 0, 1, '2019-04-30 16:48:20', '2019-04-30 16:48:20'),
(42, 4, 6, 'Product Price Updated!', 630.00, 640.00, -5.00, 1, 0, 1, '2019-04-30 16:49:00', '2019-04-30 16:49:00'),
(43, 4, 6, 'Product Price Updated!', 600.00, 630.00, -20.00, 1, 0, 1, '2019-04-30 16:49:48', '2019-04-30 16:49:48'),
(44, 4, 4, 'Product Price Updated!', 900.00, 940.00, -5.00, 1, 0, 1, '2019-04-30 16:50:51', '2019-04-30 16:50:51'),
(45, 4, 4, 'Product Price Updated!', 850.00, 900.00, -45.00, 1, 0, 1, '2019-04-30 16:51:21', '2019-04-30 16:51:21'),
(46, 4, 14, 'Product Price Updated!', 720.00, 760.00, 5.00, 1, 0, 1, '2019-04-30 16:52:18', '2019-04-30 16:52:18'),
(47, 4, 14, 'Product Price Updated!', 700.00, 730.00, -25.00, 1, 0, 1, '2019-04-30 16:52:48', '2019-04-30 16:52:48'),
(48, 4, 18, 'Product Price Updated!', 1000.00, 1040.00, -5.00, 1, 0, 1, '2019-04-30 16:54:11', '2019-04-30 16:54:11'),
(49, 4, 18, 'Product Price Updated!', 990.00, 1030.00, -10.00, 1, 0, 1, '2019-04-30 16:54:46', '2019-04-30 16:54:46'),
(50, 4, 15, 'Product Price Updated!', 64.05, NULL, -0.07, 1, 0, 1, '2019-04-30 16:55:16', '2019-04-30 16:55:16'),
(51, 4, 15, 'Product Price Updated!', 64.07, NULL, 0.02, 1, 0, 1, '2019-04-30 16:56:11', '2019-04-30 16:56:11'),
(52, 4, 15, 'Product Price Updated!', 64.10, NULL, 0.03, 1, 0, 1, '2019-04-30 16:58:02', '2019-04-30 16:58:02'),
(53, 4, 15, 'Product Price Updated!', 64.03, NULL, -0.07, 1, 0, 1, '2019-04-30 16:59:17', '2019-04-30 16:59:17'),
(54, 4, 16, 'Product Price Updated!', 74.05, NULL, 1.05, 1, 0, 1, '2019-04-30 16:59:17', '2019-04-30 16:59:17'),
(55, 4, 16, 'Product Price Updated!', 74.08, NULL, 0.03, 1, 0, 1, '2019-04-30 16:59:53', '2019-04-30 16:59:53'),
(56, 4, 9, 'Product Price Updated!', 2050.00, NULL, 40.00, 1, 0, 1, '2019-04-30 17:00:26', '2019-04-30 17:00:26'),
(57, 4, 9, 'Product Price Updated!', 2010.00, NULL, -40.00, 1, 0, 1, '2019-04-30 17:01:01', '2019-04-30 17:01:01'),
(58, 1, 1, 'Product Price Updated!', 525.00, NULL, -15.00, 1, 0, 1, '2019-05-01 05:35:18', '2019-05-01 05:35:18'),
(59, 4, 1, 'Product Price Updated!', 525.00, NULL, -15.00, 1, 0, 1, '2019-05-01 05:35:19', '2019-05-01 05:35:19'),
(60, 4, 2, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-05-01 05:35:19', '2019-05-01 05:35:19'),
(61, 1, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-05-01 05:36:37', '2019-05-01 05:36:37'),
(62, 4, 1, 'Product Price Updated!', 530.00, NULL, 5.00, 1, 0, 1, '2019-05-01 05:36:37', '2019-05-01 05:36:37'),
(63, 4, 2, 'Product Price Updated!', 525.00, NULL, -5.00, 1, 0, 1, '2019-05-01 05:37:11', '2019-05-01 05:37:11'),
(64, 4, 5, 'Product Price Updated!', 610.00, 650.00, 5.00, 1, 0, 1, '2019-05-01 05:37:54', '2019-05-01 05:37:54'),
(65, 4, 5, 'Product Price Updated!', 600.00, 640.00, -10.00, 1, 0, 1, '2019-05-01 05:39:07', '2019-05-01 05:39:07'),
(66, 4, 2, 'Product Price Updated!', 524.00, NULL, -1.00, 1, 0, 1, '2019-05-01 06:58:32', '2019-05-01 06:58:32'),
(67, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:11:14', '2019-05-01 07:11:14'),
(68, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:11:15', '2019-05-01 07:11:15'),
(69, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:11:38', '2019-05-01 07:11:38'),
(70, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:11:38', '2019-05-01 07:11:38'),
(71, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:31:28', '2019-05-01 07:31:28'),
(72, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:31:57', '2019-05-01 07:31:57'),
(73, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:32:30', '2019-05-01 07:32:30'),
(74, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:32:30', '2019-05-01 07:32:30'),
(75, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:32:56', '2019-05-01 07:32:56'),
(76, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:32:56', '2019-05-01 07:32:56'),
(77, 1, 1, 'Product Price Updated!', 532.00, NULL, 2.00, 1, 0, 1, '2019-05-01 07:37:55', '2019-05-01 07:37:55'),
(78, 4, 1, 'Product Price Updated!', 532.00, NULL, 2.00, 1, 0, 1, '2019-05-01 07:37:56', '2019-05-01 07:37:56'),
(79, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:48:46', '2019-05-01 07:48:46'),
(80, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:48:47', '2019-05-01 07:48:47'),
(81, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:52:56', '2019-05-01 07:52:56'),
(82, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:52:57', '2019-05-01 07:52:57'),
(83, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:53:14', '2019-05-01 07:53:14'),
(84, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:53:14', '2019-05-01 07:53:14'),
(85, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:53:33', '2019-05-01 07:53:33'),
(86, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 07:53:34', '2019-05-01 07:53:34'),
(87, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:59:53', '2019-05-01 07:59:53'),
(88, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 07:59:54', '2019-05-01 07:59:54'),
(89, 1, 1, 'Product Price Updated!', 534.00, NULL, 3.00, 1, 0, 1, '2019-05-01 08:00:23', '2019-05-01 08:00:23'),
(90, 4, 1, 'Product Price Updated!', 534.00, NULL, 3.00, 1, 0, 1, '2019-05-01 08:00:23', '2019-05-01 08:00:23'),
(91, 1, 1, 'Product Price Updated!', 532.00, NULL, -2.00, 1, 0, 1, '2019-05-01 08:00:54', '2019-05-01 08:00:54'),
(92, 4, 1, 'Product Price Updated!', 532.00, NULL, -2.00, 1, 0, 1, '2019-05-01 08:00:54', '2019-05-01 08:00:54'),
(93, 1, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 08:05:40', '2019-05-01 08:05:40'),
(94, 4, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 08:05:40', '2019-05-01 08:05:40'),
(95, 4, 2, 'Product Price Updated!', 523.00, NULL, -1.00, 1, 0, 1, '2019-05-01 08:07:49', '2019-05-01 08:07:49'),
(96, 1, 2, 'Product Price Updated!', 523.00, NULL, -1.00, 1, 0, 1, '2019-05-01 08:07:49', '2019-05-01 08:07:49'),
(97, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 08:08:31', '2019-05-01 08:08:31'),
(98, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 08:08:31', '2019-05-01 08:08:31'),
(99, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 08:52:49', '2019-05-01 08:52:49'),
(100, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 08:52:49', '2019-05-01 08:52:49'),
(101, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:51:12', '2019-05-01 09:51:12'),
(102, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:51:12', '2019-05-01 09:51:12'),
(103, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:51:13', '2019-05-01 09:51:13'),
(104, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:51:42', '2019-05-01 09:51:42'),
(105, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:51:42', '2019-05-01 09:51:42'),
(106, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:51:42', '2019-05-01 09:51:42'),
(107, 1, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:51:59', '2019-05-01 09:51:59'),
(108, 4, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:52:00', '2019-05-01 09:52:00'),
(109, 6, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:52:00', '2019-05-01 09:52:00'),
(110, 1, 1, 'Product Price Updated!', 536.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:52:20', '2019-05-01 09:52:20'),
(111, 4, 1, 'Product Price Updated!', 536.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:52:20', '2019-05-01 09:52:20'),
(112, 6, 1, 'Product Price Updated!', 536.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:52:20', '2019-05-01 09:52:20'),
(113, 1, 1, 'Product Price Updated!', 532.00, NULL, -4.00, 1, 0, 1, '2019-05-01 09:54:16', '2019-05-01 09:54:16'),
(114, 4, 1, 'Product Price Updated!', 532.00, NULL, -4.00, 1, 0, 1, '2019-05-01 09:54:16', '2019-05-01 09:54:16'),
(115, 6, 1, 'Product Price Updated!', 532.00, NULL, -4.00, 1, 0, 1, '2019-05-01 09:54:16', '2019-05-01 09:54:16'),
(116, 1, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:54:43', '2019-05-01 09:54:43'),
(117, 4, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:54:43', '2019-05-01 09:54:43'),
(118, 6, 1, 'Product Price Updated!', 534.00, NULL, 2.00, 1, 0, 1, '2019-05-01 09:54:43', '2019-05-01 09:54:43'),
(119, 1, 1, 'Product Price Updated!', 532.00, NULL, -2.00, 1, 0, 1, '2019-05-01 09:55:01', '2019-05-01 09:55:01'),
(120, 4, 1, 'Product Price Updated!', 532.00, NULL, -2.00, 1, 0, 1, '2019-05-01 09:55:02', '2019-05-01 09:55:02'),
(121, 6, 1, 'Product Price Updated!', 532.00, NULL, -2.00, 1, 0, 1, '2019-05-01 09:55:02', '2019-05-01 09:55:02'),
(122, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:55:19', '2019-05-01 09:55:19'),
(123, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:55:19', '2019-05-01 09:55:19'),
(124, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:55:20', '2019-05-01 09:55:20'),
(125, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:55:54', '2019-05-01 09:55:54'),
(126, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:55:54', '2019-05-01 09:55:54'),
(127, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:55:54', '2019-05-01 09:55:54'),
(128, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:56:20', '2019-05-01 09:56:20'),
(129, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:56:20', '2019-05-01 09:56:20'),
(130, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 09:56:20', '2019-05-01 09:56:20'),
(131, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:56:53', '2019-05-01 09:56:53'),
(132, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:56:53', '2019-05-01 09:56:53'),
(133, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 09:56:53', '2019-05-01 09:56:53'),
(134, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:11:12', '2019-05-01 10:11:12'),
(135, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:11:12', '2019-05-01 10:11:12'),
(136, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:11:12', '2019-05-01 10:11:12'),
(137, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:28:11', '2019-05-01 10:28:11'),
(138, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:28:11', '2019-05-01 10:28:11'),
(139, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:28:11', '2019-05-01 10:28:11'),
(140, 1, 1, 'Product Price Updated!', 530.00, NULL, -2.00, 1, 0, 1, '2019-05-01 10:29:59', '2019-05-01 10:29:59'),
(141, 4, 1, 'Product Price Updated!', 530.00, NULL, -2.00, 1, 0, 1, '2019-05-01 10:29:59', '2019-05-01 10:29:59'),
(142, 6, 1, 'Product Price Updated!', 530.00, NULL, -2.00, 1, 0, 1, '2019-05-01 10:30:00', '2019-05-01 10:30:00'),
(143, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:34:55', '2019-05-01 10:34:55'),
(144, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:34:55', '2019-05-01 10:34:55'),
(145, 6, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:34:56', '2019-05-01 10:34:56'),
(146, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:38:02', '2019-05-01 10:38:02'),
(147, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:38:02', '2019-05-01 10:38:02'),
(148, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:38:03', '2019-05-01 10:38:03'),
(149, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:38:52', '2019-05-01 10:38:52'),
(150, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:38:52', '2019-05-01 10:38:52'),
(151, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:38:53', '2019-05-01 10:38:53'),
(152, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:39:58', '2019-05-01 10:39:58'),
(153, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:39:58', '2019-05-01 10:39:58'),
(154, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:39:58', '2019-05-01 10:39:58'),
(155, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:40:59', '2019-05-01 10:40:59'),
(156, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:40:59', '2019-05-01 10:40:59'),
(157, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:40:59', '2019-05-01 10:40:59'),
(158, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:45:34', '2019-05-01 10:45:34'),
(159, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:45:34', '2019-05-01 10:45:34'),
(160, 6, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:45:34', '2019-05-01 10:45:34'),
(161, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:46:57', '2019-05-01 10:46:57'),
(162, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:46:57', '2019-05-01 10:46:57'),
(163, 6, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:46:57', '2019-05-01 10:46:57'),
(164, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:23', '2019-05-01 10:57:23'),
(165, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:23', '2019-05-01 10:57:23'),
(166, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:23', '2019-05-01 10:57:23'),
(167, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:57:38', '2019-05-01 10:57:38'),
(168, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:57:38', '2019-05-01 10:57:38'),
(169, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 10:57:39', '2019-05-01 10:57:39'),
(170, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:59', '2019-05-01 10:57:59'),
(171, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:59', '2019-05-01 10:57:59'),
(172, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 10:57:59', '2019-05-01 10:57:59'),
(173, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:02:04', '2019-05-01 11:02:04'),
(174, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:02:05', '2019-05-01 11:02:05'),
(175, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:02:05', '2019-05-01 11:02:05'),
(176, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:05:21', '2019-05-01 11:05:21'),
(177, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:05:21', '2019-05-01 11:05:21'),
(178, 6, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:05:21', '2019-05-01 11:05:21'),
(179, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:05:50', '2019-05-01 11:05:50'),
(180, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:05:50', '2019-05-01 11:05:50'),
(181, 6, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:05:51', '2019-05-01 11:05:51'),
(182, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:06:11', '2019-05-01 11:06:11'),
(183, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:06:11', '2019-05-01 11:06:11'),
(184, 6, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:06:11', '2019-05-01 11:06:11'),
(185, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:16:37', '2019-05-01 11:16:37'),
(186, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:16:38', '2019-05-01 11:16:38'),
(187, 6, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:16:38', '2019-05-01 11:16:38'),
(188, 1, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:17:05', '2019-05-01 11:17:05'),
(189, 4, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:17:05', '2019-05-01 11:17:05'),
(190, 6, 1, 'Product Price Updated!', 530.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:17:05', '2019-05-01 11:17:05'),
(191, 1, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:19:32', '2019-05-01 11:19:32'),
(192, 4, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:19:33', '2019-05-01 11:19:33'),
(193, 6, 1, 'Product Price Updated!', 531.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:19:33', '2019-05-01 11:19:33'),
(194, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:33:13', '2019-05-01 11:33:13'),
(195, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:33:13', '2019-05-01 11:33:13'),
(196, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:33:13', '2019-05-01 11:33:13'),
(197, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:34:04', '2019-05-01 11:34:04'),
(198, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:34:04', '2019-05-01 11:34:04'),
(199, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:34:05', '2019-05-01 11:34:05'),
(200, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:34:40', '2019-05-01 11:34:40'),
(201, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:34:40', '2019-05-01 11:34:40'),
(202, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:34:40', '2019-05-01 11:34:40'),
(203, 1, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 11:38:18', '2019-05-01 11:38:18'),
(204, 4, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 11:38:18', '2019-05-01 11:38:18'),
(205, 6, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 11:38:19', '2019-05-01 11:38:19'),
(206, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:39:21', '2019-05-01 11:39:21'),
(207, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:39:21', '2019-05-01 11:39:21'),
(208, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:39:21', '2019-05-01 11:39:21'),
(209, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:40:09', '2019-05-01 11:40:09'),
(210, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:40:09', '2019-05-01 11:40:09'),
(211, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 11:40:10', '2019-05-01 11:40:10'),
(212, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:40:21', '2019-05-01 11:40:21'),
(213, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:40:21', '2019-05-01 11:40:21'),
(214, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 11:40:22', '2019-05-01 11:40:22'),
(215, 1, 1, 'Product Price Updated!', 534.00, NULL, 3.00, 1, 0, 1, '2019-05-01 11:40:35', '2019-05-01 11:40:35'),
(216, 4, 1, 'Product Price Updated!', 534.00, NULL, 3.00, 1, 0, 1, '2019-05-01 11:40:35', '2019-05-01 11:40:35'),
(217, 6, 1, 'Product Price Updated!', 534.00, NULL, 3.00, 1, 0, 1, '2019-05-01 11:40:35', '2019-05-01 11:40:35'),
(218, 4, 1, 'Product Price Updated!', 540.00, NULL, 6.00, 1, 0, 1, '2019-05-01 12:05:01', '2019-05-01 12:05:01'),
(219, 6, 1, 'Product Price Updated!', 540.00, NULL, 6.00, 1, 0, 1, '2019-05-01 12:05:01', '2019-05-01 12:05:01'),
(220, 1, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:40:22', '2019-05-01 12:40:22'),
(221, 4, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:40:23', '2019-05-01 12:40:23'),
(222, 6, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:40:23', '2019-05-01 12:40:23'),
(223, 1, 1, 'Product Price Updated!', 531.00, NULL, -10.00, 1, 0, 1, '2019-05-01 12:40:48', '2019-05-01 12:40:48'),
(224, 4, 1, 'Product Price Updated!', 531.00, NULL, -10.00, 1, 0, 1, '2019-05-01 12:40:48', '2019-05-01 12:40:48'),
(225, 6, 1, 'Product Price Updated!', 531.00, NULL, -10.00, 1, 0, 1, '2019-05-01 12:40:48', '2019-05-01 12:40:48'),
(226, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:41:22', '2019-05-01 12:41:22'),
(227, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:41:23', '2019-05-01 12:41:23'),
(228, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:41:23', '2019-05-01 12:41:23'),
(229, 1, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 12:42:01', '2019-05-01 12:42:01'),
(230, 4, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 12:42:01', '2019-05-01 12:42:01'),
(231, 6, 1, 'Product Price Updated!', 531.00, NULL, -1.00, 1, 0, 1, '2019-05-01 12:42:01', '2019-05-01 12:42:01'),
(232, 1, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:42:11', '2019-05-01 12:42:11'),
(233, 4, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:42:11', '2019-05-01 12:42:11'),
(234, 6, 1, 'Product Price Updated!', 532.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:42:11', '2019-05-01 12:42:11'),
(235, 1, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 12:42:49', '2019-05-01 12:42:49'),
(236, 4, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 12:42:50', '2019-05-01 12:42:50'),
(237, 6, 1, 'Product Price Updated!', 532.00, NULL, 0.00, 1, 0, 1, '2019-05-01 12:42:50', '2019-05-01 12:42:50'),
(238, 4, 12, 'Product Price Updated!', 801.00, NULL, 1.00, 1, 0, 1, '2019-05-01 12:59:35', '2019-05-01 12:59:35'),
(239, 4, 7, 'Product Price Updated!', 449.00, NULL, 4.00, 1, 0, 1, '2019-05-01 13:03:36', '2019-05-01 13:03:36'),
(240, 1, 7, 'Product Price Updated!', 449.00, NULL, 4.00, 1, 0, 1, '2019-05-01 13:03:36', '2019-05-01 13:03:36'),
(241, 6, 7, 'Product Price Updated!', 449.00, NULL, 4.00, 1, 0, 1, '2019-05-01 13:03:36', '2019-05-01 13:03:36'),
(242, 4, 12, 'Product Price Updated!', 50.00, NULL, -751.00, 1, 0, 1, '2019-05-01 13:12:32', '2019-05-01 13:12:32'),
(243, 4, 12, 'Product Price Updated!', 800.00, NULL, 750.00, 1, 0, 1, '2019-05-01 13:15:00', '2019-05-01 13:15:00'),
(244, 1, 12, 'Product Price Updated!', 800.00, NULL, 750.00, 1, 0, 1, '2019-05-01 13:15:00', '2019-05-01 13:15:00'),
(245, 1, 1, 'Product Price Updated!', 540.00, NULL, 8.00, 1, 0, 1, '2019-05-01 14:37:29', '2019-05-01 14:37:29'),
(246, 4, 1, 'Product Price Updated!', 540.00, NULL, 8.00, 1, 0, 1, '2019-05-01 14:37:29', '2019-05-01 14:37:29'),
(247, 6, 1, 'Product Price Updated!', 540.00, NULL, 8.00, 1, 0, 1, '2019-05-01 14:37:30', '2019-05-01 14:37:30'),
(248, 4, 2, 'Product Price Updated!', 530.00, NULL, 7.00, 1, 0, 1, '2019-05-01 14:37:30', '2019-05-01 14:37:30'),
(249, 1, 2, 'Product Price Updated!', 530.00, NULL, 7.00, 1, 0, 1, '2019-05-01 14:37:30', '2019-05-01 14:37:30'),
(250, 6, 2, 'Product Price Updated!', 530.00, NULL, 7.00, 1, 0, 1, '2019-05-01 14:37:31', '2019-05-01 14:37:31'),
(251, 1, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 14:50:08', '2019-05-01 14:50:08'),
(252, 4, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 14:50:09', '2019-05-01 14:50:09'),
(253, 6, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 14:50:09', '2019-05-01 14:50:09'),
(254, 4, 2, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:09', '2019-05-01 14:50:09'),
(255, 1, 2, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:09', '2019-05-01 14:50:09'),
(256, 6, 2, 'Product Price Updated!', 540.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(257, 4, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(258, 1, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(259, 6, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(260, 4, 7, 'Product Price Updated!', 450.00, NULL, 1.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(261, 1, 7, 'Product Price Updated!', 450.00, NULL, 1.00, 1, 0, 1, '2019-05-01 14:50:10', '2019-05-01 14:50:10'),
(262, 6, 7, 'Product Price Updated!', 450.00, NULL, 1.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(263, 4, 8, 'Product Price Updated!', 670.00, NULL, -15.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(264, 1, 8, 'Product Price Updated!', 670.00, NULL, -15.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(265, 4, 9, 'Product Price Updated!', 2020.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(266, 1, 9, 'Product Price Updated!', 2020.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(267, 4, 10, 'Product Price Updated!', 139.00, NULL, -1.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(268, 1, 10, 'Product Price Updated!', 139.00, NULL, -1.00, 1, 0, 1, '2019-05-01 14:50:11', '2019-05-01 14:50:11'),
(269, 4, 11, 'Product Price Updated!', 600.00, NULL, 10.00, 1, 0, 1, '2019-05-01 14:50:12', '2019-05-01 14:50:12'),
(270, 4, 12, 'Product Price Updated!', 790.00, NULL, -10.00, 1, 0, 1, '2019-05-01 14:50:12', '2019-05-01 14:50:12'),
(271, 1, 12, 'Product Price Updated!', 790.00, NULL, -10.00, 1, 0, 1, '2019-05-01 14:50:12', '2019-05-01 14:50:12'),
(272, 4, 13, 'Product Price Updated!', 500.00, NULL, 5.00, 1, 0, 1, '2019-05-01 14:50:12', '2019-05-01 14:50:12'),
(273, 6, 13, 'Product Price Updated!', 500.00, NULL, 5.00, 1, 0, 1, '2019-05-01 14:50:12', '2019-05-01 14:50:12'),
(274, 1, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-01 15:05:18', '2019-05-01 15:05:18'),
(275, 4, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-01 15:05:18', '2019-05-01 15:05:18'),
(276, 6, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-01 15:05:18', '2019-05-01 15:05:18'),
(277, 8, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-01 15:05:18', '2019-05-01 15:05:18'),
(278, 4, 2, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:19', '2019-05-01 15:05:19'),
(279, 1, 2, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:19', '2019-05-01 15:05:19'),
(280, 6, 2, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:19', '2019-05-01 15:05:19'),
(281, 8, 2, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(282, 4, 3, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(283, 1, 3, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(284, 6, 3, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(285, 8, 3, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(286, 4, 7, 'Product Price Updated!', 445.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(287, 1, 7, 'Product Price Updated!', 445.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(288, 6, 7, 'Product Price Updated!', 445.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:20', '2019-05-01 15:05:20'),
(289, 8, 7, 'Product Price Updated!', 445.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:05:21', '2019-05-01 15:05:21'),
(290, 4, 8, 'Product Price Updated!', 660.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:21', '2019-05-01 15:05:21'),
(291, 1, 8, 'Product Price Updated!', 660.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:21', '2019-05-01 15:05:21'),
(292, 8, 8, 'Product Price Updated!', 660.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(293, 4, 9, 'Product Price Updated!', 2010.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(294, 1, 9, 'Product Price Updated!', 2010.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(295, 8, 9, 'Product Price Updated!', 2010.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(296, 4, 10, 'Product Price Updated!', 140.00, NULL, 1.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(297, 1, 10, 'Product Price Updated!', 140.00, NULL, 1.00, 1, 0, 1, '2019-05-01 15:05:22', '2019-05-01 15:05:22'),
(298, 8, 10, 'Product Price Updated!', 140.00, NULL, 1.00, 1, 0, 1, '2019-05-01 15:05:23', '2019-05-01 15:05:23'),
(299, 4, 11, 'Product Price Updated!', 590.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:23', '2019-05-01 15:05:23'),
(300, 8, 11, 'Product Price Updated!', 590.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:23', '2019-05-01 15:05:23'),
(301, 4, 12, 'Product Price Updated!', 780.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:23', '2019-05-01 15:05:23'),
(302, 1, 12, 'Product Price Updated!', 780.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:23', '2019-05-01 15:05:23'),
(303, 8, 12, 'Product Price Updated!', 780.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:24', '2019-05-01 15:05:24'),
(304, 4, 13, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:24', '2019-05-01 15:05:24'),
(305, 6, 13, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:24', '2019-05-01 15:05:24'),
(306, 8, 13, 'Product Price Updated!', 490.00, NULL, -10.00, 1, 0, 1, '2019-05-01 15:05:24', '2019-05-01 15:05:24'),
(307, 4, 5, 'Product Price Updated!', 600.00, 630.00, -5.00, 1, 0, 1, '2019-05-01 15:09:12', '2019-05-01 15:09:12'),
(308, 8, 5, 'Product Price Updated!', 600.00, 630.00, -5.00, 1, 0, 1, '2019-05-01 15:09:12', '2019-05-01 15:09:12'),
(309, 4, 5, 'Product Price Updated!', 600.00, 640.00, 5.00, 1, 0, 1, '2019-05-01 15:09:31', '2019-05-01 15:09:31'),
(310, 8, 5, 'Product Price Updated!', 600.00, 640.00, 5.00, 1, 0, 1, '2019-05-01 15:09:31', '2019-05-01 15:09:31'),
(311, 4, 6, 'Product Price Updated!', 600.00, 640.00, 5.00, 1, 0, 1, '2019-05-01 15:09:46', '2019-05-01 15:09:46'),
(312, 8, 6, 'Product Price Updated!', 600.00, 640.00, 5.00, 1, 0, 1, '2019-05-01 15:09:46', '2019-05-01 15:09:46'),
(313, 4, 6, 'Product Price Updated!', 600.00, 630.00, -5.00, 1, 0, 1, '2019-05-01 15:10:05', '2019-05-01 15:10:05'),
(314, 8, 6, 'Product Price Updated!', 600.00, 630.00, -5.00, 1, 0, 1, '2019-05-01 15:10:05', '2019-05-01 15:10:05'),
(315, 4, 4, 'Product Price Updated!', 850.00, 910.00, 5.00, 1, 0, 1, '2019-05-01 15:10:26', '2019-05-01 15:10:26'),
(316, 8, 4, 'Product Price Updated!', 850.00, 910.00, 5.00, 1, 0, 1, '2019-05-01 15:10:26', '2019-05-01 15:10:26'),
(317, 4, 4, 'Product Price Updated!', 850.00, 900.00, -5.00, 1, 0, 1, '2019-05-01 15:10:53', '2019-05-01 15:10:53'),
(318, 8, 4, 'Product Price Updated!', 850.00, 900.00, -5.00, 1, 0, 1, '2019-05-01 15:10:53', '2019-05-01 15:10:53'),
(319, 4, 4, 'Product Price Updated!', 850.00, 920.00, 10.00, 1, 0, 1, '2019-05-01 15:11:28', '2019-05-01 15:11:28'),
(320, 8, 4, 'Product Price Updated!', 850.00, 920.00, 10.00, 1, 0, 1, '2019-05-01 15:11:28', '2019-05-01 15:11:28'),
(321, 4, 14, 'Product Price Updated!', 700.00, 740.00, 5.00, 1, 0, 1, '2019-05-01 15:12:03', '2019-05-01 15:12:03'),
(322, 8, 14, 'Product Price Updated!', 700.00, 740.00, 5.00, 1, 0, 1, '2019-05-01 15:12:03', '2019-05-01 15:12:03'),
(323, 4, 14, 'Product Price Updated!', 700.00, 730.00, -5.00, 1, 0, 1, '2019-05-01 15:12:21', '2019-05-01 15:12:21'),
(324, 8, 14, 'Product Price Updated!', 700.00, 730.00, -5.00, 1, 0, 1, '2019-05-01 15:12:21', '2019-05-01 15:12:21'),
(325, 4, 18, 'Product Price Updated!', 990.00, 1020.00, -5.00, 1, 0, 1, '2019-05-01 15:12:36', '2019-05-01 15:12:36'),
(326, 8, 18, 'Product Price Updated!', 990.00, 1020.00, -5.00, 1, 0, 1, '2019-05-01 15:12:36', '2019-05-01 15:12:36'),
(327, 4, 18, 'Product Price Updated!', 990.00, 1030.00, 5.00, 1, 0, 1, '2019-05-01 15:13:10', '2019-05-01 15:13:10'),
(328, 8, 18, 'Product Price Updated!', 990.00, 1030.00, 5.00, 1, 0, 1, '2019-05-01 15:13:10', '2019-05-01 15:13:10'),
(329, 4, 15, 'Product Price Updated!', 64.00, NULL, -0.03, 1, 0, 1, '2019-05-01 15:13:32', '2019-05-01 15:13:32'),
(330, 8, 15, 'Product Price Updated!', 64.00, NULL, -0.03, 1, 0, 1, '2019-05-01 15:13:32', '2019-05-01 15:13:32'),
(331, 4, 16, 'Product Price Updated!', 74.01, NULL, -0.07, 1, 0, 1, '2019-05-01 15:13:32', '2019-05-01 15:13:32'),
(332, 8, 16, 'Product Price Updated!', 74.01, NULL, -0.07, 1, 0, 1, '2019-05-01 15:13:32', '2019-05-01 15:13:32'),
(333, 4, 15, 'Product Price Updated!', 64.05, NULL, 0.05, 1, 0, 1, '2019-05-01 15:21:17', '2019-05-01 15:21:17'),
(334, 8, 15, 'Product Price Updated!', 64.05, NULL, 0.05, 1, 0, 1, '2019-05-01 15:21:18', '2019-05-01 15:21:18'),
(335, 4, 16, 'Product Price Updated!', 74.06, NULL, 0.05, 1, 0, 1, '2019-05-01 15:21:18', '2019-05-01 15:21:18'),
(336, 8, 16, 'Product Price Updated!', 74.06, NULL, 0.05, 1, 0, 1, '2019-05-01 15:21:19', '2019-05-01 15:21:19'),
(337, 4, 15, 'Product Price Updated!', 64.50, NULL, 0.45, 1, 0, 1, '2019-05-01 15:23:21', '2019-05-01 15:23:21'),
(338, 8, 15, 'Product Price Updated!', 64.50, NULL, 0.45, 1, 0, 1, '2019-05-01 15:23:21', '2019-05-01 15:23:21'),
(339, 4, 16, 'Product Price Updated!', 74.50, NULL, 0.44, 1, 0, 1, '2019-05-01 15:23:38', '2019-05-01 15:23:38'),
(340, 8, 16, 'Product Price Updated!', 74.50, NULL, 0.44, 1, 0, 1, '2019-05-01 15:23:38', '2019-05-01 15:23:38'),
(341, 4, 15, 'Product Price Updated!', 64.20, NULL, -0.30, 1, 0, 1, '2019-05-01 15:23:53', '2019-05-01 15:23:53'),
(342, 8, 15, 'Product Price Updated!', 64.20, NULL, -0.30, 1, 0, 1, '2019-05-01 15:23:53', '2019-05-01 15:23:53'),
(343, 4, 15, 'Product Price Updated!', 64.00, NULL, -0.20, 1, 0, 1, '2019-05-01 15:24:09', '2019-05-01 15:24:09'),
(344, 8, 15, 'Product Price Updated!', 64.00, NULL, -0.20, 1, 0, 1, '2019-05-01 15:24:10', '2019-05-01 15:24:10'),
(345, 4, 16, 'Product Price Updated!', 74.00, NULL, -0.50, 1, 0, 1, '2019-05-01 15:24:10', '2019-05-01 15:24:10'),
(346, 8, 16, 'Product Price Updated!', 74.00, NULL, -0.50, 1, 0, 1, '2019-05-01 15:24:10', '2019-05-01 15:24:10'),
(347, 4, 15, 'Product Price Updated!', 64.50, NULL, 0.50, 1, 0, 1, '2019-05-01 15:24:44', '2019-05-01 15:24:44'),
(348, 8, 15, 'Product Price Updated!', 64.50, NULL, 0.50, 1, 0, 1, '2019-05-01 15:24:44', '2019-05-01 15:24:44'),
(349, 1, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:54:24', '2019-05-01 15:54:24'),
(350, 4, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:54:24', '2019-05-01 15:54:24'),
(351, 6, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:54:24', '2019-05-01 15:54:24'),
(352, 8, 1, 'Product Price Updated!', 535.00, NULL, -5.00, 1, 0, 1, '2019-05-01 15:54:25', '2019-05-01 15:54:25'),
(353, 1, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(354, 4, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:47', '2019-05-02 06:04:47'),
(355, 6, 1, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:47', '2019-05-02 06:04:47'),
(356, 4, 2, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:47', '2019-05-02 06:04:47'),
(357, 1, 2, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:47', '2019-05-02 06:04:47'),
(358, 6, 2, 'Product Price Updated!', 540.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(359, 4, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(360, 1, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(361, 6, 3, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(362, 4, 7, 'Product Price Updated!', 450.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(363, 1, 7, 'Product Price Updated!', 450.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:48', '2019-05-02 06:04:48'),
(364, 6, 7, 'Product Price Updated!', 450.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(365, 4, 8, 'Product Price Updated!', 670.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(366, 1, 8, 'Product Price Updated!', 670.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(367, 4, 9, 'Product Price Updated!', 2020.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(368, 1, 9, 'Product Price Updated!', 2020.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(369, 4, 10, 'Product Price Updated!', 145.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(370, 1, 10, 'Product Price Updated!', 145.00, NULL, 5.00, 1, 0, 1, '2019-05-02 06:04:49', '2019-05-02 06:04:49'),
(371, 4, 11, 'Product Price Updated!', 560.00, NULL, -30.00, 1, 0, 1, '2019-05-02 06:04:50', '2019-05-02 06:04:50'),
(372, 4, 12, 'Product Price Updated!', 790.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:50', '2019-05-02 06:04:50'),
(373, 1, 12, 'Product Price Updated!', 790.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:50', '2019-05-02 06:04:50'),
(374, 4, 13, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:50', '2019-05-02 06:04:50'),
(375, 6, 13, 'Product Price Updated!', 500.00, NULL, 10.00, 1, 0, 1, '2019-05-02 06:04:50', '2019-05-02 06:04:50'),
(376, 4, 13, 'Product Price Updated!', 501.00, NULL, 1.00, 1, 0, 1, '2019-05-02 12:02:09', '2019-05-02 12:02:09'),
(377, 6, 13, 'Product Price Updated!', 501.00, NULL, 1.00, 1, 0, 1, '2019-05-02 12:02:09', '2019-05-02 12:02:09'),
(378, 1, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:14:59', '2019-05-03 10:14:59'),
(379, 4, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:15:00', '2019-05-03 10:15:00'),
(380, 6, 1, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:15:00', '2019-05-03 10:15:00'),
(381, 4, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:20:20', '2019-05-03 10:20:20'),
(382, 6, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:20:21', '2019-05-03 10:20:21'),
(383, 1, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:20:44', '2019-05-03 10:20:44'),
(384, 4, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:20:45', '2019-05-03 10:20:45'),
(385, 6, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:20:45', '2019-05-03 10:20:45'),
(386, 1, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:21:12', '2019-05-03 10:21:12'),
(387, 4, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:21:12', '2019-05-03 10:21:12'),
(388, 6, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:21:13', '2019-05-03 10:21:13'),
(389, 4, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:33:22', '2019-05-03 10:33:22'),
(390, 6, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:33:22', '2019-05-03 10:33:22'),
(391, 4, 1, 'Product Price Updated!', 543.00, NULL, 2.00, 1, 0, 1, '2019-05-03 10:33:54', '2019-05-03 10:33:54'),
(392, 6, 1, 'Product Price Updated!', 543.00, NULL, 2.00, 1, 0, 1, '2019-05-03 10:33:55', '2019-05-03 10:33:55'),
(393, 4, 2, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:34:29', '2019-05-03 10:34:29'),
(394, 6, 2, 'Product Price Updated!', 541.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:34:29', '2019-05-03 10:34:29'),
(395, 4, 1, 'Product Price Updated!', 541.00, NULL, -2.00, 1, 0, 1, '2019-05-03 10:39:15', '2019-05-03 10:39:15'),
(396, 6, 1, 'Product Price Updated!', 541.00, NULL, -2.00, 1, 0, 1, '2019-05-03 10:39:16', '2019-05-03 10:39:16'),
(397, 1, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:43:03', '2019-05-03 10:43:03'),
(398, 4, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:43:03', '2019-05-03 10:43:03'),
(399, 6, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:43:03', '2019-05-03 10:43:03'),
(400, 1, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:43:48', '2019-05-03 10:43:48'),
(401, 4, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:43:48', '2019-05-03 10:43:48'),
(402, 6, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:43:49', '2019-05-03 10:43:49'),
(403, 1, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:45:02', '2019-05-03 10:45:02'),
(404, 4, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:45:03', '2019-05-03 10:45:03'),
(405, 6, 1, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-03 10:45:03', '2019-05-03 10:45:03'),
(406, 1, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:45:32', '2019-05-03 10:45:32'),
(407, 4, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:45:32', '2019-05-03 10:45:32'),
(408, 6, 1, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-03 10:45:32', '2019-05-03 10:45:32'),
(409, 1, 1, 'Product Price Updated!', 540.00, NULL, -1.00, 1, 0, 1, '2019-05-04 06:15:33', '2019-05-04 06:15:33'),
(410, 4, 1, 'Product Price Updated!', 540.00, NULL, -1.00, 1, 0, 1, '2019-05-04 06:15:33', '2019-05-04 06:15:33'),
(411, 6, 1, 'Product Price Updated!', 540.00, NULL, -1.00, 1, 0, 1, '2019-05-04 06:15:33', '2019-05-04 06:15:33'),
(412, 4, 10, 'Product Price Updated!', 150.00, NULL, 5.00, 1, 0, 1, '2019-05-07 09:18:16', '2019-05-07 09:18:16'),
(413, 4, 2, 'Product Price Updated!', 540.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:21:09', '2019-05-07 09:21:09'),
(414, 6, 2, 'Product Price Updated!', 540.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:21:09', '2019-05-07 09:21:09'),
(416, 4, 2, 'Product Price Updated!', 542.00, NULL, 2.00, 1, 0, 1, '2019-05-07 09:21:38', '2019-05-07 09:21:38'),
(417, 6, 2, 'Product Price Updated!', 542.00, NULL, 2.00, 1, 0, 1, '2019-05-07 09:21:38', '2019-05-07 09:21:38'),
(419, 4, 2, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:22:01', '2019-05-07 09:22:01'),
(420, 6, 2, 'Product Price Updated!', 541.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:22:01', '2019-05-07 09:22:01'),
(422, 4, 2, 'Product Price Updated!', 524.00, NULL, -17.00, 1, 0, 1, '2019-05-07 09:22:44', '2019-05-07 09:22:44'),
(423, 6, 2, 'Product Price Updated!', 524.00, NULL, -17.00, 1, 0, 1, '2019-05-07 09:22:44', '2019-05-07 09:22:44'),
(425, 4, 2, 'Product Price Updated!', 550.00, NULL, 26.00, 1, 0, 1, '2019-05-07 09:23:15', '2019-05-07 09:23:15'),
(426, 6, 2, 'Product Price Updated!', 550.00, NULL, 26.00, 1, 0, 1, '2019-05-07 09:23:15', '2019-05-07 09:23:15'),
(428, 4, 2, 'Product Price Updated!', 541.00, NULL, -9.00, 1, 0, 1, '2019-05-07 09:23:55', '2019-05-07 09:23:55'),
(429, 6, 2, 'Product Price Updated!', 541.00, NULL, -9.00, 1, 0, 1, '2019-05-07 09:23:55', '2019-05-07 09:23:55'),
(430, 4, 2, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:26:57', '2019-05-07 09:26:57'),
(431, 6, 2, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:26:57', '2019-05-07 09:26:57'),
(432, 4, 2, 'Product Price Updated!', 543.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:27:55', '2019-05-07 09:27:55'),
(433, 6, 2, 'Product Price Updated!', 543.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:27:55', '2019-05-07 09:27:55'),
(435, 4, 2, 'Product Price Updated!', 541.00, NULL, -2.00, 1, 0, 1, '2019-05-07 09:28:40', '2019-05-07 09:28:40'),
(436, 6, 2, 'Product Price Updated!', 541.00, NULL, -2.00, 1, 0, 1, '2019-05-07 09:28:41', '2019-05-07 09:28:41'),
(437, 4, 2, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:29:19', '2019-05-07 09:29:19'),
(438, 6, 2, 'Product Price Updated!', 542.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:29:19', '2019-05-07 09:29:19'),
(440, 4, 2, 'Product Price Updated!', 544.00, NULL, 2.00, 1, 0, 1, '2019-05-07 09:32:37', '2019-05-07 09:32:37'),
(441, 6, 2, 'Product Price Updated!', 544.00, NULL, 2.00, 1, 0, 1, '2019-05-07 09:32:37', '2019-05-07 09:32:37'),
(442, 4, 2, 'Product Price Updated!', 545.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:33:01', '2019-05-07 09:33:01'),
(443, 6, 2, 'Product Price Updated!', 545.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:33:01', '2019-05-07 09:33:01'),
(444, 4, 2, 'Product Price Updated!', 546.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:33:55', '2019-05-07 09:33:55'),
(445, 6, 2, 'Product Price Updated!', 546.00, NULL, 1.00, 1, 0, 1, '2019-05-07 09:33:55', '2019-05-07 09:33:55'),
(447, 4, 2, 'Product Price Updated!', 545.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:34:25', '2019-05-07 09:34:25'),
(448, 6, 2, 'Product Price Updated!', 545.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:34:26', '2019-05-07 09:34:26'),
(450, 4, 2, 'Product Price Updated!', 544.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:34:39', '2019-05-07 09:34:39');
INSERT INTO `notifications` (`id`, `user_id`, `product_id`, `message`, `min_price`, `max_price`, `price_diff`, `message_type`, `is_read`, `status`, `created_at`, `updated_at`) VALUES
(451, 6, 2, 'Product Price Updated!', 544.00, NULL, -1.00, 1, 0, 1, '2019-05-07 09:34:39', '2019-05-07 09:34:39'),
(453, 4, 2, 'Product Price Updated!', 550.00, NULL, 6.00, 1, 0, 1, '2019-05-07 09:39:19', '2019-05-07 09:39:19'),
(454, 6, 2, 'Product Price Updated!', 550.00, NULL, 6.00, 1, 0, 1, '2019-05-07 09:39:19', '2019-05-07 09:39:19'),
(456, 4, 2, 'Product Price Updated!', 540.00, NULL, -10.00, 1, 0, 1, '2019-05-07 09:39:50', '2019-05-07 09:39:50'),
(457, 6, 2, 'Product Price Updated!', 540.00, NULL, -10.00, 1, 0, 1, '2019-05-07 09:39:50', '2019-05-07 09:39:50'),
(459, 4, 2, 'Product Price Updated!', 540.00, NULL, 0.00, 1, 0, 1, '2019-05-07 09:40:13', '2019-05-07 09:40:13'),
(460, 6, 2, 'Product Price Updated!', 540.00, NULL, 0.00, 1, 0, 1, '2019-05-07 09:40:13', '2019-05-07 09:40:13'),
(462, 4, 2, 'Product Price Updated!', 550.00, NULL, 10.00, 1, 0, 1, '2019-05-07 09:40:33', '2019-05-07 09:40:33'),
(463, 6, 2, 'Product Price Updated!', 550.00, NULL, 10.00, 1, 0, 1, '2019-05-07 09:40:33', '2019-05-07 09:40:33'),
(465, 4, 13, 'Product Price Updated!', 500.00, NULL, -1.00, 1, 0, 1, '2019-05-23 13:59:07', '2019-05-23 13:59:07'),
(466, 6, 13, 'Product Price Updated!', 500.00, NULL, -1.00, 1, 0, 1, '2019-05-23 13:59:08', '2019-05-23 13:59:08'),
(467, 4, 2, 'Product Price Updated!', 500.00, NULL, -50.00, 1, 0, 1, '2019-06-12 13:53:36', '2019-06-12 13:53:36'),
(468, 6, 2, 'Product Price Updated!', 500.00, NULL, -50.00, 1, 0, 1, '2019-06-12 13:53:36', '2019-06-12 13:53:36'),
(469, 4, 2, 'Product Price Updated!', 550.00, NULL, 50.00, 1, 0, 1, '2019-06-12 13:54:04', '2019-06-12 13:54:04'),
(470, 6, 2, 'Product Price Updated!', 550.00, NULL, 50.00, 1, 0, 1, '2019-06-12 13:54:04', '2019-06-12 13:54:04'),
(471, 4, 1, 'Product Price Updated!', 560.00, NULL, 20.00, 1, 0, 1, '2019-06-12 14:19:11', '2019-06-12 14:19:11'),
(472, 6, 1, 'Product Price Updated!', 560.00, NULL, 20.00, 1, 0, 1, '2019-06-12 14:19:11', '2019-06-12 14:19:11'),
(473, 4, 5, 'Product Price Updated!', 610.00, 650.00, 10.00, 1, 0, 1, '2019-06-12 14:25:08', '2019-06-12 14:25:08'),
(474, 4, 13, 'Product Price Updated!', 550.00, NULL, 49.00, 1, 0, 1, '2019-06-13 06:08:45', '2019-06-13 06:08:45'),
(475, 6, 13, 'Product Price Updated!', 550.00, NULL, 49.00, 1, 0, 1, '2019-06-13 06:08:45', '2019-06-13 06:08:45'),
(476, 4, 2, 'Product Price Updated!', 550.00, NULL, -10.00, 1, 0, 1, '2019-06-13 06:10:54', '2019-06-13 06:10:54'),
(477, 6, 2, 'Product Price Updated!', 550.00, NULL, -10.00, 1, 0, 1, '2019-06-13 06:10:55', '2019-06-13 06:10:55'),
(478, 4, 2, 'Product Price Updated!', 551.00, NULL, 1.00, 1, 0, 1, '2019-06-14 05:39:10', '2019-06-14 05:39:10'),
(479, 6, 2, 'Product Price Updated!', 551.00, NULL, 1.00, 1, 0, 1, '2019-06-14 05:39:10', '2019-06-14 05:39:10'),
(480, 4, 2, 'Product Price Updated!', 552.00, NULL, 1.00, 1, 0, 1, '2019-06-24 08:27:03', '2019-06-24 08:27:03'),
(481, 6, 2, 'Product Price Updated!', 552.00, NULL, 1.00, 1, 0, 1, '2019-06-24 08:27:04', '2019-06-24 08:27:04'),
(483, 4, 1, 'Product Price Updated!', 551.00, NULL, -9.00, 1, 0, 1, '2019-06-24 08:27:20', '2019-06-24 08:27:20'),
(484, 6, 1, 'Product Price Updated!', 551.00, NULL, -9.00, 1, 0, 1, '2019-06-24 08:27:20', '2019-06-24 08:27:20'),
(486, 4, 2, 'Product Price Updated!', 553.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:50:49', '2019-06-25 07:50:49'),
(487, 6, 2, 'Product Price Updated!', 553.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:50:50', '2019-06-25 07:50:50'),
(489, 4, 2, 'Product Price Updated!', 554.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:51:20', '2019-06-25 07:51:20'),
(490, 6, 2, 'Product Price Updated!', 554.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:51:21', '2019-06-25 07:51:21'),
(492, 4, 2, 'Product Price Updated!', 553.00, NULL, -1.00, 1, 0, 1, '2019-06-25 07:51:51', '2019-06-25 07:51:51'),
(493, 6, 2, 'Product Price Updated!', 553.00, NULL, -1.00, 1, 0, 1, '2019-06-25 07:51:51', '2019-06-25 07:51:51'),
(495, 4, 2, 'Product Price Updated!', 554.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:52:09', '2019-06-25 07:52:09'),
(496, 6, 2, 'Product Price Updated!', 554.00, NULL, 1.00, 1, 0, 1, '2019-06-25 07:52:09', '2019-06-25 07:52:09'),
(498, 4, 2, 'Product Price Updated!', 555.00, NULL, 1.00, 1, 0, 1, '2019-06-25 09:24:29', '2019-06-25 09:24:29'),
(499, 6, 2, 'Product Price Updated!', 555.00, NULL, 1.00, 1, 0, 1, '2019-06-25 09:24:30', '2019-06-25 09:24:30'),
(501, 4, 2, 'Product Price Updated!', 554.00, NULL, -1.00, 1, 0, 1, '2019-06-25 10:56:00', '2019-06-25 10:56:00'),
(502, 6, 2, 'Product Price Updated!', 554.00, NULL, -1.00, 1, 0, 1, '2019-06-25 10:56:00', '2019-06-25 10:56:00'),
(504, 4, 2, 'Product Price Updated!', 555.00, NULL, 1.00, 1, 0, 1, '2019-06-25 10:56:52', '2019-06-25 10:56:52'),
(505, 6, 2, 'Product Price Updated!', 555.00, NULL, 1.00, 1, 0, 1, '2019-06-25 10:56:53', '2019-06-25 10:56:53'),
(506, 4, 1, 'Product Price Updated!', 555.00, NULL, 4.00, 1, 0, 1, '2019-06-25 11:05:41', '2019-06-25 11:05:41'),
(507, 6, 1, 'Product Price Updated!', 555.00, NULL, 4.00, 1, 0, 1, '2019-06-25 11:05:41', '2019-06-25 11:05:41'),
(508, 4, 2, 'Product Price Updated!', 554.00, NULL, -1.00, 1, 0, 1, '2019-06-25 11:06:31', '2019-06-25 11:06:31'),
(509, 6, 2, 'Product Price Updated!', 554.00, NULL, -1.00, 1, 0, 1, '2019-06-25 11:06:31', '2019-06-25 11:06:31'),
(510, 4, 2, 'Product Price Updated!', 551.00, 555.00, 276.00, 1, 0, 1, '2019-06-25 11:08:06', '2019-06-25 11:08:06'),
(511, 6, 2, 'Product Price Updated!', 551.00, 555.00, 276.00, 1, 0, 1, '2019-06-25 11:08:06', '2019-06-25 11:08:06'),
(512, 4, 2, 'Product Price Updated!', 552.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:16:48', '2019-06-25 11:16:48'),
(513, 6, 2, 'Product Price Updated!', 552.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:16:48', '2019-06-25 11:16:48'),
(515, 4, 7, 'Product Price Updated!', 451.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:25:47', '2019-06-25 11:25:47'),
(516, 6, 7, 'Product Price Updated!', 451.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:25:47', '2019-06-25 11:25:47'),
(518, 4, 2, 'Product Price Updated!', 553.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:26:57', '2019-06-25 11:26:57'),
(519, 6, 2, 'Product Price Updated!', 553.00, NULL, 1.00, 1, 0, 1, '2019-06-25 11:26:57', '2019-06-25 11:26:57');

-- --------------------------------------------------------

--
-- Table structure for table `packing`
--

CREATE TABLE `packing` (
  `id` int(10) UNSIGNED NOT NULL,
  `packing_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `packing`
--

INSERT INTO `packing` (`id`, `packing_type`, `image_path`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Bulk Vessel', 'Bulk_Vessels.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 'Steel Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(3, 'HDPE Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(4, '250 KG Steel Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(5, '250 KG HDPE Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(6, '200 KG Steel Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(7, '200 KG HDPE Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(8, '180 KG HDPE Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(9, '180 KG Steel Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(10, '170 KG Steel Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(11, '170 KG HDPE Drums', 'packing/Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(12, '165 KG HDPE Drums', 'Drums.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(13, '1000 KG IBC', 'IBC_Tank.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(14, '20 MT IBC', 'IBC_Tank.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(15, '20 MT IBC', 'IBC_Tank.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(16, 'ISO Tank', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(17, '20 MT ISO Tank', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(18, 'Carton', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(19, 'Flexi Bag', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(20, 'Bulk Bags', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(21, '25 Kg Bags', 'Other_Tanks.png', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `product`
--

CREATE TABLE `product` (
  `id` int(10) UNSIGNED NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` int(10) UNSIGNED NOT NULL,
  `packing_id` int(10) UNSIGNED NOT NULL,
  `unit_id` int(10) UNSIGNED NOT NULL,
  `delivery_term_id` int(10) UNSIGNED NOT NULL,
  `currency_id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `hs_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cas_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `loading_port` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `marquee` tinyint(4) NOT NULL DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product`
--

INSERT INTO `product` (`id`, `uuid`, `category_id`, `packing_id`, `unit_id`, `delivery_term_id`, `currency_id`, `name`, `hs_code`, `cas_number`, `loading_port`, `marquee`, `status`, `created_at`, `updated_at`) VALUES
(1, 'fdf6fe1e-8f80-450d-a4c9-d86ba9d73adf', 1, 1, 1, 1, 1, 'RBD Palm Oil', '15119020', '8002753', 'Port Klang, Malaysia', 1, 1, '2019-01-12 11:50:34', '2019-02-12 11:24:57'),
(2, '2b5dcf8c-87d8-4f76-a8f0-d659d661ba3a', 1, 1, 1, 1, 1, 'RBD Palm Olein', '15119020', '93334395', 'Pasir Gudang Port, Malaysia', 1, 1, '2019-01-12 12:17:39', '2019-06-13 06:10:26'),
(3, '4861e84a-aab3-4bd4-9712-7a180210bea6', 1, 1, 1, 1, 1, 'RBD Palm Stearin', '38231112', '11099073', 'Port Klang, Malaysia', 1, 1, '2019-01-12 12:19:23', '2019-02-12 11:25:06'),
(4, '30df6fe6-f2ba-4e3f-894e-56b537b25507', 5, 21, 1, 1, 1, 'Lauric Acid C1299', '29159090', '29159090', 'Port Klang', 1, 1, '2019-01-12 12:23:34', '2019-01-18 06:34:19'),
(5, '28481c91-b8d6-44b4-a759-86c414b2e435', 2, 4, 1, 1, 1, 'Refined Glycerine 99.7 USP', '290.54.500', '56-81-5', 'Port Klang, Malaysia', 1, 1, '2019-01-13 14:28:33', '2019-02-12 11:26:43'),
(6, 'e24e2219-b92c-4988-a29d-0984c5bbbbb7', 3, 21, 1, 1, 1, 'Soap Noodles 8020 TFM 78', '340120200', '61789897', 'Pasir Gudang Port, Malaysia', 1, 1, '2019-01-13 14:30:12', '2019-03-01 05:00:52'),
(7, '7ca118c3-a606-43c5-ab47-19f6c941dce3', 1, 1, 1, 1, 1, 'Palm Fatty Acid Distillate - PFAD', '38231910', '68440153', 'Port Klang, Malaysia', 1, 1, '2019-01-13 17:09:14', '2019-02-12 11:24:17'),
(8, '6bfa8760-4d76-498a-a7c8-bab3f6df1e95', 1, 1, 1, 1, 1, 'RBD Palm Kernel Oil', '15132910', '8023798', 'Port Klang, Malaysia', 1, 1, '2019-01-13 17:11:33', '2019-02-12 11:22:48'),
(9, '8e2eeb6d-2dd1-40d0-9ba6-53d16dbb5c76', 1, 1, 1, 1, 2, 'Crude Palm Oil - CPO', '15111000', '15111000', 'Port Klang, Malaysia', 1, 1, '2019-01-13 17:13:53', '2019-02-12 11:24:39'),
(10, '7d966e39-4851-49a4-900c-f727862f4667', 1, 1, 6, 1, 2, 'Crude Palm Kernel Oil - CPKO', '15132110', '15132110', 'Port Klang, Malaysia', 1, 1, '2019-01-13 17:16:59', '2019-03-11 11:31:18'),
(11, '3fa1f4aa-0959-42d8-a68d-de52b2eba976', 1, 1, 1, 1, 1, 'RBD Palm Kernel Olein', '15132990', '8023798', 'Port Klang, Malaysia', 1, 1, '2019-01-22 17:45:57', '2019-02-12 11:23:32'),
(12, 'c7e5bfab-786c-43e9-a2c8-6bb8a2ae7c02', 1, 1, 1, 1, 1, 'RBD Palm Kernel Stearin', '15132910', '8023798', 'Port Klang, Malaysia', 1, 1, '2019-01-22 17:51:14', '2019-02-21 13:05:19'),
(13, 'a2cdf6d2-1118-497f-8379-4feaccc72554', 1, 1, 1, 1, 1, 'Crude Palm Oil Indonesia', '15111000', '8002753', 'Port Belawan, Indonesia', 1, 1, '2019-01-22 17:56:21', '2019-02-12 11:23:49'),
(14, '2c8c824c-ddaf-40c9-845d-d1241ef8ad68', 6, 21, 1, 1, 1, 'Stearic Acid Triple Pressed  C1838', '38231900', '57114', 'Pasir Gudang Port', 1, 1, '2019-01-22 18:06:35', '2019-04-30 06:06:58'),
(15, '34143435-00b1-4aec-b59e-3f3a5a905577', 9, 2, 7, 4, 1, 'WTI Crude Oil', '270900', '8002059', 'West Texas Intermediate', 1, 1, '2019-01-22 18:30:14', '2019-02-09 13:08:52'),
(16, 'f00f1ead-5603-4a14-ae84-1e6b94879de4', 9, 2, 7, 4, 1, 'Brent Crude Oil', '270900', '8002059', 'Sweet Light Crude Oil', 1, 1, '2019-01-22 18:38:48', '2019-01-28 14:38:20'),
(17, '2ce063d6-c923-4d1e-bb08-a12992e5c332', 8, 19, 8, 6, 1, 'test', '54354', '54545', 'test', 0, 0, '2019-01-28 12:23:27', '2019-01-28 12:28:08'),
(18, 'dbc57aa6-9ae5-49e9-8613-01c59c31eaec', 7, 3, 1, 1, 1, 'Sodium Lauryl Ether Sulfate  SLES N70', '340211000', '68891383', 'Malaysia', 1, 0, '2019-02-01 09:17:23', '2019-06-13 05:19:44');

-- --------------------------------------------------------

--
-- Table structure for table `product_documents`
--

CREATE TABLE `product_documents` (
  `id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  `document_type` int(10) UNSIGNED NOT NULL COMMENT '1:Specifications; 2:TDS-MSDS; 3:COA',
  `document_path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_favourite`
--

CREATE TABLE `product_favourite` (
  `id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_favourite`
--

INSERT INTO `product_favourite` (`id`, `product_id`, `user_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, '2019-04-30 13:54:42', '2019-04-30 13:54:42'),
(2, 1, 4, 1, '2019-04-30 14:39:51', '2019-04-30 14:39:51'),
(3, 2, 4, 1, '2019-04-30 14:39:53', '2019-04-30 14:39:53'),
(4, 3, 4, 1, '2019-04-30 14:39:55', '2019-04-30 14:39:55'),
(5, 7, 4, 1, '2019-04-30 14:40:00', '2019-04-30 14:40:00'),
(6, 8, 4, 1, '2019-04-30 14:40:03', '2019-04-30 14:40:03'),
(7, 11, 4, 1, '2019-04-30 14:40:06', '2019-04-30 14:40:06'),
(8, 10, 4, 1, '2019-04-30 14:40:08', '2019-04-30 14:40:08'),
(9, 12, 4, 1, '2019-04-30 14:40:10', '2019-04-30 14:40:10'),
(10, 13, 4, 1, '2019-04-30 14:40:13', '2019-04-30 14:40:13'),
(11, 5, 4, 1, '2019-04-30 14:40:18', '2019-04-30 14:40:18'),
(12, 6, 4, 1, '2019-04-30 14:40:22', '2019-04-30 14:40:22'),
(13, 4, 4, 1, '2019-04-30 14:40:30', '2019-04-30 14:40:30'),
(14, 14, 4, 1, '2019-04-30 14:40:35', '2019-04-30 14:40:35'),
(15, 18, 4, 1, '2019-04-30 14:40:40', '2019-04-30 14:40:40'),
(16, 15, 4, 1, '2019-04-30 14:40:45', '2019-04-30 16:58:23'),
(17, 16, 4, 1, '2019-04-30 16:58:24', '2019-04-30 16:58:24'),
(18, 9, 4, 1, '2019-04-30 16:58:40', '2019-04-30 16:58:40'),
(19, 2, 1, 1, '2019-05-01 07:13:06', '2019-05-01 07:13:10'),
(20, 3, 1, 1, '2019-05-01 07:13:11', '2019-05-01 07:13:11'),
(21, 9, 1, 1, '2019-05-01 07:13:14', '2019-05-01 07:13:14'),
(22, 8, 1, 1, '2019-05-01 07:13:15', '2019-05-01 07:13:15'),
(23, 10, 1, 1, '2019-05-01 07:13:16', '2019-05-01 07:13:16'),
(24, 7, 1, 1, '2019-05-01 07:13:19', '2019-05-01 13:02:43'),
(25, 1, 3, 1, '2019-05-01 07:40:53', '2019-05-01 07:40:53'),
(26, 1, 6, 1, '2019-05-01 09:50:43', '2019-05-01 09:50:43'),
(27, 2, 6, 1, '2019-05-01 09:50:45', '2019-05-01 09:50:45'),
(28, 3, 6, 1, '2019-05-01 09:50:48', '2019-05-01 09:50:48'),
(29, 7, 6, 1, '2019-05-01 09:50:49', '2019-05-01 09:50:49'),
(30, 13, 6, 1, '2019-05-01 09:50:52', '2019-05-01 09:50:52'),
(31, 12, 1, 1, '2019-05-01 13:14:32', '2019-05-01 13:14:32'),
(32, 1, 8, 1, '2019-05-01 15:01:59', '2019-05-01 15:01:59'),
(33, 2, 8, 1, '2019-05-01 15:02:01', '2019-05-01 15:02:01'),
(34, 3, 8, 1, '2019-05-01 15:02:02', '2019-05-01 15:02:02'),
(35, 7, 8, 1, '2019-05-01 15:02:04', '2019-05-01 15:02:04'),
(36, 8, 8, 1, '2019-05-01 15:02:07', '2019-05-01 15:02:07'),
(37, 9, 8, 1, '2019-05-01 15:02:09', '2019-05-01 15:02:09'),
(38, 10, 8, 1, '2019-05-01 15:02:11', '2019-05-01 15:02:11'),
(39, 11, 8, 1, '2019-05-01 15:02:13', '2019-05-01 15:02:13'),
(40, 12, 8, 1, '2019-05-01 15:02:15', '2019-05-01 15:02:15'),
(41, 13, 8, 1, '2019-05-01 15:02:16', '2019-05-01 15:02:16'),
(42, 5, 8, 1, '2019-05-01 15:02:21', '2019-05-01 15:02:21'),
(43, 6, 8, 1, '2019-05-01 15:02:24', '2019-05-01 15:02:24'),
(44, 4, 8, 1, '2019-05-01 15:02:29', '2019-05-01 15:02:29'),
(45, 14, 8, 1, '2019-05-01 15:02:32', '2019-05-01 15:02:32'),
(46, 18, 8, 1, '2019-05-01 15:02:35', '2019-05-01 15:02:35'),
(47, 15, 8, 1, '2019-05-01 15:02:41', '2019-05-01 15:02:41'),
(48, 16, 8, 1, '2019-05-01 15:02:42', '2019-05-01 15:02:42');

-- --------------------------------------------------------

--
-- Table structure for table `product_loading_port`
--

CREATE TABLE `product_loading_port` (
  `country_id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_price`
--

CREATE TABLE `product_price` (
  `id` int(10) UNSIGNED NOT NULL,
  `product_id` int(10) UNSIGNED NOT NULL,
  `min_price` double(12,2) NOT NULL,
  `max_price` double(12,2) DEFAULT NULL,
  `price_diff` double(12,2) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_price`
--

INSERT INTO `product_price` (`id`, `product_id`, `min_price`, `max_price`, `price_diff`, `status`, `created_at`, `updated_at`) VALUES
(6, 2, 502.00, NULL, 0.00, 0, '2019-01-12 12:17:39', '2019-01-13 14:24:50'),
(7, 3, 492.00, NULL, 0.00, 0, '2019-01-12 12:19:23', '2019-01-13 14:24:51'),
(8, 1, 507.00, NULL, -1993.00, 0, '2019-01-12 12:19:48', '2019-01-13 14:20:46'),
(9, 4, 900.00, NULL, 0.00, 0, '2019-01-12 12:23:34', '2019-01-13 14:35:13'),
(10, 1, 508.00, NULL, 1.00, 0, '2019-01-13 14:20:46', '2019-01-13 14:24:50'),
(11, 1, 527.00, NULL, 19.00, 0, '2019-01-13 14:24:50', '2019-01-14 08:30:41'),
(12, 2, 532.00, NULL, 30.00, 0, '2019-01-13 14:24:51', '2019-01-14 08:30:41'),
(13, 3, 530.00, NULL, 38.00, 0, '2019-01-13 14:24:51', '2019-01-14 08:30:41'),
(14, 5, 730.00, 750.00, 0.00, 0, '2019-01-13 14:28:33', '2019-01-13 17:18:58'),
(15, 6, 700.00, 720.00, 0.00, 0, '2019-01-13 14:30:12', '2019-01-13 17:07:35'),
(16, 4, 1000.00, 1050.00, 575.00, 0, '2019-01-13 14:35:13', '2019-03-01 05:15:46'),
(17, 6, 650.00, 700.00, -35.00, 0, '2019-01-13 17:07:35', '2019-01-13 17:25:30'),
(18, 7, 403.00, NULL, 0.00, 0, '2019-01-13 17:09:14', '2019-01-14 08:30:41'),
(19, 8, 885.00, NULL, 0.00, 0, '2019-01-13 17:11:33', '2019-01-14 13:04:23'),
(20, 9, 2060.00, NULL, 0.00, 0, '2019-01-13 17:13:53', '2019-01-14 08:30:41'),
(21, 10, 191.00, NULL, 0.00, 0, '2019-01-13 17:16:59', '2019-01-14 08:30:41'),
(22, 5, 700.00, 750.00, -15.00, 0, '2019-01-13 17:18:58', '2019-02-04 10:47:59'),
(23, 6, 650.00, 700.00, 0.00, 0, '2019-01-13 17:25:30', '2019-02-12 06:11:53'),
(24, 1, 530.00, NULL, 3.00, 0, '2019-01-14 08:30:41', '2019-01-14 13:04:22'),
(25, 2, 535.00, NULL, 3.00, 0, '2019-01-14 08:30:41', '2019-01-14 13:04:22'),
(26, 3, 550.00, NULL, 20.00, 0, '2019-01-14 08:30:41', '2019-01-14 13:04:22'),
(27, 7, 400.00, NULL, -3.00, 0, '2019-01-14 08:30:41', '2019-01-14 13:04:23'),
(28, 9, 2050.00, NULL, -10.00, 0, '2019-01-14 08:30:41', '2019-01-15 13:48:11'),
(29, 10, 190.00, NULL, -1.00, 0, '2019-01-14 08:30:41', '2019-01-14 13:04:23'),
(30, 1, 525.00, NULL, -5.00, 0, '2019-01-14 13:04:22', '2019-01-15 13:48:10'),
(31, 2, 530.00, NULL, -5.00, 0, '2019-01-14 13:04:22', '2019-01-15 13:48:10'),
(32, 3, 542.00, NULL, -8.00, 0, '2019-01-14 13:04:22', '2019-01-15 13:48:10'),
(33, 7, 392.00, NULL, -8.00, 0, '2019-01-14 13:04:23', '2019-01-15 13:48:11'),
(34, 8, 880.00, NULL, -5.00, 0, '2019-01-14 13:04:23', '2019-01-16 14:55:05'),
(35, 10, 189.00, NULL, -1.00, 0, '2019-01-14 13:04:23', '2019-01-15 13:48:11'),
(36, 1, 522.00, NULL, -3.00, 0, '2019-01-15 13:48:10', '2019-01-16 14:55:05'),
(37, 2, 527.00, NULL, -3.00, 0, '2019-01-15 13:48:10', '2019-01-16 14:55:05'),
(38, 3, 547.00, NULL, 5.00, 0, '2019-01-15 13:48:10', '2019-01-16 14:55:05'),
(39, 7, 395.00, NULL, 3.00, 0, '2019-01-15 13:48:11', '2019-01-17 13:08:19'),
(40, 9, 2060.00, NULL, 10.00, 0, '2019-01-15 13:48:11', '2019-01-16 14:55:05'),
(41, 10, 188.00, NULL, -1.00, 0, '2019-01-15 13:48:11', '2019-01-16 14:55:05'),
(42, 1, 525.00, NULL, 3.00, 0, '2019-01-16 14:55:05', '2019-01-17 13:08:19'),
(43, 2, 530.00, NULL, 3.00, 0, '2019-01-16 14:55:05', '2019-01-17 13:08:19'),
(44, 3, 540.00, NULL, -7.00, 0, '2019-01-16 14:55:05', '2019-01-17 13:08:19'),
(45, 8, 875.00, NULL, -5.00, 0, '2019-01-16 14:55:05', '2019-01-18 15:02:46'),
(46, 9, 2050.00, NULL, -10.00, 0, '2019-01-16 14:55:05', '2019-01-18 15:02:46'),
(47, 10, 185.00, NULL, -3.00, 0, '2019-01-16 14:55:05', '2019-01-18 15:02:46'),
(48, 1, 527.00, NULL, 2.00, 0, '2019-01-17 13:08:19', '2019-01-18 15:02:46'),
(49, 2, 532.00, NULL, 2.00, 0, '2019-01-17 13:08:19', '2019-01-18 15:02:46'),
(50, 3, 542.00, NULL, 2.00, 0, '2019-01-17 13:08:19', '2019-01-22 12:43:09'),
(51, 7, 397.00, NULL, 2.00, 0, '2019-01-17 13:08:19', '2019-01-18 15:02:46'),
(52, 1, 535.00, NULL, 8.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(53, 2, 540.00, NULL, 8.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(54, 7, 400.00, NULL, 3.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(55, 8, 885.00, NULL, 10.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(56, 9, 2070.00, NULL, 20.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(57, 10, 187.00, NULL, 2.00, 0, '2019-01-18 15:02:46', '2019-01-22 12:43:09'),
(58, 1, 542.00, NULL, 7.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(59, 2, 547.00, NULL, 7.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(60, 3, 547.00, NULL, 5.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(61, 7, 402.00, NULL, 2.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:33:34'),
(62, 8, 895.00, NULL, 10.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(63, 9, 2100.00, NULL, 30.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(64, 10, 190.00, NULL, 3.00, 0, '2019-01-22 12:43:09', '2019-01-23 13:24:56'),
(65, 11, 775.00, NULL, 0.00, 0, '2019-01-22 17:45:57', '2019-01-23 13:24:56'),
(66, 12, 1065.00, NULL, 0.00, 0, '2019-01-22 17:51:14', '2019-01-23 13:24:56'),
(67, 13, 527.00, NULL, 0.00, 0, '2019-01-22 17:56:21', '2019-01-23 13:24:56'),
(68, 14, 730.00, 770.00, 0.00, 0, '2019-01-22 18:06:35', '2019-04-30 06:08:19'),
(69, 15, 52.55, NULL, 0.00, 0, '2019-01-22 18:30:14', '2019-01-23 13:33:08'),
(70, 16, 60.97, NULL, 0.00, 0, '2019-01-22 18:38:48', '2019-01-23 13:32:47'),
(71, 1, 545.00, NULL, 3.00, 0, '2019-01-23 13:24:56', '2019-01-28 10:31:34'),
(72, 2, 550.00, NULL, 3.00, 0, '2019-01-23 13:24:56', '2019-01-24 14:50:13'),
(73, 3, 550.00, NULL, 3.00, 0, '2019-01-23 13:24:56', '2019-01-24 14:50:13'),
(74, 8, 885.00, NULL, -10.00, 0, '2019-01-23 13:24:56', '2019-01-28 10:31:34'),
(75, 9, 2090.00, NULL, -10.00, 0, '2019-01-23 13:24:56', '2019-01-24 14:50:13'),
(76, 10, 188.00, NULL, -2.00, 0, '2019-01-23 13:24:56', '2019-01-29 12:30:04'),
(77, 11, 765.00, NULL, -10.00, 0, '2019-01-23 13:24:56', '2019-01-28 10:31:34'),
(78, 12, 1055.00, NULL, -10.00, 0, '2019-01-23 13:24:56', '2019-01-25 07:22:41'),
(79, 13, 522.00, NULL, -5.00, 0, '2019-01-23 13:24:56', '2019-01-23 13:37:49'),
(80, 16, 62.26, NULL, 1.29, 0, '2019-01-23 13:32:47', '2019-01-24 14:51:34'),
(81, 15, 53.55, NULL, 1.00, 0, '2019-01-23 13:33:08', '2019-01-24 14:51:48'),
(82, 7, 402.50, NULL, 0.50, 0, '2019-01-23 13:33:34', '2019-01-24 14:50:13'),
(83, 13, 522.50, NULL, 0.50, 0, '2019-01-23 13:37:49', '2019-01-25 07:21:17'),
(84, 2, 555.00, NULL, 5.00, 0, '2019-01-24 14:50:13', '2019-01-28 10:31:34'),
(85, 3, 545.00, NULL, -5.00, 0, '2019-01-24 14:50:13', '2019-01-28 10:32:44'),
(86, 7, 405.00, NULL, 2.50, 0, '2019-01-24 14:50:13', '2019-01-28 10:32:06'),
(87, 9, 2180.00, NULL, 90.00, 0, '2019-01-24 14:50:13', '2019-01-28 10:31:34'),
(88, 16, 60.89, NULL, -1.37, 0, '2019-01-24 14:51:34', '2019-01-25 17:32:43'),
(89, 15, 52.61, NULL, -0.94, 0, '2019-01-24 14:51:48', '2019-01-25 17:33:04'),
(90, 13, 522.00, NULL, -0.50, 0, '2019-01-25 07:21:17', '2019-01-28 10:31:34'),
(91, 12, 1055.20, NULL, 0.20, 0, '2019-01-25 07:22:41', '2019-01-25 07:22:57'),
(92, 12, 1055.00, NULL, -0.20, 0, '2019-01-25 07:22:57', '2019-01-28 10:31:34'),
(93, 16, 61.49, NULL, 0.60, 0, '2019-01-25 17:32:43', '2019-01-28 14:40:52'),
(94, 15, 53.61, NULL, 1.00, 0, '2019-01-25 17:33:04', '2019-01-28 14:39:50'),
(95, 1, 560.00, NULL, 15.00, 0, '2019-01-28 10:31:34', '2019-01-28 14:32:43'),
(96, 2, 565.00, NULL, 10.00, 0, '2019-01-28 10:31:34', '2019-01-28 14:32:19'),
(97, 8, 890.00, NULL, 5.00, 0, '2019-01-28 10:31:34', '2019-01-29 12:30:04'),
(98, 9, 2200.00, NULL, 20.00, 0, '2019-01-28 10:31:34', '2019-01-29 12:30:04'),
(99, 11, 770.00, NULL, 5.00, 0, '2019-01-28 10:31:34', '2019-01-29 12:30:04'),
(100, 12, 1060.00, NULL, 5.00, 0, '2019-01-28 10:31:34', '2019-01-29 12:30:04'),
(101, 13, 535.00, NULL, 13.00, 0, '2019-01-28 10:31:34', '2019-01-29 12:30:04'),
(102, 7, 422.50, NULL, 17.50, 0, '2019-01-28 10:32:06', '2019-01-29 12:30:04'),
(103, 3, 562.50, NULL, 17.50, 0, '2019-01-28 10:32:44', '2019-01-28 14:34:50'),
(104, 17, 44.55, 66.66, 0.00, 0, '2019-01-28 12:23:27', '2019-01-28 12:23:41'),
(105, 17, 44.57, 66.67, 0.02, 1, '2019-01-28 12:23:41', '2019-01-28 12:23:41'),
(106, 2, 567.50, NULL, 2.50, 0, '2019-01-28 14:32:19', '2019-01-29 12:30:04'),
(107, 1, 562.50, NULL, 2.50, 0, '2019-01-28 14:32:43', '2019-01-29 12:30:04'),
(108, 3, 560.00, NULL, -2.50, 0, '2019-01-28 14:34:50', '2019-01-31 11:19:23'),
(109, 15, 52.06, NULL, -1.55, 0, '2019-01-28 14:39:50', '2019-01-29 12:30:51'),
(110, 16, 60.09, NULL, -1.40, 0, '2019-01-28 14:40:52', '2019-01-29 12:30:51'),
(111, 1, 557.50, NULL, -5.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:29'),
(112, 2, 562.50, NULL, -5.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:29'),
(113, 7, 425.00, NULL, 2.50, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(114, 8, 875.00, NULL, -15.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(115, 9, 2160.00, NULL, -40.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(116, 10, 185.00, NULL, -3.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(117, 11, 755.00, NULL, -15.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(118, 12, 1045.00, NULL, -15.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(119, 13, 525.00, NULL, -10.00, 0, '2019-01-29 12:30:04', '2019-01-30 13:05:30'),
(120, 15, 52.36, NULL, 0.30, 0, '2019-01-29 12:30:51', '2019-01-30 13:06:28'),
(121, 16, 60.25, NULL, 0.16, 0, '2019-01-29 12:30:51', '2019-01-30 13:06:28'),
(122, 1, 565.00, NULL, 7.50, 0, '2019-01-30 13:05:29', '2019-01-31 11:19:23'),
(123, 2, 570.00, NULL, 7.50, 0, '2019-01-30 13:05:29', '2019-01-31 11:19:23'),
(124, 7, 440.00, NULL, 15.00, 0, '2019-01-30 13:05:30', '2019-02-04 13:06:40'),
(125, 8, 870.00, NULL, -5.00, 0, '2019-01-30 13:05:30', '2019-01-31 11:19:23'),
(126, 9, 2180.00, NULL, 20.00, 0, '2019-01-30 13:05:30', '2019-02-04 13:06:40'),
(127, 10, 184.00, NULL, -1.00, 0, '2019-01-30 13:05:30', '2019-01-31 11:19:23'),
(128, 11, 750.00, NULL, -5.00, 0, '2019-01-30 13:05:30', '2019-01-31 11:19:23'),
(129, 12, 1040.00, NULL, -5.00, 0, '2019-01-30 13:05:30', '2019-01-31 11:19:23'),
(130, 13, 530.00, NULL, 5.00, 0, '2019-01-30 13:05:30', '2019-02-04 18:15:36'),
(131, 15, 53.84, NULL, 1.48, 0, '2019-01-30 13:06:28', '2019-01-31 14:38:44'),
(132, 16, 61.75, NULL, 1.50, 0, '2019-01-30 13:06:28', '2019-01-31 14:38:44'),
(133, 1, 562.50, NULL, -2.50, 0, '2019-01-31 11:19:23', '2019-02-04 18:15:36'),
(134, 2, 567.50, NULL, -2.50, 0, '2019-01-31 11:19:23', '2019-02-04 18:15:36'),
(135, 3, 562.50, NULL, 2.50, 0, '2019-01-31 11:19:23', '2019-01-31 14:38:02'),
(136, 8, 865.00, NULL, -5.00, 0, '2019-01-31 11:19:23', '2019-02-07 08:28:16'),
(137, 10, 182.00, NULL, -2.00, 0, '2019-01-31 11:19:23', '2019-02-07 08:28:16'),
(138, 11, 745.00, NULL, -5.00, 0, '2019-01-31 11:19:23', '2019-02-07 08:28:16'),
(139, 12, 1035.00, NULL, -5.00, 0, '2019-01-31 11:19:23', '2019-02-07 08:28:16'),
(140, 3, 550.00, NULL, -12.50, 0, '2019-01-31 14:38:02', '2019-02-04 13:06:40'),
(141, 15, 54.67, NULL, 0.83, 0, '2019-01-31 14:38:44', '2019-02-02 05:18:07'),
(142, 16, 61.94, NULL, 0.19, 0, '2019-01-31 14:38:44', '2019-02-02 05:18:07'),
(143, 18, 1100.00, 1150.00, 0.00, 0, '2019-02-01 09:17:23', '2019-03-01 05:05:22'),
(144, 15, 55.26, NULL, 0.59, 0, '2019-02-02 05:18:07', '2019-02-04 13:08:25'),
(145, 16, 62.75, NULL, 0.81, 0, '2019-02-02 05:18:07', '2019-02-04 13:08:25'),
(146, 5, 680.00, 720.00, -25.00, 0, '2019-02-04 10:47:59', '2019-02-11 11:02:04'),
(147, 3, 555.00, NULL, 5.00, 0, '2019-02-04 13:06:40', '2019-02-07 08:28:16'),
(148, 7, 445.00, NULL, 5.00, 0, '2019-02-04 13:06:40', '2019-02-04 18:15:36'),
(149, 9, 2207.00, NULL, 27.00, 0, '2019-02-04 13:06:40', '2019-02-07 08:28:16'),
(150, 15, 54.86, NULL, -0.40, 0, '2019-02-04 13:08:25', '2019-02-07 08:38:38'),
(151, 16, 62.44, NULL, -0.31, 0, '2019-02-04 13:08:25', '2019-02-07 08:38:38'),
(152, 1, 565.00, NULL, 2.50, 0, '2019-02-04 18:15:36', '2019-02-07 08:28:16'),
(153, 2, 570.00, NULL, 2.50, 0, '2019-02-04 18:15:36', '2019-02-07 08:28:16'),
(154, 7, 435.00, NULL, -10.00, 0, '2019-02-04 18:15:36', '2019-02-07 08:28:16'),
(155, 13, 532.50, NULL, 2.50, 0, '2019-02-04 18:15:36', '2019-02-07 08:28:16'),
(158, 1, 575.00, NULL, 10.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:17'),
(159, 2, 580.00, NULL, 10.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:17'),
(160, 3, 560.00, NULL, 5.00, 0, '2019-02-07 08:28:16', '2019-02-08 04:56:37'),
(161, 7, 442.50, NULL, 7.50, 0, '2019-02-07 08:28:16', '2019-02-18 11:45:53'),
(162, 8, 875.00, NULL, 10.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:18'),
(163, 9, 2200.00, NULL, -7.00, 0, '2019-02-07 08:28:16', '2019-02-08 04:56:37'),
(164, 10, 184.00, NULL, 2.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:18'),
(165, 11, 775.00, NULL, 30.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:18'),
(166, 12, 1045.00, NULL, 10.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:18'),
(167, 13, 537.50, NULL, 5.00, 0, '2019-02-07 08:28:16', '2019-02-08 06:58:18'),
(168, 15, 53.68, NULL, -1.18, 0, '2019-02-07 08:38:38', '2019-02-08 17:54:56'),
(169, 16, 62.24, NULL, -0.20, 0, '2019-02-07 08:38:38', '2019-02-08 17:54:56'),
(170, 3, 557.50, NULL, -2.50, 0, '2019-02-08 04:56:37', '2019-02-08 06:58:17'),
(171, 9, 2180.00, NULL, -20.00, 0, '2019-02-08 04:56:37', '2019-02-08 06:58:18'),
(172, 1, 567.50, NULL, -7.50, 0, '2019-02-08 06:58:17', '2019-02-12 06:05:51'),
(173, 2, 572.50, NULL, -7.50, 0, '2019-02-08 06:58:17', '2019-02-12 06:05:51'),
(174, 3, 555.00, NULL, -2.50, 0, '2019-02-08 06:58:17', '2019-02-12 06:05:51'),
(175, 8, 865.00, NULL, -10.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(176, 9, 2170.00, NULL, -10.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(177, 10, 182.00, NULL, -2.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(178, 11, 745.00, NULL, -30.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(179, 12, 1035.00, NULL, -10.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(180, 13, 532.50, NULL, -5.00, 0, '2019-02-08 06:58:18', '2019-02-11 06:21:59'),
(181, 15, 52.58, NULL, -1.10, 0, '2019-02-08 17:54:56', '2019-02-09 13:08:32'),
(182, 16, 61.89, NULL, -0.35, 0, '2019-02-08 17:54:56', '2019-02-09 13:08:32'),
(183, 15, 52.72, NULL, 0.14, 0, '2019-02-09 13:08:32', '2019-02-11 14:12:29'),
(184, 16, 62.10, NULL, 0.21, 0, '2019-02-09 13:08:32', '2019-02-11 14:12:29'),
(185, 8, 840.00, NULL, -25.00, 0, '2019-02-11 06:21:59', '2019-02-12 06:05:51'),
(186, 9, 2160.00, NULL, -10.00, 0, '2019-02-11 06:21:59', '2019-02-12 12:40:50'),
(187, 10, 178.00, NULL, -4.00, 0, '2019-02-11 06:21:59', '2019-02-12 06:05:51'),
(188, 11, 725.00, NULL, -20.00, 0, '2019-02-11 06:21:59', '2019-02-12 06:05:51'),
(189, 12, 1010.00, NULL, -25.00, 0, '2019-02-11 06:21:59', '2019-02-12 06:05:51'),
(190, 13, 527.50, NULL, -5.00, 0, '2019-02-11 06:21:59', '2019-02-12 12:40:50'),
(191, 5, 650.00, 700.00, -25.00, 0, '2019-02-11 11:02:04', '2019-03-18 17:45:26'),
(192, 15, 52.28, NULL, -0.44, 0, '2019-02-11 14:12:29', '2019-02-12 12:47:01'),
(193, 16, 61.90, NULL, -0.20, 0, '2019-02-11 14:12:29', '2019-02-12 12:47:01'),
(194, 1, 562.50, NULL, -5.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(195, 2, 567.50, NULL, -5.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(196, 3, 552.50, NULL, -2.50, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(197, 8, 820.00, NULL, -20.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(198, 10, 173.00, NULL, -5.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(199, 11, 715.00, NULL, -10.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(200, 12, 990.00, NULL, -20.00, 0, '2019-02-12 06:05:51', '2019-02-12 12:40:50'),
(201, 6, 650.00, 680.00, -10.00, 0, '2019-02-12 06:11:53', '2019-02-13 01:11:31'),
(202, 1, 560.00, NULL, -2.50, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(203, 2, 565.00, NULL, -2.50, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(204, 3, 547.50, NULL, -5.00, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(205, 8, 810.00, NULL, -10.00, 0, '2019-02-12 12:40:50', '2019-02-14 06:07:42'),
(206, 9, 2150.00, NULL, -10.00, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(207, 10, 172.00, NULL, -1.00, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(208, 11, 710.00, NULL, -5.00, 0, '2019-02-12 12:40:50', '2019-02-14 06:07:42'),
(209, 12, 985.00, NULL, -5.00, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(210, 13, 522.50, NULL, -5.00, 0, '2019-02-12 12:40:50', '2019-02-13 06:48:11'),
(211, 15, 53.39, NULL, 1.11, 0, '2019-02-12 12:47:01', '2019-02-13 15:12:02'),
(212, 16, 62.77, NULL, 0.87, 0, '2019-02-12 12:47:01', '2019-02-13 15:12:03'),
(213, 6, 600.00, 650.00, -40.00, 0, '2019-02-13 01:11:31', '2019-02-13 05:29:49'),
(214, 6, 620.00, 650.00, 10.00, 0, '2019-02-13 05:29:49', '2019-02-13 14:03:36'),
(215, 1, 562.50, NULL, 2.50, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(216, 2, 567.50, NULL, 2.50, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(217, 3, 552.50, NULL, 5.00, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(218, 9, 2160.00, NULL, 10.00, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(219, 10, 171.00, NULL, -1.00, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(220, 12, 980.00, NULL, -5.00, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(221, 13, 525.00, NULL, 2.50, 0, '2019-02-13 06:48:11', '2019-02-14 06:07:42'),
(222, 6, 660.00, 700.00, 45.00, 0, '2019-02-13 14:03:36', '2019-03-01 05:00:40'),
(223, 15, 53.99, NULL, 0.60, 0, '2019-02-13 15:12:03', '2019-02-14 11:56:00'),
(224, 16, 63.47, NULL, 0.70, 0, '2019-02-13 15:12:03', '2019-02-14 11:56:00'),
(225, 1, 560.00, NULL, -2.50, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(226, 2, 565.00, NULL, -2.50, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(227, 3, 547.50, NULL, -5.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(228, 8, 800.00, NULL, -10.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(229, 9, 2140.00, NULL, -20.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(230, 10, 169.00, NULL, -2.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(231, 11, 700.00, NULL, -10.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(232, 12, 970.00, NULL, -10.00, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(233, 13, 522.50, NULL, -2.50, 0, '2019-02-14 06:07:42', '2019-02-14 11:49:09'),
(234, 1, 565.00, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(235, 2, 570.00, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(236, 3, 550.00, NULL, 2.50, 0, '2019-02-14 11:49:09', '2019-02-15 13:17:35'),
(237, 8, 805.00, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(238, 9, 2160.00, NULL, 20.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(239, 10, 170.00, NULL, 1.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(240, 11, 705.00, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(241, 12, 975.00, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(242, 13, 527.50, NULL, 5.00, 0, '2019-02-14 11:49:09', '2019-02-15 07:19:04'),
(243, 15, 54.45, NULL, 0.46, 0, '2019-02-14 11:56:00', '2019-02-15 13:20:20'),
(244, 16, 64.52, NULL, 1.05, 0, '2019-02-14 11:56:00', '2019-02-15 13:20:20'),
(245, 1, 557.50, NULL, -7.50, 0, '2019-02-15 07:19:04', '2019-02-19 08:08:40'),
(246, 2, 562.50, NULL, -7.50, 0, '2019-02-15 07:19:04', '2019-02-18 11:45:53'),
(247, 8, 800.00, NULL, -5.00, 0, '2019-02-15 07:19:04', '2019-02-20 12:40:01'),
(248, 9, 2140.00, NULL, -20.00, 0, '2019-02-15 07:19:04', '2019-02-18 11:45:53'),
(249, 10, 168.00, NULL, -2.00, 0, '2019-02-15 07:19:04', '2019-02-20 12:40:01'),
(250, 11, 700.00, NULL, -5.00, 0, '2019-02-15 07:19:04', '2019-02-20 12:40:01'),
(251, 12, 970.00, NULL, -5.00, 0, '2019-02-15 07:19:04', '2019-02-20 12:40:01'),
(252, 13, 525.00, NULL, -2.50, 0, '2019-02-15 07:19:04', '2019-02-19 08:08:40'),
(253, 3, 547.50, NULL, -2.50, 0, '2019-02-15 13:17:35', '2019-02-18 11:45:53'),
(254, 15, 54.96, NULL, 0.51, 0, '2019-02-15 13:20:20', '2019-02-18 15:33:01'),
(255, 16, 65.21, NULL, 0.69, 0, '2019-02-15 13:20:20', '2019-02-18 15:33:01'),
(256, 2, 567.50, NULL, 5.00, 0, '2019-02-18 11:45:53', '2019-02-19 08:08:40'),
(257, 3, 555.00, NULL, 7.50, 0, '2019-02-18 11:45:53', '2019-02-19 08:08:40'),
(258, 7, 457.50, NULL, 15.00, 0, '2019-02-18 11:45:53', '2019-02-19 08:08:40'),
(259, 9, 2150.00, NULL, 10.00, 0, '2019-02-18 11:45:53', '2019-02-19 08:08:40'),
(260, 15, 56.43, NULL, 1.47, 0, '2019-02-18 15:33:01', '2019-02-19 15:27:53'),
(261, 16, 66.46, NULL, 1.25, 0, '2019-02-18 15:33:01', '2019-02-19 15:27:53'),
(262, 1, 567.50, NULL, 10.00, 0, '2019-02-19 08:08:40', '2019-02-20 07:00:28'),
(263, 2, 572.50, NULL, 5.00, 0, '2019-02-19 08:08:40', '2019-02-20 07:00:28'),
(264, 3, 557.50, NULL, 2.50, 0, '2019-02-19 08:08:40', '2019-02-20 07:00:28'),
(265, 7, 460.00, NULL, 2.50, 0, '2019-02-19 08:08:40', '2019-02-20 07:00:28'),
(266, 9, 2160.00, NULL, 10.00, 0, '2019-02-19 08:08:40', '2019-02-20 07:00:28'),
(267, 13, 530.00, NULL, 5.00, 0, '2019-02-19 08:08:40', '2019-02-21 13:04:56'),
(268, 15, 56.36, NULL, -0.07, 0, '2019-02-19 15:27:53', '2019-02-20 12:41:01'),
(269, 16, 66.36, NULL, -0.10, 0, '2019-02-19 15:27:53', '2019-02-20 12:41:01'),
(270, 1, 557.50, NULL, -10.00, 0, '2019-02-20 07:00:28', '2019-02-20 12:40:01'),
(271, 2, 562.50, NULL, -10.00, 0, '2019-02-20 07:00:28', '2019-02-20 12:40:01'),
(272, 3, 550.00, NULL, -7.50, 0, '2019-02-20 07:00:28', '2019-02-20 12:40:01'),
(273, 7, 457.50, NULL, -2.50, 0, '2019-02-20 07:00:28', '2019-02-21 10:03:18'),
(274, 9, 2150.00, NULL, -10.00, 0, '2019-02-20 07:00:28', '2019-02-20 12:40:01'),
(275, 1, 555.00, NULL, -2.50, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(276, 2, 560.00, NULL, -2.50, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(277, 3, 547.50, NULL, -2.50, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(278, 8, 795.00, NULL, -5.00, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(279, 9, 2120.00, NULL, -30.00, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(280, 10, 167.00, NULL, -1.00, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(281, 11, 695.00, NULL, -5.00, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(282, 12, 965.00, NULL, -5.00, 0, '2019-02-20 12:40:01', '2019-02-21 10:03:18'),
(283, 15, 56.15, NULL, -0.21, 0, '2019-02-20 12:41:01', '2019-02-21 13:14:00'),
(284, 16, 66.05, NULL, -0.31, 0, '2019-02-20 12:41:01', '2019-02-21 13:14:00'),
(285, 1, 550.00, NULL, -5.00, 0, '2019-02-21 10:03:18', '2019-02-21 13:04:56'),
(286, 2, 555.00, NULL, -5.00, 0, '2019-02-21 10:03:18', '2019-02-21 13:04:56'),
(287, 3, 550.00, NULL, 2.50, 0, '2019-02-21 10:03:18', '2019-02-22 09:18:02'),
(288, 7, 470.00, NULL, 12.50, 0, '2019-02-21 10:03:18', '2019-02-25 09:48:03'),
(289, 8, 780.00, NULL, -15.00, 0, '2019-02-21 10:03:18', '2019-02-25 09:48:03'),
(290, 9, 2110.00, NULL, -10.00, 0, '2019-02-21 10:03:18', '2019-02-21 13:04:56'),
(291, 10, 163.00, NULL, -4.00, 0, '2019-02-21 10:03:18', '2019-02-21 13:04:56'),
(292, 11, 690.00, NULL, -5.00, 0, '2019-02-21 10:03:18', '2019-02-25 09:48:03'),
(293, 12, 950.00, NULL, -15.00, 0, '2019-02-21 10:03:18', '2019-02-25 09:48:03'),
(294, 1, 552.50, NULL, 2.50, 0, '2019-02-21 13:04:56', '2019-02-22 09:18:02'),
(295, 2, 557.50, NULL, 2.50, 0, '2019-02-21 13:04:56', '2019-02-22 09:18:02'),
(296, 9, 2130.00, NULL, 20.00, 0, '2019-02-21 13:04:56', '2019-02-25 09:48:03'),
(297, 10, 164.00, NULL, 1.00, 0, '2019-02-21 13:04:56', '2019-02-25 09:48:03'),
(298, 13, 532.50, NULL, 2.50, 0, '2019-02-21 13:04:56', '2019-02-22 09:18:02'),
(299, 15, 57.22, NULL, 1.07, 0, '2019-02-21 13:14:00', '2019-02-23 12:33:40'),
(300, 16, 67.07, NULL, 1.02, 0, '2019-02-21 13:14:00', '2019-02-23 12:33:40'),
(301, 1, 550.00, NULL, -2.50, 0, '2019-02-22 09:18:02', '2019-02-25 09:48:03'),
(302, 2, 555.00, NULL, -2.50, 0, '2019-02-22 09:18:02', '2019-02-25 09:48:03'),
(303, 3, 547.50, NULL, -2.50, 0, '2019-02-22 09:18:02', '2019-02-25 09:48:03'),
(304, 13, 530.00, NULL, -2.50, 0, '2019-02-22 09:18:02', '2019-02-25 09:48:03'),
(305, 15, 57.26, NULL, 0.04, 0, '2019-02-23 12:33:40', '2019-02-26 06:39:04'),
(306, 16, 67.25, NULL, 0.18, 0, '2019-02-23 12:33:40', '2019-02-26 06:39:04'),
(307, 1, 545.00, NULL, -5.00, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(308, 2, 550.00, NULL, -5.00, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(309, 3, 545.00, NULL, -2.50, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(310, 7, 467.50, NULL, -2.50, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(311, 8, 770.00, NULL, -10.00, 0, '2019-02-25 09:48:03', '2019-02-26 08:22:08'),
(312, 9, 2120.00, NULL, -10.00, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(313, 10, 160.00, NULL, -4.00, 0, '2019-02-25 09:48:03', '2019-02-26 08:22:08'),
(314, 11, 680.00, NULL, -10.00, 0, '2019-02-25 09:48:03', '2019-02-26 08:22:08'),
(315, 12, 940.00, NULL, -10.00, 0, '2019-02-25 09:48:03', '2019-02-26 08:22:08'),
(316, 13, 527.50, NULL, -2.50, 0, '2019-02-25 09:48:03', '2019-02-25 11:33:43'),
(317, 1, 540.00, NULL, -5.00, 0, '2019-02-25 11:33:43', '2019-02-26 08:22:08'),
(318, 2, 545.00, NULL, -5.00, 0, '2019-02-25 11:33:43', '2019-02-26 08:22:08'),
(319, 3, 540.00, NULL, -5.00, 0, '2019-02-25 11:33:43', '2019-02-26 12:10:51'),
(320, 7, 470.00, NULL, 2.50, 0, '2019-02-25 11:33:43', '2019-02-26 08:22:08'),
(321, 9, 2090.00, NULL, -30.00, 0, '2019-02-25 11:33:43', '2019-02-26 08:22:08'),
(322, 13, 520.00, NULL, -7.50, 0, '2019-02-25 11:33:43', '2019-02-26 08:22:08'),
(323, 15, 55.24, NULL, -2.02, 0, '2019-02-26 06:39:04', '2019-02-28 09:47:04'),
(324, 16, 64.78, NULL, -2.47, 0, '2019-02-26 06:39:04', '2019-02-28 09:47:04'),
(325, 1, 535.00, NULL, -5.00, 0, '2019-02-26 08:22:08', '2019-02-26 12:10:51'),
(326, 2, 540.00, NULL, -5.00, 0, '2019-02-26 08:22:08', '2019-02-26 12:10:51'),
(327, 7, 465.00, NULL, -5.00, 0, '2019-02-26 08:22:08', '2019-02-26 12:10:51'),
(328, 8, 760.00, NULL, -10.00, 0, '2019-02-26 08:22:08', '2019-02-27 13:17:58'),
(329, 9, 2040.00, NULL, -50.00, 0, '2019-02-26 08:22:08', '2019-02-26 12:10:51'),
(330, 10, 158.00, NULL, -2.00, 0, '2019-02-26 08:22:08', '2019-02-27 13:17:58'),
(331, 11, 670.00, NULL, -10.00, 0, '2019-02-26 08:22:08', '2019-02-27 13:17:58'),
(332, 12, 930.00, NULL, -10.00, 0, '2019-02-26 08:22:08', '2019-02-27 13:17:58'),
(333, 13, 515.00, NULL, -5.00, 0, '2019-02-26 08:22:08', '2019-02-27 08:24:03'),
(334, 1, 532.50, NULL, -2.50, 0, '2019-02-26 12:10:51', '2019-02-27 13:17:58'),
(335, 2, 537.50, NULL, -2.50, 0, '2019-02-26 12:10:51', '2019-02-27 13:17:58'),
(336, 3, 535.00, NULL, -5.00, 0, '2019-02-26 12:10:51', '2019-02-27 13:17:58'),
(337, 7, 462.50, NULL, -2.50, 0, '2019-02-26 12:10:51', '2019-02-27 08:24:03'),
(338, 9, 2030.00, NULL, -10.00, 0, '2019-02-26 12:10:51', '2019-02-27 13:17:58'),
(339, 7, 467.50, NULL, 5.00, 0, '2019-02-27 08:24:03', '2019-02-27 13:17:58'),
(340, 13, 510.00, NULL, -5.00, 0, '2019-02-27 08:24:03', '2019-02-27 13:17:58'),
(341, 1, 525.00, NULL, -7.50, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(342, 2, 530.00, NULL, -7.50, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(343, 3, 520.00, NULL, -15.00, 0, '2019-02-27 13:17:58', '2019-02-28 06:44:17'),
(344, 7, 460.00, NULL, -7.50, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(345, 8, 750.00, NULL, -10.00, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(346, 9, 2000.00, NULL, -30.00, 0, '2019-02-27 13:17:58', '2019-02-28 06:44:17'),
(347, 10, 156.00, NULL, -2.00, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(348, 11, 660.00, NULL, -10.00, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(349, 12, 920.00, NULL, -10.00, 0, '2019-02-27 13:17:58', '2019-02-28 12:15:59'),
(350, 13, 505.00, NULL, -5.00, 0, '2019-02-27 13:17:58', '2019-03-01 18:15:54'),
(351, 3, 525.00, NULL, 5.00, 0, '2019-02-28 06:44:17', '2019-02-28 12:15:59'),
(352, 9, 2010.00, NULL, 10.00, 0, '2019-02-28 06:44:17', '2019-02-28 12:15:59'),
(353, 15, 56.52, NULL, 1.28, 0, '2019-02-28 09:47:04', '2019-02-28 12:23:21'),
(354, 16, 66.03, NULL, 1.25, 0, '2019-02-28 09:47:04', '2019-02-28 12:23:21'),
(355, 1, 527.50, NULL, 2.50, 0, '2019-02-28 12:15:59', '2019-03-01 07:05:08'),
(356, 2, 532.50, NULL, 2.50, 0, '2019-02-28 12:15:59', '2019-03-01 07:05:08'),
(357, 3, 520.00, NULL, -5.00, 0, '2019-02-28 12:15:59', '2019-03-01 07:05:08'),
(358, 7, 462.50, NULL, 2.50, 0, '2019-02-28 12:15:59', '2019-03-01 07:05:08'),
(359, 8, 740.00, NULL, -10.00, 0, '2019-02-28 12:15:59', '2019-03-01 18:15:54'),
(360, 9, 2000.00, NULL, -10.00, 0, '2019-02-28 12:15:59', '2019-03-01 07:05:08'),
(361, 10, 153.00, NULL, -3.00, 0, '2019-02-28 12:15:59', '2019-03-01 18:15:54'),
(362, 11, 640.00, NULL, -20.00, 0, '2019-02-28 12:15:59', '2019-03-01 18:15:54'),
(363, 12, 910.00, NULL, -10.00, 0, '2019-02-28 12:15:59', '2019-03-01 18:15:54'),
(364, 15, 56.75, NULL, 0.23, 0, '2019-02-28 12:23:21', '2019-03-04 16:37:48'),
(365, 16, 66.20, NULL, 0.17, 0, '2019-02-28 12:23:21', '2019-03-04 16:37:48'),
(366, 6, 640.00, 670.00, -25.00, 0, '2019-03-01 05:00:40', '2019-03-13 17:03:51'),
(367, 18, 1050.00, 1080.00, -60.00, 0, '2019-03-01 05:05:22', '2019-03-18 17:44:20'),
(368, 4, 900.00, 950.00, -100.00, 0, '2019-03-01 05:15:46', '2019-04-30 16:50:51'),
(369, 1, 530.00, NULL, 2.50, 0, '2019-03-01 07:05:08', '2019-03-01 18:15:54'),
(370, 2, 535.00, NULL, 2.50, 0, '2019-03-01 07:05:08', '2019-03-01 18:15:54'),
(371, 3, 525.00, NULL, 5.00, 0, '2019-03-01 07:05:08', '2019-03-04 10:07:29'),
(372, 7, 467.50, NULL, 5.00, 0, '2019-03-01 07:05:08', '2019-03-04 10:07:29'),
(373, 9, 2020.00, NULL, 20.00, 0, '2019-03-01 07:05:08', '2019-03-01 18:15:54'),
(374, 1, 535.00, NULL, 5.00, 0, '2019-03-01 18:15:54', '2019-03-04 10:07:29'),
(375, 2, 540.00, NULL, 5.00, 0, '2019-03-01 18:15:54', '2019-03-04 10:07:29'),
(376, 8, 780.00, NULL, 40.00, 0, '2019-03-01 18:15:54', '2019-03-07 16:17:12'),
(377, 9, 2050.00, NULL, 30.00, 0, '2019-03-01 18:15:54', '2019-03-04 10:07:29'),
(378, 10, 160.00, NULL, 7.00, 0, '2019-03-01 18:15:54', '2019-03-07 16:17:12'),
(379, 11, 670.00, NULL, 30.00, 0, '2019-03-01 18:15:54', '2019-03-07 16:17:12'),
(380, 12, 940.00, NULL, 30.00, 0, '2019-03-01 18:15:54', '2019-03-07 16:17:12'),
(381, 13, 510.00, NULL, 5.00, 0, '2019-03-01 18:15:54', '2019-03-04 10:07:29'),
(382, 1, 540.00, NULL, 5.00, 0, '2019-03-04 10:07:29', '2019-03-05 04:25:55'),
(383, 2, 545.00, NULL, 5.00, 0, '2019-03-04 10:07:29', '2019-03-05 04:25:55'),
(384, 3, 530.00, NULL, 5.00, 0, '2019-03-04 10:07:29', '2019-03-06 07:03:49'),
(385, 7, 480.00, NULL, 12.50, 0, '2019-03-04 10:07:29', '2019-03-07 05:20:39'),
(386, 9, 2040.00, NULL, -10.00, 0, '2019-03-04 10:07:29', '2019-03-05 04:25:55'),
(387, 13, 515.00, NULL, 5.00, 0, '2019-03-04 10:07:29', '2019-03-05 04:25:55'),
(388, 15, 56.58, NULL, -0.17, 0, '2019-03-04 16:37:48', '2019-03-05 13:36:26'),
(389, 16, 65.71, NULL, -0.49, 0, '2019-03-04 16:37:48', '2019-03-05 13:36:26'),
(390, 1, 537.50, NULL, -2.50, 0, '2019-03-05 04:25:55', '2019-03-06 07:03:49'),
(391, 2, 542.50, NULL, -2.50, 0, '2019-03-05 04:25:55', '2019-03-06 07:03:49'),
(392, 9, 2000.00, NULL, -40.00, 0, '2019-03-05 04:25:55', '2019-03-06 07:03:49'),
(393, 13, 510.00, NULL, -5.00, 0, '2019-03-05 04:25:55', '2019-03-06 07:03:49'),
(394, 15, 56.72, NULL, 0.14, 0, '2019-03-05 13:36:26', '2019-03-07 13:32:36'),
(395, 16, 65.70, NULL, -0.01, 0, '2019-03-05 13:36:26', '2019-03-07 13:32:36'),
(396, 1, 530.00, NULL, -7.50, 0, '2019-03-06 07:03:49', '2019-03-07 05:20:39'),
(397, 2, 535.00, NULL, -7.50, 0, '2019-03-06 07:03:49', '2019-03-07 05:20:39'),
(398, 3, 532.50, NULL, 2.50, 0, '2019-03-06 07:03:49', '2019-03-07 05:20:39'),
(399, 9, 1992.00, NULL, -8.00, 0, '2019-03-06 07:03:49', '2019-03-06 09:34:41'),
(400, 13, 500.00, NULL, -10.00, 0, '2019-03-06 07:03:49', '2019-03-07 05:20:39'),
(401, 9, 2000.00, NULL, 8.00, 0, '2019-03-06 09:34:41', '2019-03-07 05:20:39'),
(402, 1, 532.50, NULL, 2.50, 0, '2019-03-07 05:20:39', '2019-03-07 16:17:12'),
(403, 2, 537.50, NULL, 2.50, 0, '2019-03-07 05:20:39', '2019-03-07 16:17:12'),
(404, 3, 522.50, NULL, -10.00, 0, '2019-03-07 05:20:39', '2019-03-07 16:17:12'),
(405, 7, 485.00, NULL, 5.00, 0, '2019-03-07 05:20:39', '2019-03-07 16:17:12'),
(406, 9, 2020.00, NULL, 20.00, 0, '2019-03-07 05:20:39', '2019-03-07 16:17:12'),
(407, 13, 510.00, NULL, 10.00, 0, '2019-03-07 05:20:39', '2019-03-08 06:11:49'),
(408, 15, 56.96, NULL, 0.24, 0, '2019-03-07 13:32:36', '2019-03-08 06:19:30'),
(409, 16, 66.85, NULL, 1.15, 0, '2019-03-07 13:32:36', '2019-03-08 06:19:30'),
(410, 1, 525.00, NULL, -7.50, 0, '2019-03-07 16:17:12', '2019-03-13 16:57:37'),
(411, 2, 530.00, NULL, -7.50, 0, '2019-03-07 16:17:12', '2019-03-13 16:57:37'),
(412, 3, 520.00, NULL, -2.50, 0, '2019-03-07 16:17:12', '2019-03-08 06:11:49'),
(413, 7, 482.50, NULL, -2.50, 0, '2019-03-07 16:17:12', '2019-03-08 06:11:49'),
(414, 8, 720.00, NULL, -60.00, 0, '2019-03-07 16:17:12', '2019-03-09 14:00:51'),
(415, 9, 1990.00, NULL, -30.00, 0, '2019-03-07 16:17:12', '2019-03-08 06:11:49'),
(416, 10, 153.00, NULL, -7.00, 0, '2019-03-07 16:17:12', '2019-03-08 06:11:49'),
(417, 11, 620.00, NULL, -50.00, 0, '2019-03-07 16:17:12', '2019-03-09 14:00:51'),
(418, 12, 900.00, NULL, -40.00, 0, '2019-03-07 16:17:12', '2019-03-09 14:00:51'),
(419, 3, 522.50, NULL, 2.50, 0, '2019-03-08 06:11:49', '2019-03-09 14:00:51'),
(420, 7, 485.00, NULL, 2.50, 0, '2019-03-08 06:11:49', '2019-03-09 14:00:51'),
(421, 9, 1980.00, NULL, -10.00, 0, '2019-03-08 06:11:49', '2019-03-09 14:00:51'),
(422, 10, 154.00, NULL, 1.00, 0, '2019-03-08 06:11:49', '2019-03-11 12:11:23'),
(423, 13, 507.50, NULL, -2.50, 0, '2019-03-08 06:11:49', '2019-03-09 14:00:51'),
(424, 15, 56.36, NULL, -0.60, 0, '2019-03-08 06:19:30', '2019-03-11 11:21:37'),
(425, 16, 66.91, NULL, 0.06, 0, '2019-03-08 06:19:30', '2019-03-11 11:21:37'),
(426, 3, 515.00, NULL, -7.50, 0, '2019-03-09 14:00:51', '2019-03-11 12:11:23'),
(427, 7, 482.50, NULL, -2.50, 0, '2019-03-09 14:00:51', '2019-03-11 12:11:23'),
(428, 8, 710.00, NULL, -10.00, 0, '2019-03-09 14:00:51', '2019-03-11 11:17:06'),
(429, 9, 1971.00, NULL, -9.00, 0, '2019-03-09 14:00:51', '2019-03-11 11:17:06'),
(430, 11, 610.00, NULL, -10.00, 0, '2019-03-09 14:00:51', '2019-03-11 11:17:06'),
(431, 12, 890.00, NULL, -10.00, 0, '2019-03-09 14:00:51', '2019-03-11 11:17:06'),
(432, 13, 500.00, NULL, -7.50, 0, '2019-03-09 14:00:51', '2019-03-11 11:17:06'),
(433, 8, 700.00, NULL, -10.00, 0, '2019-03-11 11:17:06', '2019-03-13 16:57:37'),
(434, 9, 1959.00, NULL, -12.00, 0, '2019-03-11 11:17:06', '2019-03-12 14:08:52'),
(435, 11, 600.00, NULL, -10.00, 0, '2019-03-11 11:17:06', '2019-03-13 16:57:37'),
(436, 12, 880.00, NULL, -10.00, 0, '2019-03-11 11:17:06', '2019-03-13 16:57:37'),
(437, 13, 495.00, NULL, -5.00, 0, '2019-03-11 11:17:06', '2019-03-12 14:08:52'),
(438, 15, 56.53, NULL, 0.17, 0, '2019-03-11 11:21:37', '2019-03-12 14:13:23'),
(439, 16, 66.26, NULL, -0.65, 0, '2019-03-11 11:21:37', '2019-03-12 14:13:23'),
(440, 3, 520.00, NULL, 5.00, 0, '2019-03-11 12:11:23', '2019-03-13 16:57:37'),
(441, 7, 485.00, NULL, 2.50, 0, '2019-03-11 12:11:23', '2019-03-12 14:08:52'),
(442, 10, 150.00, NULL, -4.00, 0, '2019-03-11 12:11:23', '2019-03-13 16:57:37'),
(443, 7, 482.50, NULL, -2.50, 0, '2019-03-12 14:08:52', '2019-03-13 16:57:37'),
(444, 9, 1960.00, NULL, 1.00, 0, '2019-03-12 14:08:52', '2019-03-13 16:57:37'),
(445, 13, 500.00, NULL, 5.00, 0, '2019-03-12 14:08:52', '2019-03-13 16:57:37'),
(446, 15, 57.21, NULL, 0.68, 0, '2019-03-12 14:13:23', '2019-03-13 17:02:57'),
(447, 16, 66.93, NULL, 0.67, 0, '2019-03-12 14:13:23', '2019-03-13 17:02:57'),
(448, 1, 520.00, NULL, -5.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(449, 2, 525.00, NULL, -5.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(450, 3, 510.00, NULL, -10.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(451, 7, 475.00, NULL, -7.50, 0, '2019-03-13 16:57:37', '2019-03-15 14:23:11'),
(452, 8, 685.00, NULL, -15.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(453, 9, 1920.00, NULL, -40.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(454, 10, 147.00, NULL, -3.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(455, 11, 585.00, NULL, -15.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(456, 12, 865.00, NULL, -15.00, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(457, 13, 497.50, NULL, -2.50, 0, '2019-03-13 16:57:37', '2019-03-14 15:33:34'),
(458, 15, 57.87, NULL, 0.66, 0, '2019-03-13 17:02:57', '2019-03-15 14:26:12'),
(459, 16, 67.20, NULL, 0.27, 0, '2019-03-13 17:02:57', '2019-03-15 14:26:13'),
(460, 6, 600.00, 650.00, -30.00, 0, '2019-03-13 17:03:51', '2019-03-15 14:27:15'),
(461, 1, 515.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-18 13:50:45'),
(462, 2, 520.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-18 13:50:45'),
(463, 3, 507.50, NULL, -2.50, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(464, 8, 680.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(465, 9, 1900.00, NULL, -20.00, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(466, 10, 142.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(467, 11, 580.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(468, 12, 860.00, NULL, -5.00, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(469, 13, 490.00, NULL, -7.50, 0, '2019-03-14 15:33:34', '2019-03-15 14:23:11'),
(470, 3, 502.50, NULL, -5.00, 0, '2019-03-15 14:23:11', '2019-03-20 11:14:20'),
(471, 7, 472.50, NULL, -2.50, 0, '2019-03-15 14:23:11', '2019-03-20 11:14:20'),
(472, 8, 685.00, NULL, 5.00, 0, '2019-03-15 14:23:11', '2019-03-19 06:33:20'),
(473, 9, 1920.00, NULL, 20.00, 0, '2019-03-15 14:23:11', '2019-03-18 13:50:45'),
(474, 10, 143.00, NULL, 1.00, 0, '2019-03-15 14:23:11', '2019-03-19 06:33:20'),
(475, 11, 585.00, NULL, 5.00, 0, '2019-03-15 14:23:11', '2019-03-19 06:33:20'),
(476, 12, 865.00, NULL, 5.00, 0, '2019-03-15 14:23:11', '2019-03-19 06:33:20'),
(477, 13, 487.50, NULL, -2.50, 0, '2019-03-15 14:23:11', '2019-03-18 13:50:45'),
(478, 15, 58.16, NULL, 0.29, 0, '2019-03-15 14:26:12', '2019-03-18 13:55:13'),
(479, 16, 66.47, NULL, -0.73, 0, '2019-03-15 14:26:13', '2019-03-18 13:55:13'),
(480, 6, 600.00, 630.00, -10.00, 0, '2019-03-15 14:27:15', '2019-04-25 13:01:28'),
(481, 1, 512.50, NULL, -2.50, 0, '2019-03-18 13:50:45', '2019-03-19 06:33:20'),
(482, 2, 517.50, NULL, -2.50, 0, '2019-03-18 13:50:45', '2019-03-19 06:33:20'),
(483, 9, 1930.00, NULL, 10.00, 0, '2019-03-18 13:50:45', '2019-03-19 06:33:20'),
(484, 13, 485.00, NULL, -2.50, 0, '2019-03-18 13:50:45', '2019-03-19 06:33:20'),
(485, 15, 58.53, NULL, 0.37, 0, '2019-03-18 13:55:13', '2019-03-19 06:39:17'),
(486, 16, 67.23, NULL, 0.76, 0, '2019-03-18 13:55:13', '2019-03-19 06:39:17'),
(487, 18, 1030.00, 1060.00, -20.00, 0, '2019-03-18 17:44:20', '2019-04-30 06:11:27'),
(488, 5, 630.00, 670.00, -25.00, 0, '2019-03-18 17:45:26', '2019-04-15 15:51:54'),
(489, 1, 515.00, NULL, 2.50, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(490, 2, 520.00, NULL, 2.50, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(491, 8, 695.00, NULL, 10.00, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(492, 9, 1920.00, NULL, -10.00, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(493, 10, 145.00, NULL, 2.00, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(494, 11, 595.00, NULL, 10.00, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(495, 12, 875.00, NULL, 10.00, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(496, 13, 487.50, NULL, 2.50, 0, '2019-03-19 06:33:20', '2019-03-20 11:14:20'),
(497, 15, 59.25, NULL, 0.72, 0, '2019-03-19 06:39:17', '2019-03-20 11:17:50'),
(498, 16, 67.53, NULL, 0.30, 0, '2019-03-19 06:39:17', '2019-03-20 11:17:50'),
(499, 1, 517.50, NULL, 2.50, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(500, 2, 522.50, NULL, 2.50, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(501, 3, 510.00, NULL, 7.50, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(502, 7, 475.00, NULL, 2.50, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(503, 8, 725.00, NULL, 30.00, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(504, 9, 1950.00, NULL, 30.00, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(505, 10, 153.00, NULL, 8.00, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(506, 11, 625.00, NULL, 30.00, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(507, 12, 895.00, NULL, 20.00, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(508, 13, 495.00, NULL, 7.50, 0, '2019-03-20 11:14:20', '2019-03-20 11:23:18'),
(509, 15, 58.70, NULL, -0.55, 0, '2019-03-20 11:17:50', '2019-03-22 13:20:51'),
(510, 16, 67.13, NULL, -0.40, 0, '2019-03-20 11:17:50', '2019-03-22 13:20:51'),
(511, 1, 522.50, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(512, 2, 527.50, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(513, 3, 517.50, NULL, 7.50, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(514, 7, 480.00, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-25 06:37:22'),
(515, 8, 730.00, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(516, 9, 2020.00, NULL, 70.00, 0, '2019-03-20 11:23:18', '2019-03-22 11:55:21'),
(517, 10, 154.00, NULL, 1.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(518, 11, 630.00, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(519, 12, 900.00, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-21 07:42:34'),
(520, 13, 500.00, NULL, 5.00, 0, '2019-03-20 11:23:18', '2019-03-22 11:55:21'),
(521, 1, 527.50, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-25 06:37:22'),
(522, 2, 532.50, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-25 06:37:22'),
(523, 3, 522.50, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-22 06:16:06'),
(524, 8, 735.00, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-22 06:16:06'),
(525, 10, 155.00, NULL, 1.00, 0, '2019-03-21 07:42:34', '2019-03-22 11:55:21'),
(526, 11, 635.00, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-22 06:16:06'),
(527, 12, 905.00, NULL, 5.00, 0, '2019-03-21 07:42:34', '2019-03-22 06:16:06'),
(528, 3, 517.50, NULL, -5.00, 0, '2019-03-22 06:16:06', '2019-03-26 11:39:28'),
(529, 8, 745.00, NULL, 10.00, 0, '2019-03-22 06:16:06', '2019-03-22 11:55:21'),
(530, 11, 645.00, NULL, 10.00, 0, '2019-03-22 06:16:06', '2019-03-22 11:55:21'),
(531, 12, 915.00, NULL, 10.00, 0, '2019-03-22 06:16:06', '2019-03-22 11:55:21'),
(532, 8, 730.00, NULL, -15.00, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(533, 9, 2040.00, NULL, 20.00, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(534, 10, 153.00, NULL, -2.00, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(535, 11, 630.00, NULL, -15.00, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(536, 12, 900.00, NULL, -15.00, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(537, 13, 502.50, NULL, 2.50, 0, '2019-03-22 11:55:21', '2019-03-25 06:37:22'),
(538, 15, 59.22, NULL, 0.52, 0, '2019-03-22 13:20:51', '2019-03-26 16:43:35'),
(539, 16, 66.73, NULL, -0.40, 0, '2019-03-22 13:20:51', '2019-03-26 16:43:35'),
(540, 1, 525.00, NULL, -2.50, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(541, 2, 530.00, NULL, -2.50, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(542, 7, 477.50, NULL, -2.50, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(543, 8, 720.00, NULL, -10.00, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(544, 9, 2010.00, NULL, -30.00, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(545, 10, 148.00, NULL, -5.00, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(546, 11, 620.00, NULL, -10.00, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(547, 12, 890.00, NULL, -10.00, 0, '2019-03-25 06:37:22', '2019-03-26 11:39:28'),
(548, 13, 500.00, NULL, -2.50, 0, '2019-03-25 06:37:22', '2019-03-27 14:54:49'),
(549, 1, 520.00, NULL, -5.00, 0, '2019-03-26 11:39:28', '2019-03-27 14:54:48'),
(550, 2, 525.00, NULL, -5.00, 0, '2019-03-26 11:39:28', '2019-03-27 14:54:48'),
(551, 3, 515.00, NULL, -2.50, 0, '2019-03-26 11:39:28', '2019-03-26 16:39:25'),
(552, 7, 472.50, NULL, -5.00, 0, '2019-03-26 11:39:28', '2019-03-28 12:11:38'),
(553, 8, 710.00, NULL, -10.00, 0, '2019-03-26 11:39:28', '2019-03-27 08:19:44'),
(554, 9, 2000.00, NULL, -10.00, 0, '2019-03-26 11:39:28', '2019-03-27 08:19:44'),
(555, 10, 146.00, NULL, -2.00, 0, '2019-03-26 11:39:28', '2019-03-27 08:19:44'),
(556, 11, 610.00, NULL, -10.00, 0, '2019-03-26 11:39:28', '2019-03-27 08:19:44'),
(557, 12, 880.00, NULL, -10.00, 0, '2019-03-26 11:39:28', '2019-03-27 08:19:44'),
(558, 3, 510.00, NULL, -5.00, 0, '2019-03-26 16:39:25', '2019-03-27 08:19:44'),
(559, 15, 59.84, NULL, 0.62, 0, '2019-03-26 16:43:35', '2019-03-28 12:26:11'),
(560, 16, 67.35, NULL, 0.62, 0, '2019-03-26 16:43:35', '2019-03-28 12:26:11'),
(561, 3, 507.50, NULL, -2.50, 0, '2019-03-27 08:19:44', '2019-03-27 14:54:48'),
(562, 8, 705.00, NULL, -5.00, 0, '2019-03-27 08:19:44', '2019-03-27 14:54:48'),
(563, 9, 2010.00, NULL, 10.00, 0, '2019-03-27 08:19:44', '2019-03-28 12:11:38'),
(564, 10, 145.00, NULL, -1.00, 0, '2019-03-27 08:19:44', '2019-03-27 14:54:48'),
(565, 11, 605.00, NULL, -5.00, 0, '2019-03-27 08:19:44', '2019-03-27 14:54:48'),
(566, 12, 875.00, NULL, -5.00, 0, '2019-03-27 08:19:44', '2019-03-27 14:54:49'),
(567, 1, 522.50, NULL, 2.50, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(568, 2, 527.50, NULL, 2.50, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(569, 3, 515.00, NULL, 7.50, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(570, 8, 710.00, NULL, 5.00, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(571, 10, 147.00, NULL, 2.00, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(572, 11, 610.00, NULL, 5.00, 0, '2019-03-27 14:54:48', '2019-03-28 12:11:38'),
(573, 12, 880.00, NULL, 5.00, 0, '2019-03-27 14:54:49', '2019-03-28 12:11:38'),
(574, 13, 502.50, NULL, 2.50, 0, '2019-03-27 14:54:49', '2019-03-28 12:11:38'),
(575, 1, 520.00, NULL, -2.50, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(576, 2, 525.00, NULL, -2.50, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(577, 3, 510.00, NULL, -5.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(578, 7, 470.00, NULL, -2.50, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(579, 8, 705.00, NULL, -5.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(580, 9, 2000.00, NULL, -10.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(581, 10, 145.00, NULL, -2.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(582, 11, 605.00, NULL, -5.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(583, 12, 875.00, NULL, -5.00, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(584, 13, 500.00, NULL, -2.50, 0, '2019-03-28 12:11:38', '2019-03-29 06:28:13'),
(585, 15, 58.75, NULL, -1.09, 0, '2019-03-28 12:26:11', '2019-04-01 08:05:56'),
(586, 16, 66.62, NULL, -0.73, 0, '2019-03-28 12:26:11', '2019-04-01 08:05:56'),
(587, 1, 517.50, NULL, -2.50, 0, '2019-03-29 06:28:13', '2019-03-29 15:05:33'),
(588, 2, 522.50, NULL, -2.50, 0, '2019-03-29 06:28:13', '2019-03-29 15:05:33'),
(589, 3, 505.00, NULL, -5.00, 0, '2019-03-29 06:28:13', '2019-04-01 05:19:42'),
(590, 7, 467.50, NULL, -2.50, 0, '2019-03-29 06:28:13', '2019-03-29 15:05:33'),
(591, 8, 700.00, NULL, -5.00, 0, '2019-03-29 06:28:13', '2019-04-02 05:46:08'),
(592, 9, 1990.00, NULL, -10.00, 0, '2019-03-29 06:28:13', '2019-03-29 15:05:33'),
(593, 10, 144.00, NULL, -1.00, 0, '2019-03-29 06:28:13', '2019-04-01 05:19:42'),
(594, 11, 600.00, NULL, -5.00, 0, '2019-03-29 06:28:13', '2019-04-02 05:46:08'),
(595, 12, 870.00, NULL, -5.00, 0, '2019-03-29 06:28:13', '2019-04-02 05:46:08'),
(596, 13, 495.00, NULL, -5.00, 0, '2019-03-29 06:28:13', '2019-04-02 05:46:08'),
(597, 1, 520.00, NULL, 2.50, 0, '2019-03-29 15:05:33', '2019-04-01 05:19:42'),
(598, 2, 525.00, NULL, 2.50, 0, '2019-03-29 15:05:33', '2019-04-01 05:19:42'),
(599, 7, 465.00, NULL, -2.50, 0, '2019-03-29 15:05:33', '2019-04-01 05:19:42'),
(600, 9, 2000.00, NULL, 10.00, 0, '2019-03-29 15:05:33', '2019-04-01 11:27:28'),
(601, 1, 517.50, NULL, -2.50, 0, '2019-04-01 05:19:42', '2019-04-01 11:27:28'),
(602, 2, 522.50, NULL, -2.50, 0, '2019-04-01 05:19:42', '2019-04-01 11:27:28'),
(603, 3, 507.50, NULL, 2.50, 0, '2019-04-01 05:19:42', '2019-04-01 11:27:28'),
(604, 7, 467.50, NULL, 2.50, 0, '2019-04-01 05:19:42', '2019-04-03 05:24:34'),
(605, 10, 145.00, NULL, 1.00, 0, '2019-04-01 05:19:42', '2019-04-02 05:46:08'),
(606, 15, 60.71, NULL, 1.96, 0, '2019-04-01 08:05:56', '2019-04-01 12:47:29'),
(607, 16, 68.49, NULL, 1.87, 0, '2019-04-01 08:05:56', '2019-04-01 12:35:28'),
(608, 1, 515.00, NULL, -2.50, 0, '2019-04-01 11:27:28', '2019-04-02 05:46:08'),
(609, 2, 525.00, NULL, 2.50, 0, '2019-04-01 11:27:28', '2019-04-02 05:46:08'),
(610, 3, 505.00, NULL, -2.50, 0, '2019-04-01 11:27:28', '2019-04-02 05:46:08'),
(611, 9, 2010.00, NULL, 10.00, 0, '2019-04-01 11:27:28', '2019-04-02 05:46:08'),
(612, 16, 68.65, NULL, 0.16, 0, '2019-04-01 12:35:28', '2019-04-03 05:28:12'),
(613, 15, 60.86, NULL, 0.15, 0, '2019-04-01 12:47:29', '2019-04-03 05:28:12'),
(614, 1, 525.00, NULL, 10.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(615, 2, 530.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(616, 3, 510.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(617, 8, 705.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(618, 9, 2020.00, NULL, 10.00, 0, '2019-04-02 05:46:08', '2019-04-03 05:24:34'),
(619, 10, 146.00, NULL, 1.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(620, 11, 605.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(621, 12, 875.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(622, 13, 500.00, NULL, 5.00, 0, '2019-04-02 05:46:08', '2019-04-02 12:01:21'),
(623, 1, 527.50, NULL, 2.50, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(624, 2, 532.50, NULL, 2.50, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(625, 3, 507.50, NULL, -2.50, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(626, 8, 710.00, NULL, 5.00, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(627, 10, 147.00, NULL, 1.00, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(628, 11, 610.00, NULL, 5.00, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(629, 12, 880.00, NULL, 5.00, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(630, 13, 502.50, NULL, 2.50, 0, '2019-04-02 12:01:21', '2019-04-03 05:24:34'),
(631, 1, 530.00, NULL, 2.50, 0, '2019-04-03 05:24:34', '2019-04-03 12:53:10'),
(632, 2, 535.00, NULL, 2.50, 0, '2019-04-03 05:24:34', '2019-04-03 12:53:10'),
(633, 3, 515.00, NULL, 7.50, 0, '2019-04-03 05:24:34', '2019-04-05 05:28:43'),
(634, 7, 475.00, NULL, 7.50, 0, '2019-04-03 05:24:34', '2019-04-03 12:53:10'),
(635, 8, 720.00, NULL, 10.00, 0, '2019-04-03 05:24:34', '2019-04-05 13:01:56'),
(636, 9, 2030.00, NULL, 10.00, 0, '2019-04-03 05:24:34', '2019-04-03 12:53:10'),
(637, 10, 150.00, NULL, 3.00, 0, '2019-04-03 05:24:34', '2019-04-04 12:20:11'),
(638, 11, 620.00, NULL, 10.00, 0, '2019-04-03 05:24:34', '2019-04-05 13:01:56'),
(639, 12, 890.00, NULL, 10.00, 0, '2019-04-03 05:24:34', '2019-04-05 13:01:56'),
(640, 13, 505.00, NULL, 2.50, 0, '2019-04-03 05:24:34', '2019-04-04 12:20:11'),
(641, 15, 62.85, NULL, 1.99, 0, '2019-04-03 05:28:12', '2019-04-03 19:19:37'),
(642, 16, 69.75, NULL, 1.10, 0, '2019-04-03 05:28:12', '2019-04-03 19:19:37'),
(643, 1, 532.50, NULL, 2.50, 0, '2019-04-03 12:53:10', '2019-04-04 12:20:11'),
(644, 2, 537.50, NULL, 2.50, 0, '2019-04-03 12:53:10', '2019-04-04 12:20:11'),
(645, 7, 470.00, NULL, -5.00, 0, '2019-04-03 12:53:10', '2019-04-05 13:01:56');
INSERT INTO `product_price` (`id`, `product_id`, `min_price`, `max_price`, `price_diff`, `status`, `created_at`, `updated_at`) VALUES
(646, 9, 2050.00, NULL, 20.00, 0, '2019-04-03 12:53:10', '2019-04-04 12:20:11'),
(647, 15, 62.39, NULL, -0.46, 0, '2019-04-03 19:19:37', '2019-04-06 19:29:43'),
(648, 16, 69.32, NULL, -0.43, 0, '2019-04-03 19:19:37', '2019-04-06 19:29:43'),
(649, 1, 542.50, NULL, 10.00, 0, '2019-04-04 12:20:11', '2019-04-05 13:01:56'),
(650, 2, 547.50, NULL, 10.00, 0, '2019-04-04 12:20:11', '2019-04-05 13:01:56'),
(651, 9, 2100.00, NULL, 50.00, 0, '2019-04-04 12:20:11', '2019-04-05 05:28:43'),
(652, 10, 149.00, NULL, -1.00, 0, '2019-04-04 12:20:11', '2019-04-05 13:01:56'),
(653, 13, 510.00, NULL, 5.00, 0, '2019-04-04 12:20:11', '2019-04-05 05:28:43'),
(654, 3, 522.50, NULL, 7.50, 0, '2019-04-05 05:28:43', '2019-04-05 13:01:56'),
(655, 9, 2070.00, NULL, -30.00, 0, '2019-04-05 05:28:43', '2019-04-05 13:01:56'),
(656, 13, 512.50, NULL, 2.50, 0, '2019-04-05 05:28:43', '2019-04-05 13:01:56'),
(657, 1, 545.00, NULL, 2.50, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(658, 2, 550.00, NULL, 2.50, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(659, 3, 525.00, NULL, 2.50, 0, '2019-04-05 13:01:56', '2019-04-08 12:09:06'),
(660, 7, 475.00, NULL, 5.00, 0, '2019-04-05 13:01:56', '2019-04-10 06:34:12'),
(661, 8, 730.00, NULL, 10.00, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(662, 9, 2100.00, NULL, 30.00, 0, '2019-04-05 13:01:56', '2019-04-08 12:09:06'),
(663, 10, 151.00, NULL, 2.00, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(664, 11, 630.00, NULL, 10.00, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(665, 12, 900.00, NULL, 10.00, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(666, 13, 515.00, NULL, 2.50, 0, '2019-04-05 13:01:56', '2019-04-08 05:39:20'),
(667, 15, 63.08, NULL, 0.69, 0, '2019-04-06 19:29:43', '2019-04-08 08:50:31'),
(668, 16, 70.34, NULL, 1.02, 0, '2019-04-06 19:29:43', '2019-04-08 08:50:31'),
(669, 1, 547.50, NULL, 2.50, 0, '2019-04-08 05:39:20', '2019-04-08 12:09:06'),
(670, 2, 552.50, NULL, 2.50, 0, '2019-04-08 05:39:20', '2019-04-08 12:09:06'),
(671, 8, 725.00, NULL, -5.00, 0, '2019-04-08 05:39:20', '2019-04-09 06:04:49'),
(672, 10, 150.00, NULL, -1.00, 0, '2019-04-08 05:39:20', '2019-04-09 06:04:49'),
(673, 11, 625.00, NULL, -5.00, 0, '2019-04-08 05:39:20', '2019-04-09 06:04:49'),
(674, 12, 895.00, NULL, -5.00, 0, '2019-04-08 05:39:20', '2019-04-09 06:04:49'),
(675, 13, 512.50, NULL, -2.50, 0, '2019-04-08 05:39:20', '2019-04-09 06:04:49'),
(676, 15, 63.48, NULL, 0.40, 0, '2019-04-08 08:50:31', '2019-04-09 14:09:35'),
(677, 16, 70.78, NULL, 0.44, 0, '2019-04-08 08:50:31', '2019-04-09 14:09:35'),
(678, 1, 550.00, NULL, 2.50, 0, '2019-04-08 12:09:06', '2019-04-10 06:34:12'),
(679, 2, 555.00, NULL, 2.50, 0, '2019-04-08 12:09:06', '2019-04-10 06:34:12'),
(680, 3, 522.50, NULL, -2.50, 0, '2019-04-08 12:09:06', '2019-04-09 06:04:49'),
(681, 9, 2070.00, NULL, -30.00, 0, '2019-04-08 12:09:06', '2019-04-09 10:41:19'),
(682, 3, 527.50, NULL, 5.00, 0, '2019-04-09 06:04:49', '2019-04-09 10:41:19'),
(683, 8, 735.00, NULL, 10.00, 0, '2019-04-09 06:04:49', '2019-04-11 07:02:40'),
(684, 10, 152.00, NULL, 2.00, 0, '2019-04-09 06:04:49', '2019-04-11 07:02:40'),
(685, 11, 635.00, NULL, 10.00, 0, '2019-04-09 06:04:49', '2019-04-11 07:02:40'),
(686, 12, 905.00, NULL, 10.00, 0, '2019-04-09 06:04:49', '2019-04-11 07:02:40'),
(687, 13, 515.00, NULL, 2.50, 0, '2019-04-09 06:04:49', '2019-04-11 07:02:40'),
(688, 3, 525.00, NULL, -2.50, 0, '2019-04-09 10:41:19', '2019-04-10 06:34:12'),
(689, 9, 2090.00, NULL, 20.00, 0, '2019-04-09 10:41:19', '2019-04-10 06:34:12'),
(690, 15, 64.03, NULL, 0.55, 0, '2019-04-09 14:09:35', '2019-04-10 16:17:18'),
(691, 16, 70.54, NULL, -0.24, 0, '2019-04-09 14:09:35', '2019-04-10 16:17:18'),
(692, 1, 547.50, NULL, -2.50, 0, '2019-04-10 06:34:12', '2019-04-11 07:02:40'),
(693, 2, 552.50, NULL, -2.50, 0, '2019-04-10 06:34:12', '2019-04-11 07:02:40'),
(694, 3, 530.00, NULL, 5.00, 0, '2019-04-10 06:34:12', '2019-04-11 07:02:40'),
(695, 7, 472.50, NULL, -2.50, 0, '2019-04-10 06:34:12', '2019-04-11 07:02:40'),
(696, 9, 2100.00, NULL, 10.00, 0, '2019-04-10 06:34:12', '2019-04-11 07:02:40'),
(697, 15, 64.38, NULL, 0.35, 0, '2019-04-10 16:17:18', '2019-04-11 15:04:51'),
(698, 16, 71.43, NULL, 0.89, 0, '2019-04-10 16:17:18', '2019-04-11 15:04:51'),
(699, 1, 542.50, NULL, -5.00, 0, '2019-04-11 07:02:40', '2019-04-12 07:31:12'),
(700, 2, 547.50, NULL, -5.00, 0, '2019-04-11 07:02:40', '2019-04-12 07:31:12'),
(701, 3, 520.00, NULL, -10.00, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(702, 7, 465.00, NULL, -7.50, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(703, 8, 700.00, NULL, -35.00, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(704, 9, 2060.00, NULL, -40.00, 0, '2019-04-11 07:02:40', '2019-04-12 11:59:01'),
(705, 10, 140.00, NULL, -12.00, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(706, 11, 600.00, NULL, -35.00, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(707, 12, 870.00, NULL, -35.00, 0, '2019-04-11 07:02:40', '2019-04-11 12:03:54'),
(708, 13, 510.00, NULL, -5.00, 0, '2019-04-11 07:02:40', '2019-04-12 07:31:12'),
(709, 3, 510.00, NULL, -10.00, 0, '2019-04-11 12:03:54', '2019-04-15 12:45:33'),
(710, 7, 462.50, NULL, -2.50, 0, '2019-04-11 12:03:54', '2019-04-15 07:53:37'),
(711, 8, 670.00, NULL, -30.00, 0, '2019-04-11 12:03:54', '2019-04-12 11:59:01'),
(712, 10, 138.00, NULL, -2.00, 0, '2019-04-11 12:03:54', '2019-04-12 11:59:01'),
(713, 11, 570.00, NULL, -30.00, 0, '2019-04-11 12:03:54', '2019-04-12 11:59:01'),
(714, 12, 850.00, NULL, -20.00, 0, '2019-04-11 12:03:54', '2019-04-12 11:59:01'),
(715, 15, 64.84, NULL, 0.46, 0, '2019-04-11 15:04:51', '2019-04-11 15:05:33'),
(716, 16, 71.27, NULL, -0.16, 0, '2019-04-11 15:04:51', '2019-04-12 15:51:42'),
(717, 15, 63.84, NULL, -1.00, 0, '2019-04-11 15:05:33', '2019-04-12 15:51:42'),
(718, 1, 540.00, NULL, -2.50, 0, '2019-04-12 07:31:12', '2019-04-15 07:53:37'),
(719, 2, 545.00, NULL, -2.50, 0, '2019-04-12 07:31:12', '2019-04-15 07:53:37'),
(720, 13, 512.50, NULL, 2.50, 0, '2019-04-12 07:31:12', '2019-04-15 07:53:37'),
(721, 8, 685.00, NULL, 15.00, 0, '2019-04-12 11:59:01', '2019-04-15 07:53:37'),
(722, 9, 2050.00, NULL, -10.00, 0, '2019-04-12 11:59:01', '2019-04-15 07:53:37'),
(723, 10, 141.00, NULL, 3.00, 0, '2019-04-12 11:59:01', '2019-04-15 07:53:37'),
(724, 11, 585.00, NULL, 15.00, 0, '2019-04-12 11:59:01', '2019-04-15 07:53:37'),
(725, 12, 865.00, NULL, 15.00, 0, '2019-04-12 11:59:01', '2019-04-15 07:53:37'),
(726, 15, 64.31, NULL, 0.47, 0, '2019-04-12 15:51:42', '2019-04-15 15:50:43'),
(727, 16, 71.63, NULL, 0.36, 0, '2019-04-12 15:51:42', '2019-04-15 15:50:43'),
(728, 1, 537.50, NULL, -2.50, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(729, 2, 542.50, NULL, -2.50, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(730, 7, 460.00, NULL, -2.50, 0, '2019-04-15 07:53:37', '2019-04-17 09:25:12'),
(731, 8, 670.00, NULL, -15.00, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(732, 9, 2060.00, NULL, 10.00, 0, '2019-04-15 07:53:37', '2019-04-16 07:09:52'),
(733, 10, 142.00, NULL, 1.00, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(734, 11, 570.00, NULL, -15.00, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(735, 12, 820.00, NULL, -45.00, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(736, 13, 510.00, NULL, -2.50, 0, '2019-04-15 07:53:37', '2019-04-15 12:45:33'),
(737, 1, 535.00, NULL, -2.50, 0, '2019-04-15 12:45:33', '2019-04-17 10:54:08'),
(738, 2, 540.00, NULL, -2.50, 0, '2019-04-15 12:45:33', '2019-04-17 09:25:12'),
(739, 3, 505.00, NULL, -5.00, 0, '2019-04-15 12:45:33', '2019-04-16 07:09:52'),
(740, 8, 650.00, NULL, -20.00, 0, '2019-04-15 12:45:33', '2019-04-16 12:12:13'),
(741, 10, 135.00, NULL, -7.00, 0, '2019-04-15 12:45:33', '2019-04-16 07:09:52'),
(742, 11, 550.00, NULL, -20.00, 0, '2019-04-15 12:45:33', '2019-04-16 07:09:52'),
(743, 12, 800.00, NULL, -20.00, 0, '2019-04-15 12:45:33', '2019-04-16 12:12:13'),
(744, 13, 507.50, NULL, -2.50, 0, '2019-04-15 12:45:33', '2019-04-16 07:09:52'),
(745, 15, 63.17, NULL, -1.14, 0, '2019-04-15 15:50:43', '2019-04-17 10:04:25'),
(746, 16, 71.07, NULL, -0.56, 0, '2019-04-15 15:50:43', '2019-04-17 10:04:25'),
(747, 5, 630.00, 660.00, -5.00, 0, '2019-04-15 15:51:54', '2019-04-25 12:57:52'),
(748, 3, 500.00, NULL, -5.00, 0, '2019-04-16 07:09:52', '2019-04-17 09:25:12'),
(749, 9, 2040.00, NULL, -20.00, 0, '2019-04-16 07:09:52', '2019-04-16 12:12:13'),
(750, 10, 134.00, NULL, -1.00, 0, '2019-04-16 07:09:52', '2019-04-16 12:12:13'),
(751, 11, 560.00, NULL, 10.00, 0, '2019-04-16 07:09:52', '2019-04-16 12:12:13'),
(752, 13, 505.00, NULL, -2.50, 0, '2019-04-16 07:09:52', '2019-04-17 10:54:08'),
(753, 8, 655.00, NULL, 5.00, 0, '2019-04-16 12:12:13', '2019-04-17 09:25:12'),
(754, 9, 2050.00, NULL, 10.00, 0, '2019-04-16 12:12:13', '2019-04-17 09:25:12'),
(755, 10, 137.00, NULL, 3.00, 0, '2019-04-16 12:12:13', '2019-04-17 09:25:12'),
(756, 11, 565.00, NULL, 5.00, 0, '2019-04-16 12:12:13', '2019-04-17 09:25:12'),
(757, 12, 805.00, NULL, 5.00, 0, '2019-04-16 12:12:13', '2019-04-17 09:25:12'),
(758, 2, 545.00, NULL, 5.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(759, 3, 502.50, NULL, 2.50, 0, '2019-04-17 09:25:12', '2019-04-18 05:47:26'),
(760, 7, 465.00, NULL, 5.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(761, 8, 665.00, NULL, 10.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(762, 9, 2070.00, NULL, 20.00, 0, '2019-04-17 09:25:12', '2019-04-18 05:47:26'),
(763, 10, 140.00, NULL, 3.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(764, 11, 575.00, NULL, 10.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(765, 12, 815.00, NULL, 10.00, 0, '2019-04-17 09:25:12', '2019-04-17 10:54:08'),
(766, 15, 64.45, NULL, 1.28, 0, '2019-04-17 10:04:25', '2019-04-18 17:14:40'),
(767, 16, 72.11, NULL, 1.04, 0, '2019-04-17 10:04:25', '2019-04-18 17:14:40'),
(768, 1, 537.50, NULL, 2.50, 0, '2019-04-17 10:54:08', '2019-04-19 11:19:56'),
(769, 2, 542.50, NULL, -2.50, 0, '2019-04-17 10:54:08', '2019-04-19 11:19:56'),
(770, 7, 462.50, NULL, -2.50, 0, '2019-04-17 10:54:08', '2019-04-18 05:47:26'),
(771, 8, 675.00, NULL, 10.00, 0, '2019-04-17 10:54:08', '2019-04-18 05:47:26'),
(772, 10, 143.00, NULL, 3.00, 0, '2019-04-17 10:54:08', '2019-04-18 05:47:26'),
(773, 11, 585.00, NULL, 10.00, 0, '2019-04-17 10:54:08', '2019-04-18 05:47:26'),
(774, 12, 825.00, NULL, 10.00, 0, '2019-04-17 10:54:08', '2019-04-18 05:47:26'),
(775, 13, 512.50, NULL, 7.50, 0, '2019-04-17 10:54:08', '2019-04-22 06:03:33'),
(776, 3, 500.00, NULL, -2.50, 0, '2019-04-18 05:47:26', '2019-04-23 06:32:27'),
(777, 7, 460.00, NULL, -2.50, 0, '2019-04-18 05:47:26', '2019-04-22 13:19:18'),
(778, 8, 670.00, NULL, -5.00, 0, '2019-04-18 05:47:26', '2019-04-19 11:19:56'),
(779, 9, 2110.00, NULL, 40.00, 0, '2019-04-18 05:47:26', '2019-04-18 16:32:42'),
(780, 10, 142.00, NULL, -1.00, 0, '2019-04-18 05:47:26', '2019-04-19 11:19:56'),
(781, 11, 580.00, NULL, -5.00, 0, '2019-04-18 05:47:26', '2019-04-19 11:19:56'),
(782, 12, 820.00, NULL, -5.00, 0, '2019-04-18 05:47:26', '2019-04-19 11:19:56'),
(783, 9, 2070.00, NULL, -40.00, 0, '2019-04-18 16:32:42', '2019-04-19 11:19:56'),
(784, 15, 63.74, NULL, -0.71, 0, '2019-04-18 17:14:40', '2019-04-22 14:25:02'),
(785, 16, 71.68, NULL, -0.43, 0, '2019-04-18 17:14:40', '2019-04-22 14:25:02'),
(786, 1, 540.00, NULL, 2.50, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(787, 2, 545.00, NULL, 2.50, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(788, 8, 675.00, NULL, 5.00, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(789, 9, 2080.00, NULL, 10.00, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(790, 10, 148.00, NULL, 6.00, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(791, 11, 585.00, NULL, 5.00, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(792, 12, 825.00, NULL, 5.00, 0, '2019-04-19 11:19:56', '2019-04-22 06:03:33'),
(793, 1, 535.00, NULL, -5.00, 0, '2019-04-22 06:03:33', '2019-04-22 13:19:18'),
(794, 2, 540.00, NULL, -5.00, 0, '2019-04-22 06:03:33', '2019-04-22 13:19:18'),
(795, 8, 660.00, NULL, -15.00, 0, '2019-04-22 06:03:33', '2019-04-24 06:28:15'),
(796, 9, 2100.00, NULL, 20.00, 0, '2019-04-22 06:03:33', '2019-04-22 13:19:18'),
(797, 10, 144.00, NULL, -4.00, 0, '2019-04-22 06:03:33', '2019-04-23 06:32:27'),
(798, 11, 570.00, NULL, -15.00, 0, '2019-04-22 06:03:33', '2019-04-24 06:28:15'),
(799, 12, 810.00, NULL, -15.00, 0, '2019-04-22 06:03:33', '2019-04-24 06:28:15'),
(800, 13, 510.00, NULL, -2.50, 0, '2019-04-22 06:03:33', '2019-04-23 11:54:27'),
(801, 1, 532.50, NULL, -2.50, 0, '2019-04-22 13:19:18', '2019-04-24 06:28:15'),
(802, 2, 537.50, NULL, -2.50, 0, '2019-04-22 13:19:18', '2019-04-24 06:28:15'),
(803, 7, 457.50, NULL, -2.50, 0, '2019-04-22 13:19:18', '2019-04-23 06:32:27'),
(804, 9, 2090.00, NULL, -10.00, 0, '2019-04-22 13:19:18', '2019-04-23 06:32:27'),
(805, 15, 65.57, NULL, 1.83, 0, '2019-04-22 14:25:02', '2019-04-24 11:44:31'),
(806, 16, 73.96, NULL, 2.28, 0, '2019-04-22 14:25:02', '2019-04-24 11:44:31'),
(807, 3, 497.50, NULL, -2.50, 0, '2019-04-23 06:32:27', '2019-04-23 11:54:27'),
(808, 7, 455.00, NULL, -2.50, 0, '2019-04-23 06:32:27', '2019-04-24 06:28:15'),
(809, 9, 2070.00, NULL, -20.00, 0, '2019-04-23 06:32:27', '2019-04-25 06:57:16'),
(810, 10, 143.00, NULL, -1.00, 0, '2019-04-23 06:32:27', '2019-04-24 06:28:15'),
(811, 3, 495.00, NULL, -2.50, 0, '2019-04-23 11:54:27', '2019-04-24 06:28:15'),
(812, 13, 507.50, NULL, -2.50, 0, '2019-04-23 11:54:27', '2019-04-24 06:28:15'),
(813, 1, 525.00, NULL, -7.50, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(814, 2, 530.00, NULL, -7.50, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(815, 3, 490.00, NULL, -5.00, 0, '2019-04-24 06:28:15', '2019-04-25 06:57:16'),
(816, 7, 445.00, NULL, -10.00, 0, '2019-04-24 06:28:15', '2019-04-26 10:13:41'),
(817, 8, 650.00, NULL, -10.00, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(818, 10, 140.00, NULL, -3.00, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(819, 11, 560.00, NULL, -10.00, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(820, 12, 800.00, NULL, -10.00, 0, '2019-04-24 06:28:15', '2019-04-24 11:41:11'),
(821, 13, 502.50, NULL, -5.00, 0, '2019-04-24 06:28:15', '2019-04-26 10:13:41'),
(822, 1, 530.00, NULL, 5.00, 0, '2019-04-24 11:41:11', '2019-04-26 10:13:41'),
(823, 2, 535.00, NULL, 5.00, 0, '2019-04-24 11:41:11', '2019-04-26 10:13:41'),
(824, 8, 655.00, NULL, 5.00, 0, '2019-04-24 11:41:11', '2019-04-25 06:57:16'),
(825, 10, 141.00, NULL, 1.00, 0, '2019-04-24 11:41:11', '2019-04-25 06:57:16'),
(826, 11, 565.00, NULL, 5.00, 0, '2019-04-24 11:41:11', '2019-04-25 06:57:16'),
(827, 12, 805.00, NULL, 5.00, 0, '2019-04-24 11:41:11', '2019-04-25 06:57:16'),
(828, 15, 65.93, NULL, 0.36, 0, '2019-04-24 11:44:31', '2019-04-26 10:17:48'),
(829, 16, 73.71, NULL, -0.25, 0, '2019-04-24 11:44:31', '2019-04-26 10:17:48'),
(830, 3, 485.00, NULL, -5.00, 0, '2019-04-25 06:57:16', '2019-04-26 10:13:41'),
(831, 8, 650.00, NULL, -5.00, 0, '2019-04-25 06:57:16', '2019-04-26 10:13:41'),
(832, 9, 2050.00, NULL, -20.00, 0, '2019-04-25 06:57:16', '2019-04-26 14:08:19'),
(833, 10, 140.00, NULL, -1.00, 0, '2019-04-25 06:57:16', '2019-04-26 10:13:41'),
(834, 11, 560.00, NULL, -5.00, 0, '2019-04-25 06:57:16', '2019-04-26 10:13:41'),
(835, 12, 800.00, NULL, -5.00, 0, '2019-04-25 06:57:16', '2019-04-26 10:13:41'),
(836, 5, 600.00, 640.00, -25.00, 0, '2019-04-25 12:57:52', '2019-04-26 10:18:39'),
(837, 6, 600.00, 620.00, -5.00, 0, '2019-04-25 13:01:28', '2019-04-26 10:19:16'),
(838, 1, 525.00, NULL, -5.00, 0, '2019-04-26 10:13:41', '2019-04-26 14:08:19'),
(839, 2, 530.00, NULL, -5.00, 0, '2019-04-26 10:13:41', '2019-04-26 14:08:19'),
(840, 3, 480.00, NULL, -5.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(841, 7, 442.50, NULL, -2.50, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(842, 8, 655.00, NULL, 5.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(843, 10, 139.00, NULL, -1.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(844, 11, 565.00, NULL, 5.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(845, 12, 805.00, NULL, 5.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(846, 13, 492.50, NULL, -10.00, 0, '2019-04-26 10:13:41', '2019-04-29 05:14:13'),
(847, 15, 64.12, NULL, -1.81, 0, '2019-04-26 10:17:48', '2019-04-30 16:55:16'),
(848, 16, 72.37, NULL, -1.34, 0, '2019-04-26 10:17:48', '2019-04-30 16:55:16'),
(849, 5, 610.00, 650.00, 10.00, 0, '2019-04-26 10:18:39', '2019-04-30 16:32:09'),
(850, 6, 600.00, 630.00, 5.00, 0, '2019-04-26 10:19:16', '2019-04-30 16:47:46'),
(851, 1, 522.50, NULL, -2.50, 0, '2019-04-26 14:08:19', '2019-04-30 05:55:06'),
(852, 2, 527.50, NULL, -2.50, 0, '2019-04-26 14:08:19', '2019-04-30 05:55:06'),
(853, 9, 2040.00, NULL, -10.00, 0, '2019-04-26 14:08:19', '2019-04-29 05:14:13'),
(858, 3, 482.50, NULL, 2.50, 0, '2019-04-29 05:14:13', '2019-04-29 11:07:07'),
(859, 7, 440.00, NULL, -2.50, 0, '2019-04-29 05:14:13', '2019-04-30 05:55:06'),
(860, 8, 650.00, NULL, -5.00, 0, '2019-04-29 05:14:13', '2019-04-30 05:55:06'),
(861, 9, 2020.00, NULL, -20.00, 0, '2019-04-29 05:14:13', '2019-04-30 16:35:25'),
(862, 10, 138.00, NULL, -1.00, 0, '2019-04-29 05:14:13', '2019-04-30 05:55:06'),
(863, 11, 560.00, NULL, -5.00, 0, '2019-04-29 05:14:13', '2019-04-30 05:55:06'),
(864, 12, 800.00, NULL, -5.00, 0, '2019-04-29 05:14:13', '2019-04-30 05:55:06'),
(865, 13, 490.00, NULL, -2.50, 0, '2019-04-29 05:14:13', '2019-04-29 11:07:07'),
(866, 3, 485.00, NULL, 2.50, 0, '2019-04-29 11:07:07', '2019-04-30 16:35:25'),
(867, 13, 487.50, NULL, -2.50, 0, '2019-04-29 11:07:07', '2019-04-30 05:55:06'),
(868, 1, 520.00, NULL, -2.50, 0, '2019-04-30 05:55:06', '2019-04-30 13:23:07'),
(869, 2, 525.00, NULL, -2.50, 0, '2019-04-30 05:55:06', '2019-04-30 13:34:35'),
(870, 7, 437.50, NULL, -2.50, 0, '2019-04-30 05:55:06', '2019-04-30 16:35:25'),
(871, 8, 660.00, NULL, 10.00, 0, '2019-04-30 05:55:06', '2019-04-30 16:35:25'),
(872, 10, 140.00, NULL, 2.00, 0, '2019-04-30 05:55:06', '2019-04-30 14:01:13'),
(873, 11, 570.00, NULL, 10.00, 0, '2019-04-30 05:55:06', '2019-04-30 16:35:25'),
(874, 12, 810.00, NULL, 10.00, 0, '2019-04-30 05:55:06', '2019-04-30 16:35:25'),
(875, 13, 485.00, NULL, -2.50, 0, '2019-04-30 05:55:06', '2019-04-30 16:35:25'),
(876, 14, 720.00, 750.00, -15.00, 0, '2019-04-30 06:08:19', '2019-04-30 16:52:18'),
(877, 18, 1000.00, 1050.00, -20.00, 0, '2019-04-30 06:11:27', '2019-04-30 16:54:11'),
(878, 1, 525.00, NULL, 5.00, 0, '2019-04-30 13:23:07', '2019-04-30 13:34:35'),
(879, 1, 520.00, NULL, -5.00, 0, '2019-04-30 13:34:35', '2019-04-30 13:54:53'),
(880, 2, 520.00, NULL, -5.00, 0, '2019-04-30 13:34:35', '2019-04-30 14:20:18'),
(881, 1, 521.00, NULL, 1.00, 0, '2019-04-30 13:54:53', '2019-04-30 13:55:08'),
(882, 1, 522.00, NULL, 1.00, 0, '2019-04-30 13:55:08', '2019-04-30 13:56:46'),
(883, 1, 523.00, NULL, 1.00, 0, '2019-04-30 13:56:46', '2019-04-30 13:57:29'),
(884, 1, 524.00, NULL, 1.00, 0, '2019-04-30 13:57:29', '2019-04-30 13:58:24'),
(885, 1, 523.00, NULL, -1.00, 0, '2019-04-30 13:58:24', '2019-04-30 13:58:52'),
(886, 1, 523.00, NULL, 0.00, 0, '2019-04-30 13:58:52', '2019-04-30 13:59:48'),
(887, 1, 522.00, NULL, -1.00, 0, '2019-04-30 13:59:48', '2019-04-30 14:00:43'),
(888, 1, 525.00, NULL, 3.00, 0, '2019-04-30 14:00:43', '2019-04-30 14:00:58'),
(889, 1, 524.00, NULL, -1.00, 0, '2019-04-30 14:00:58', '2019-04-30 14:01:56'),
(890, 10, 141.00, NULL, 1.00, 0, '2019-04-30 14:01:13', '2019-04-30 16:35:25'),
(891, 1, 526.00, NULL, 2.00, 0, '2019-04-30 14:01:56', '2019-04-30 14:20:18'),
(892, 1, 525.00, NULL, -1.00, 0, '2019-04-30 14:20:18', '2019-04-30 16:29:03'),
(893, 2, 525.00, NULL, 5.00, 0, '2019-04-30 14:20:18', '2019-04-30 16:29:03'),
(894, 1, 530.00, NULL, 5.00, 0, '2019-04-30 16:29:03', '2019-04-30 16:35:25'),
(895, 2, 530.00, NULL, 5.00, 0, '2019-04-30 16:29:03', '2019-04-30 16:35:25'),
(896, 5, 610.00, 660.00, 5.00, 0, '2019-04-30 16:32:09', '2019-04-30 16:45:54'),
(897, 1, 520.00, NULL, -10.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(898, 2, 540.00, NULL, 10.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(899, 3, 480.00, NULL, -5.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(900, 7, 430.00, NULL, -7.50, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(901, 8, 670.00, NULL, 10.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(902, 9, 2000.00, NULL, -20.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(903, 10, 140.00, NULL, -1.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(904, 11, 580.00, NULL, 10.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(905, 12, 800.00, NULL, -10.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(906, 13, 490.00, NULL, 5.00, 0, '2019-04-30 16:35:25', '2019-04-30 16:38:32'),
(907, 1, 525.00, NULL, 5.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:43:25'),
(908, 2, 530.00, NULL, -10.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:42:57'),
(909, 3, 485.00, NULL, 5.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:43:14'),
(910, 7, 440.00, NULL, 10.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:44:34'),
(911, 8, 680.00, NULL, 10.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:45:28'),
(912, 9, 2020.00, NULL, 20.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:40:56'),
(913, 10, 145.00, NULL, 5.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:42:21'),
(914, 11, 585.00, NULL, 5.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:45:15'),
(915, 12, 810.00, NULL, 10.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:42:43'),
(916, 13, 500.00, NULL, 10.00, 0, '2019-04-30 16:38:32', '2019-04-30 16:44:59'),
(917, 9, 2050.00, NULL, 30.00, 0, '2019-04-30 16:40:56', '2019-04-30 16:41:31'),
(918, 9, 2020.00, NULL, -30.00, 0, '2019-04-30 16:41:31', '2019-04-30 16:43:38'),
(919, 10, 140.00, NULL, -5.00, 0, '2019-04-30 16:42:21', '2019-05-01 14:50:08'),
(920, 12, 800.00, NULL, -10.00, 0, '2019-04-30 16:42:43', '2019-05-01 12:59:35'),
(921, 2, 525.00, NULL, -5.00, 0, '2019-04-30 16:42:57', '2019-05-01 05:35:18'),
(922, 3, 490.00, NULL, 5.00, 0, '2019-04-30 16:43:14', '2019-05-01 14:50:08'),
(923, 1, 530.00, NULL, 5.00, 0, '2019-04-30 16:43:25', '2019-04-30 16:46:54'),
(924, 9, 2010.00, NULL, -10.00, 0, '2019-04-30 16:43:38', '2019-04-30 17:00:26'),
(925, 7, 445.00, NULL, 5.00, 0, '2019-04-30 16:44:34', '2019-05-01 13:03:35'),
(926, 13, 495.00, NULL, -5.00, 0, '2019-04-30 16:44:59', '2019-05-01 14:50:08'),
(927, 11, 590.00, NULL, 5.00, 0, '2019-04-30 16:45:15', '2019-05-01 14:50:08'),
(928, 8, 685.00, NULL, 5.00, 0, '2019-04-30 16:45:28', '2019-05-01 14:50:08'),
(929, 5, 610.00, 630.00, -15.00, 0, '2019-04-30 16:45:54', '2019-04-30 16:46:13'),
(930, 5, 610.00, 640.00, 5.00, 0, '2019-04-30 16:46:13', '2019-05-01 05:37:54'),
(931, 1, 540.00, NULL, 10.00, 0, '2019-04-30 16:46:54', '2019-05-01 05:35:18'),
(932, 6, 620.00, 640.00, 15.00, 0, '2019-04-30 16:47:46', '2019-04-30 16:48:20'),
(933, 6, 630.00, 650.00, 10.00, 0, '2019-04-30 16:48:20', '2019-04-30 16:49:00'),
(934, 6, 630.00, 640.00, -5.00, 0, '2019-04-30 16:49:00', '2019-04-30 16:49:47'),
(935, 6, 600.00, 630.00, -20.00, 0, '2019-04-30 16:49:47', '2019-05-01 15:09:46'),
(936, 4, 900.00, 940.00, -5.00, 0, '2019-04-30 16:50:51', '2019-04-30 16:51:21'),
(937, 4, 850.00, 900.00, -45.00, 0, '2019-04-30 16:51:21', '2019-05-01 15:10:26'),
(938, 14, 720.00, 760.00, 5.00, 0, '2019-04-30 16:52:18', '2019-04-30 16:52:48'),
(939, 14, 700.00, 730.00, -25.00, 0, '2019-04-30 16:52:48', '2019-05-01 15:12:03'),
(940, 18, 1000.00, 1040.00, -5.00, 0, '2019-04-30 16:54:11', '2019-04-30 16:54:46'),
(941, 18, 990.00, 1030.00, -10.00, 0, '2019-04-30 16:54:46', '2019-05-01 15:12:36'),
(942, 15, 64.05, NULL, -0.07, 0, '2019-04-30 16:55:16', '2019-04-30 16:56:11'),
(943, 16, 72.03, NULL, -0.34, 0, '2019-04-30 16:55:16', '2019-04-30 16:56:11'),
(944, 15, 64.07, NULL, 0.02, 0, '2019-04-30 16:56:11', '2019-04-30 16:58:01'),
(945, 16, 72.08, NULL, 0.05, 0, '2019-04-30 16:56:11', '2019-04-30 16:57:02'),
(946, 16, 72.10, NULL, 0.02, 0, '2019-04-30 16:57:02', '2019-04-30 16:57:17'),
(947, 16, 73.00, NULL, 0.90, 0, '2019-04-30 16:57:17', '2019-04-30 16:59:16'),
(948, 15, 64.10, NULL, 0.03, 0, '2019-04-30 16:58:01', '2019-04-30 16:59:16'),
(949, 15, 64.03, NULL, -0.07, 0, '2019-04-30 16:59:16', '2019-05-01 15:13:32'),
(950, 16, 74.05, NULL, 1.05, 0, '2019-04-30 16:59:16', '2019-04-30 16:59:53'),
(951, 16, 74.08, NULL, 0.03, 0, '2019-04-30 16:59:53', '2019-05-01 15:13:32'),
(952, 9, 2050.00, NULL, 40.00, 0, '2019-04-30 17:00:26', '2019-04-30 17:01:00'),
(953, 9, 2010.00, NULL, -40.00, 0, '2019-04-30 17:01:00', '2019-05-01 14:50:08'),
(954, 1, 525.00, NULL, -15.00, 0, '2019-05-01 05:35:18', '2019-05-01 05:36:36'),
(955, 2, 530.00, NULL, 5.00, 0, '2019-05-01 05:35:18', '2019-05-01 05:37:10'),
(956, 1, 530.00, NULL, 5.00, 0, '2019-05-01 05:36:36', '2019-05-01 07:11:14'),
(957, 2, 525.00, NULL, -5.00, 0, '2019-05-01 05:37:10', '2019-05-01 06:58:31'),
(958, 5, 610.00, 650.00, 5.00, 0, '2019-05-01 05:37:54', '2019-05-01 05:39:07'),
(959, 5, 600.00, 640.00, -10.00, 0, '2019-05-01 05:39:07', '2019-05-01 15:09:12'),
(960, 2, 524.00, NULL, -1.00, 0, '2019-05-01 06:58:31', '2019-05-01 08:07:48'),
(961, 1, 531.00, NULL, 1.00, 0, '2019-05-01 07:11:14', '2019-05-01 07:11:38'),
(962, 1, 532.00, NULL, 1.00, 0, '2019-05-01 07:11:38', '2019-05-01 07:31:27'),
(963, 1, 531.00, NULL, -1.00, 0, '2019-05-01 07:31:27', '2019-05-01 07:31:56'),
(964, 1, 532.00, NULL, 1.00, 0, '2019-05-01 07:31:56', '2019-05-01 07:32:30'),
(965, 1, 531.00, NULL, -1.00, 0, '2019-05-01 07:32:30', '2019-05-01 07:32:56'),
(966, 1, 530.00, NULL, -1.00, 0, '2019-05-01 07:32:56', '2019-05-01 07:37:55'),
(967, 1, 532.00, NULL, 2.00, 0, '2019-05-01 07:37:55', '2019-05-01 07:48:46'),
(968, 1, 531.00, NULL, -1.00, 0, '2019-05-01 07:48:46', '2019-05-01 07:52:56'),
(969, 1, 530.00, NULL, -1.00, 0, '2019-05-01 07:52:56', '2019-05-01 07:53:13'),
(970, 1, 531.00, NULL, 1.00, 0, '2019-05-01 07:53:13', '2019-05-01 07:53:33'),
(971, 1, 530.00, NULL, -1.00, 0, '2019-05-01 07:53:33', '2019-05-01 07:59:53'),
(972, 1, 531.00, NULL, 1.00, 0, '2019-05-01 07:59:53', '2019-05-01 08:00:23'),
(973, 1, 534.00, NULL, 3.00, 0, '2019-05-01 08:00:23', '2019-05-01 08:00:54'),
(974, 1, 532.00, NULL, -2.00, 0, '2019-05-01 08:00:54', '2019-05-01 08:05:40'),
(975, 1, 532.00, NULL, 0.00, 0, '2019-05-01 08:05:40', '2019-05-01 08:08:31'),
(976, 2, 523.00, NULL, -1.00, 0, '2019-05-01 08:07:48', '2019-05-01 14:37:29'),
(977, 1, 531.00, NULL, -1.00, 0, '2019-05-01 08:08:31', '2019-05-01 08:52:48'),
(978, 1, 532.00, NULL, 1.00, 0, '2019-05-01 08:52:48', '2019-05-01 09:51:12'),
(979, 1, 531.00, NULL, -1.00, 0, '2019-05-01 09:51:12', '2019-05-01 09:51:41'),
(980, 1, 532.00, NULL, 1.00, 0, '2019-05-01 09:51:41', '2019-05-01 09:51:59'),
(981, 1, 534.00, NULL, 2.00, 0, '2019-05-01 09:51:59', '2019-05-01 09:52:20'),
(982, 1, 536.00, NULL, 2.00, 0, '2019-05-01 09:52:20', '2019-05-01 09:54:16'),
(983, 1, 532.00, NULL, -4.00, 0, '2019-05-01 09:54:16', '2019-05-01 09:54:42'),
(984, 1, 534.00, NULL, 2.00, 0, '2019-05-01 09:54:42', '2019-05-01 09:55:01'),
(985, 1, 532.00, NULL, -2.00, 0, '2019-05-01 09:55:01', '2019-05-01 09:55:19'),
(986, 1, 531.00, NULL, -1.00, 0, '2019-05-01 09:55:19', '2019-05-01 09:55:54'),
(987, 1, 532.00, NULL, 1.00, 0, '2019-05-01 09:55:54', '2019-05-01 09:56:20'),
(988, 1, 531.00, NULL, -1.00, 0, '2019-05-01 09:56:20', '2019-05-01 09:56:53'),
(989, 1, 532.00, NULL, 1.00, 0, '2019-05-01 09:56:53', '2019-05-01 10:11:12'),
(990, 1, 531.00, NULL, -1.00, 0, '2019-05-01 10:11:12', '2019-05-01 10:28:11'),
(991, 1, 532.00, NULL, 1.00, 0, '2019-05-01 10:28:11', '2019-05-01 10:29:59'),
(992, 1, 530.00, NULL, -2.00, 0, '2019-05-01 10:29:59', '2019-05-01 10:34:55'),
(993, 1, 531.00, NULL, 1.00, 0, '2019-05-01 10:34:55', '2019-05-01 10:38:02'),
(994, 1, 532.00, NULL, 1.00, 0, '2019-05-01 10:38:02', '2019-05-01 10:38:52'),
(995, 1, 531.00, NULL, -1.00, 0, '2019-05-01 10:38:52', '2019-05-01 10:39:58'),
(996, 1, 532.00, NULL, 1.00, 0, '2019-05-01 10:39:58', '2019-05-01 10:40:59'),
(997, 1, 531.00, NULL, -1.00, 0, '2019-05-01 10:40:59', '2019-05-01 10:45:34'),
(998, 1, 530.00, NULL, -1.00, 0, '2019-05-01 10:45:34', '2019-05-01 10:46:57'),
(999, 1, 531.00, NULL, 1.00, 0, '2019-05-01 10:46:57', '2019-05-01 10:57:23'),
(1000, 1, 532.00, NULL, 1.00, 0, '2019-05-01 10:57:23', '2019-05-01 10:57:38'),
(1001, 1, 531.00, NULL, -1.00, 0, '2019-05-01 10:57:38', '2019-05-01 10:57:59'),
(1002, 1, 532.00, NULL, 1.00, 0, '2019-05-01 10:57:59', '2019-05-01 11:02:04'),
(1003, 1, 531.00, NULL, -1.00, 0, '2019-05-01 11:02:04', '2019-05-01 11:05:21'),
(1004, 1, 530.00, NULL, -1.00, 0, '2019-05-01 11:05:21', '2019-05-01 11:05:50'),
(1005, 1, 531.00, NULL, 1.00, 0, '2019-05-01 11:05:50', '2019-05-01 11:06:10'),
(1006, 1, 530.00, NULL, -1.00, 0, '2019-05-01 11:06:10', '2019-05-01 11:16:37'),
(1007, 1, 531.00, NULL, 1.00, 0, '2019-05-01 11:16:37', '2019-05-01 11:17:05'),
(1008, 1, 530.00, NULL, -1.00, 0, '2019-05-01 11:17:05', '2019-05-01 11:19:32'),
(1009, 1, 531.00, NULL, 1.00, 0, '2019-05-01 11:19:32', '2019-05-01 11:33:13'),
(1010, 1, 532.00, NULL, 1.00, 0, '2019-05-01 11:33:13', '2019-05-01 11:34:04'),
(1011, 1, 531.00, NULL, -1.00, 0, '2019-05-01 11:34:04', '2019-05-01 11:34:40'),
(1012, 1, 532.00, NULL, 1.00, 0, '2019-05-01 11:34:40', '2019-05-01 11:38:18'),
(1013, 1, 532.00, NULL, 0.00, 0, '2019-05-01 11:38:18', '2019-05-01 11:39:21'),
(1014, 1, 531.00, NULL, -1.00, 0, '2019-05-01 11:39:21', '2019-05-01 11:40:09'),
(1015, 1, 532.00, NULL, 1.00, 0, '2019-05-01 11:40:09', '2019-05-01 11:40:21'),
(1016, 1, 531.00, NULL, -1.00, 0, '2019-05-01 11:40:21', '2019-05-01 11:40:34'),
(1017, 1, 534.00, NULL, 3.00, 0, '2019-05-01 11:40:34', '2019-05-01 12:05:00'),
(1018, 1, 540.00, NULL, 6.00, 0, '2019-05-01 12:05:00', '2019-05-01 12:40:22'),
(1019, 1, 541.00, NULL, 1.00, 0, '2019-05-01 12:40:22', '2019-05-01 12:40:47'),
(1020, 1, 531.00, NULL, -10.00, 0, '2019-05-01 12:40:47', '2019-05-01 12:41:22'),
(1021, 1, 532.00, NULL, 1.00, 0, '2019-05-01 12:41:22', '2019-05-01 12:42:00'),
(1022, 1, 531.00, NULL, -1.00, 0, '2019-05-01 12:42:00', '2019-05-01 12:42:11'),
(1023, 1, 532.00, NULL, 1.00, 0, '2019-05-01 12:42:11', '2019-05-01 12:42:49'),
(1024, 1, 532.00, NULL, 0.00, 0, '2019-05-01 12:42:49', '2019-05-01 14:37:29'),
(1025, 12, 801.00, NULL, 1.00, 0, '2019-05-01 12:59:35', '2019-05-01 13:12:32'),
(1026, 7, 449.00, NULL, 4.00, 0, '2019-05-01 13:03:35', '2019-05-01 14:50:08'),
(1027, 12, 50.00, NULL, -751.00, 0, '2019-05-01 13:12:32', '2019-05-01 13:15:00'),
(1028, 12, 800.00, NULL, 750.00, 0, '2019-05-01 13:15:00', '2019-05-01 14:50:08'),
(1029, 1, 540.00, NULL, 8.00, 0, '2019-05-01 14:37:29', '2019-05-01 14:50:08'),
(1030, 2, 530.00, NULL, 7.00, 0, '2019-05-01 14:37:29', '2019-05-01 14:50:08'),
(1031, 1, 535.00, NULL, -5.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1032, 2, 540.00, NULL, 10.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1033, 3, 500.00, NULL, 10.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1034, 7, 450.00, NULL, 1.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1035, 8, 670.00, NULL, -15.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1036, 9, 2020.00, NULL, 10.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1037, 10, 139.00, NULL, -1.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1038, 11, 600.00, NULL, 10.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1039, 12, 790.00, NULL, -10.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1040, 13, 500.00, NULL, 5.00, 0, '2019-05-01 14:50:08', '2019-05-01 15:05:17'),
(1041, 1, 540.00, NULL, 5.00, 0, '2019-05-01 15:05:17', '2019-05-01 15:54:23'),
(1042, 2, 535.00, NULL, -5.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1043, 3, 490.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1044, 7, 445.00, NULL, -5.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1045, 8, 660.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1046, 9, 2010.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1047, 10, 140.00, NULL, 1.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1048, 11, 590.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1049, 12, 780.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1050, 13, 490.00, NULL, -10.00, 0, '2019-05-01 15:05:17', '2019-05-02 06:04:46'),
(1051, 5, 600.00, 630.00, -5.00, 0, '2019-05-01 15:09:12', '2019-05-01 15:09:31'),
(1052, 5, 600.00, 640.00, 5.00, 0, '2019-05-01 15:09:31', '2019-06-12 14:25:07'),
(1053, 6, 600.00, 640.00, 5.00, 0, '2019-05-01 15:09:46', '2019-05-01 15:10:05'),
(1054, 6, 600.00, 630.00, -5.00, 1, '2019-05-01 15:10:05', '2019-05-01 15:10:05'),
(1055, 4, 850.00, 910.00, 5.00, 0, '2019-05-01 15:10:26', '2019-05-01 15:10:52'),
(1056, 4, 850.00, 900.00, -5.00, 0, '2019-05-01 15:10:52', '2019-05-01 15:11:27'),
(1057, 4, 850.00, 920.00, 10.00, 1, '2019-05-01 15:11:27', '2019-05-01 15:11:27'),
(1058, 14, 700.00, 740.00, 5.00, 0, '2019-05-01 15:12:03', '2019-05-01 15:12:21'),
(1059, 14, 700.00, 730.00, -5.00, 1, '2019-05-01 15:12:21', '2019-05-01 15:12:21'),
(1060, 18, 990.00, 1020.00, -5.00, 0, '2019-05-01 15:12:36', '2019-05-01 15:13:10'),
(1061, 18, 990.00, 1030.00, 5.00, 1, '2019-05-01 15:13:10', '2019-05-01 15:13:10'),
(1062, 15, 64.00, NULL, -0.03, 0, '2019-05-01 15:13:32', '2019-05-01 15:21:17'),
(1063, 16, 74.01, NULL, -0.07, 0, '2019-05-01 15:13:32', '2019-05-01 15:21:17'),
(1064, 15, 64.05, NULL, 0.05, 0, '2019-05-01 15:21:17', '2019-05-01 15:23:20'),
(1065, 16, 74.06, NULL, 0.05, 0, '2019-05-01 15:21:17', '2019-05-01 15:23:38'),
(1066, 15, 64.50, NULL, 0.45, 0, '2019-05-01 15:23:20', '2019-05-01 15:23:53'),
(1067, 16, 74.50, NULL, 0.44, 0, '2019-05-01 15:23:38', '2019-05-01 15:24:09'),
(1068, 15, 64.20, NULL, -0.30, 0, '2019-05-01 15:23:53', '2019-05-01 15:24:09'),
(1069, 15, 64.00, NULL, -0.20, 0, '2019-05-01 15:24:09', '2019-05-01 15:24:44'),
(1070, 16, 74.00, NULL, -0.50, 1, '2019-05-01 15:24:09', '2019-05-01 15:24:09'),
(1071, 15, 64.50, NULL, 0.50, 1, '2019-05-01 15:24:44', '2019-05-01 15:24:44'),
(1072, 1, 535.00, NULL, -5.00, 0, '2019-05-01 15:54:23', '2019-05-02 06:04:46'),
(1073, 1, 540.00, NULL, 5.00, 0, '2019-05-02 06:04:46', '2019-05-03 10:14:59'),
(1074, 2, 540.00, NULL, 5.00, 0, '2019-05-02 06:04:46', '2019-05-03 10:34:28'),
(1075, 3, 500.00, NULL, 10.00, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(1076, 7, 450.00, NULL, 5.00, 0, '2019-05-02 06:04:46', '2019-06-25 11:25:47'),
(1077, 8, 670.00, NULL, 10.00, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(1078, 9, 2020.00, NULL, 10.00, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(1079, 10, 145.00, NULL, 5.00, 0, '2019-05-02 06:04:46', '2019-05-07 09:18:15'),
(1080, 11, 560.00, NULL, -30.00, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(1081, 12, 790.00, NULL, 10.00, 1, '2019-05-02 06:04:46', '2019-05-02 06:04:46'),
(1082, 13, 500.00, NULL, 10.00, 0, '2019-05-02 06:04:46', '2019-05-02 12:02:09'),
(1083, 13, 501.00, NULL, 1.00, 0, '2019-05-02 12:02:09', '2019-05-23 13:59:07'),
(1084, 1, 541.00, NULL, 1.00, 0, '2019-05-03 10:14:59', '2019-05-03 10:20:20'),
(1085, 1, 542.00, NULL, 1.00, 0, '2019-05-03 10:20:20', '2019-05-03 10:20:44'),
(1086, 1, 541.00, NULL, -1.00, 0, '2019-05-03 10:20:44', '2019-05-03 10:21:12'),
(1087, 1, 542.00, NULL, 1.00, 0, '2019-05-03 10:21:12', '2019-05-03 10:33:22'),
(1088, 1, 541.00, NULL, -1.00, 0, '2019-05-03 10:33:22', '2019-05-03 10:33:54'),
(1089, 1, 543.00, NULL, 2.00, 0, '2019-05-03 10:33:54', '2019-05-03 10:39:15'),
(1090, 2, 541.00, NULL, 1.00, 0, '2019-05-03 10:34:28', '2019-05-07 09:21:08'),
(1091, 1, 541.00, NULL, -2.00, 0, '2019-05-03 10:39:15', '2019-05-03 10:43:03'),
(1092, 1, 542.00, NULL, 1.00, 0, '2019-05-03 10:43:03', '2019-05-03 10:43:48'),
(1093, 1, 541.00, NULL, -1.00, 0, '2019-05-03 10:43:48', '2019-05-03 10:45:02'),
(1094, 1, 542.00, NULL, 1.00, 0, '2019-05-03 10:45:02', '2019-05-03 10:45:31'),
(1095, 1, 541.00, NULL, -1.00, 0, '2019-05-03 10:45:31', '2019-05-04 06:15:32'),
(1096, 1, 540.00, NULL, -1.00, 0, '2019-05-04 06:15:32', '2019-06-12 14:19:10'),
(1097, 10, 150.00, NULL, 5.00, 1, '2019-05-07 09:18:15', '2019-05-07 09:18:15'),
(1098, 2, 540.00, NULL, -1.00, 0, '2019-05-07 09:21:08', '2019-05-07 09:21:38'),
(1099, 2, 542.00, NULL, 2.00, 0, '2019-05-07 09:21:38', '2019-05-07 09:22:00'),
(1100, 2, 541.00, NULL, -1.00, 0, '2019-05-07 09:22:00', '2019-05-07 09:22:43'),
(1101, 2, 524.00, NULL, -17.00, 0, '2019-05-07 09:22:43', '2019-05-07 09:23:15'),
(1102, 2, 550.00, NULL, 26.00, 0, '2019-05-07 09:23:15', '2019-05-07 09:23:54'),
(1103, 2, 541.00, NULL, -9.00, 0, '2019-05-07 09:23:54', '2019-05-07 09:26:57'),
(1104, 2, 542.00, NULL, 1.00, 0, '2019-05-07 09:26:57', '2019-05-07 09:27:54'),
(1105, 2, 543.00, NULL, 1.00, 0, '2019-05-07 09:27:54', '2019-05-07 09:28:38'),
(1106, 2, 541.00, NULL, -2.00, 0, '2019-05-07 09:28:38', '2019-05-07 09:29:19'),
(1107, 2, 542.00, NULL, 1.00, 0, '2019-05-07 09:29:19', '2019-05-07 09:32:37'),
(1108, 2, 544.00, NULL, 2.00, 0, '2019-05-07 09:32:37', '2019-05-07 09:33:01'),
(1109, 2, 545.00, NULL, 1.00, 0, '2019-05-07 09:33:01', '2019-05-07 09:33:54'),
(1110, 2, 546.00, NULL, 1.00, 0, '2019-05-07 09:33:54', '2019-05-07 09:34:25'),
(1111, 2, 545.00, NULL, -1.00, 0, '2019-05-07 09:34:25', '2019-05-07 09:34:39'),
(1112, 2, 544.00, NULL, -1.00, 0, '2019-05-07 09:34:39', '2019-05-07 09:39:19'),
(1113, 2, 550.00, NULL, 6.00, 0, '2019-05-07 09:39:19', '2019-05-07 09:39:49'),
(1114, 2, 540.00, NULL, -10.00, 0, '2019-05-07 09:39:49', '2019-05-07 09:40:12'),
(1115, 2, 540.00, NULL, 0.00, 0, '2019-05-07 09:40:12', '2019-05-07 09:40:33'),
(1116, 2, 550.00, NULL, 10.00, 0, '2019-05-07 09:40:33', '2019-06-12 13:53:36'),
(1117, 13, 500.00, NULL, -1.00, 0, '2019-05-23 13:59:07', '2019-06-12 13:51:25'),
(1118, 13, 501.00, NULL, 1.00, 0, '2019-06-12 13:51:25', '2019-06-12 13:52:22'),
(1119, 13, 500.00, NULL, -1.00, 0, '2019-06-12 13:52:22', '2019-06-12 13:52:48'),
(1120, 13, 501.00, NULL, 1.00, 0, '2019-06-12 13:52:48', '2019-06-13 06:08:45'),
(1121, 2, 500.00, NULL, -50.00, 0, '2019-06-12 13:53:36', '2019-06-12 13:54:04'),
(1122, 2, 550.00, NULL, 50.00, 0, '2019-06-12 13:54:04', '2019-06-12 14:15:17'),
(1123, 2, 560.00, NULL, 10.00, 0, '2019-06-12 14:15:17', '2019-06-13 06:10:54'),
(1124, 1, 560.00, NULL, 20.00, 0, '2019-06-12 14:19:10', '2019-06-24 08:27:20'),
(1125, 5, 610.00, 650.00, 10.00, 1, '2019-06-12 14:25:07', '2019-06-12 14:25:07'),
(1126, 13, 550.00, NULL, 49.00, 1, '2019-06-13 06:08:45', '2019-06-13 06:08:45'),
(1127, 2, 550.00, NULL, -10.00, 0, '2019-06-13 06:10:54', '2019-06-14 05:39:10'),
(1128, 2, 551.00, NULL, 1.00, 0, '2019-06-14 05:39:10', '2019-06-24 08:27:03'),
(1129, 2, 552.00, NULL, 1.00, 0, '2019-06-24 08:27:03', '2019-06-25 07:50:49'),
(1130, 1, 551.00, NULL, -9.00, 0, '2019-06-24 08:27:20', '2019-06-25 11:05:41'),
(1131, 2, 553.00, NULL, 1.00, 0, '2019-06-25 07:50:49', '2019-06-25 07:51:20'),
(1132, 2, 554.00, NULL, 1.00, 0, '2019-06-25 07:51:20', '2019-06-25 07:51:51'),
(1133, 2, 553.00, NULL, -1.00, 0, '2019-06-25 07:51:51', '2019-06-25 07:52:08'),
(1134, 2, 554.00, NULL, 1.00, 0, '2019-06-25 07:52:08', '2019-06-25 09:24:29'),
(1135, 2, 555.00, NULL, 1.00, 0, '2019-06-25 09:24:29', '2019-06-25 10:55:59'),
(1136, 2, 554.00, NULL, -1.00, 0, '2019-06-25 10:55:59', '2019-06-25 10:56:52'),
(1137, 2, 555.00, NULL, 1.00, 0, '2019-06-25 10:56:52', '2019-06-25 11:06:31'),
(1138, 1, 555.00, NULL, 4.00, 1, '2019-06-25 11:05:41', '2019-06-25 11:05:41'),
(1139, 2, 554.00, NULL, -1.00, 0, '2019-06-25 11:06:31', '2019-06-25 11:08:06'),
(1140, 2, 551.00, 555.00, 276.00, 0, '2019-06-25 11:08:06', '2019-06-25 11:16:48'),
(1141, 2, 552.00, NULL, 1.00, 0, '2019-06-25 11:16:48', '2019-06-25 11:26:57'),
(1142, 7, 451.00, NULL, 1.00, 1, '2019-06-25 11:25:47', '2019-06-25 11:25:47'),
(1143, 2, 553.00, NULL, 1.00, 1, '2019-06-25 11:26:57', '2019-06-25 11:26:57');

-- --------------------------------------------------------

--
-- Table structure for table `social_login`
--

CREATE TABLE `social_login` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `provider_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '1:facebook; 2:google',
  `provider_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `social_user`
--

CREATE TABLE `social_user` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `provider` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '1:facebook; 2:google',
  `provider_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `social_user`
--

INSERT INTO `social_user` (`id`, `user_id`, `provider`, `provider_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, '2', '102137032062416078777', 1, '2019-04-30 11:16:39', '2019-04-30 11:16:39'),
(2, 3, '2', '118066197965173433667', 1, '2019-04-30 12:23:57', '2019-04-30 12:23:57'),
(3, 9, '2', '103631302586675593604', 1, '2019-05-02 07:34:37', '2019-05-02 07:34:37'),
(4, 10, '2', '105047237491098599268', 1, '2019-05-07 08:25:16', '2019-05-07 08:25:16'),
(5, 12, '2', '102339430852666968240', 1, '2019-06-26 04:34:06', '2019-06-26 04:34:06'),
(6, 13, '2', '111661656823982182173', 1, '2019-06-26 04:37:01', '2019-06-26 04:37:01');

-- --------------------------------------------------------

--
-- Table structure for table `ticker`
--

CREATE TABLE `ticker` (
  `category_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ticker`
--

INSERT INTO `ticker` (`category_id`, `user_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(1, 2),
(2, 2),
(3, 2),
(4, 2),
(5, 2),
(6, 2),
(7, 2),
(8, 2),
(9, 2),
(1, 3),
(2, 3),
(3, 3),
(4, 3),
(5, 3),
(6, 3),
(7, 3),
(8, 3),
(9, 3),
(1, 5),
(2, 5),
(3, 5),
(4, 5),
(5, 5),
(6, 5),
(7, 5),
(8, 5),
(9, 5),
(2, 6),
(3, 6),
(4, 6),
(5, 6),
(6, 6),
(7, 6),
(8, 6),
(9, 6),
(3, 4),
(6, 4),
(7, 4),
(2, 4),
(1, 8),
(2, 8),
(3, 8),
(4, 8),
(5, 8),
(6, 8),
(7, 8),
(8, 8),
(9, 8),
(1, 4),
(4, 4),
(5, 4),
(8, 4),
(9, 4),
(1, 9),
(2, 9),
(3, 9),
(4, 9),
(5, 9),
(6, 9),
(7, 9),
(8, 9),
(9, 9),
(2, 10),
(3, 10),
(4, 10),
(5, 10),
(6, 10),
(7, 10),
(8, 10),
(9, 10),
(1, 11),
(2, 11),
(3, 11),
(4, 11),
(5, 11),
(6, 11),
(7, 11),
(8, 11),
(9, 11),
(1, 10),
(1, 12),
(2, 12),
(3, 12),
(4, 12),
(5, 12),
(6, 12),
(7, 12),
(8, 12),
(9, 12),
(1, 13),
(2, 13),
(3, 13),
(4, 13),
(5, 13),
(6, 13),
(7, 13),
(8, 13),
(9, 13);

-- --------------------------------------------------------

--
-- Table structure for table `unit`
--

CREATE TABLE `unit` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `unit`
--

INSERT INTO `unit` (`id`, `name`, `status`, `created_at`, `updated_at`) VALUES
(1, 'MT', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(2, 'KG', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(3, 'FCL', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(4, 'Liter', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(5, 'Carton', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(6, 'Picul', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(7, 'Barrel', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14'),
(8, 'Gallon', 1, '2019-04-30 11:04:14', '2019-04-30 11:04:14');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int(10) UNSIGNED NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country_id` int(10) UNSIGNED DEFAULT NULL,
  `fullname` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mobile_no` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `profile_pic` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_social` tinyint(4) NOT NULL DEFAULT '0',
  `otp_no` int(11) DEFAULT NULL,
  `forgot_password_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_email_verified` tinyint(4) NOT NULL DEFAULT '0',
  `message_notifications` tinyint(4) NOT NULL DEFAULT '1',
  `favourite_option` tinyint(4) NOT NULL DEFAULT '1',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `uuid`, `country_id`, `fullname`, `email_id`, `password`, `country_code`, `mobile_no`, `company_name`, `profile_pic`, `is_social`, `otp_no`, `forgot_password_code`, `is_email_verified`, `message_notifications`, `favourite_option`, `status`, `created_at`, `updated_at`) VALUES
(1, '97afbb4c-8777-4c4e-9093-0e0a094f9bf2', NULL, 'Emma Smith', '<EMAIL>', '$2y$10$tlphXUsJvF9pvfoxJCqmxOa/.SVuw7l/0ckDrb8WZaViY/kfm7mgi', NULL, NULL, NULL, 'Image/IMG_5cc82e9716a0c5.50590966.png', 1, NULL, '5d13219768', 1, 1, 1, 1, '2019-04-30 11:16:39', '2019-06-26 02:11:11'),
(2, '6d7a51a6-611b-4331-bb25-97546c832de6', 1, 'Amol', '<EMAIL>', '$2y$10$5CBDL97pwQz5sfuZlHqMJ.R3kXrFInpuZuX/fM3P6iN095NkIXeZ.', NULL, '9090909099', 'tmt', NULL, 0, NULL, NULL, 1, 1, 1, 1, '2019-04-30 11:58:00', '2019-04-30 11:58:14'),
(3, '6b12c15f-4886-4b91-a2d7-60361efa5c42', NULL, 'Mridul Bhatia', '<EMAIL>', NULL, NULL, NULL, NULL, 'Image/IMG_5cc83e5d697666.63595840.png', 1, NULL, NULL, 1, 1, 1, 1, '2019-04-30 12:23:57', '2019-04-30 12:23:57'),
(4, '667c407a-3de2-4f2a-9cb6-205a5b94fc65', NULL, 'Mitul', '<EMAIL>', '$2y$10$XJZ7oXxb/5raj8Z7iAWl.OK6GlyNC0Tje/HisxaLthrwQgyyzv9.C', NULL, '9586088288', NULL, NULL, 0, NULL, NULL, 1, 1, 1, 1, '2019-04-30 13:13:16', '2019-05-01 15:38:28'),
(5, '1e1d69f7-f834-4a61-a5f6-3dc89e2f2360', 98, 'Mridul Bhatia', '<EMAIL>', '$2y$10$qGsz7h5EvYAIAKDHf9AgeO9dKzUF0Ns8Jd0cFEWYAj2MOLtTGd75G', NULL, NULL, NULL, NULL, 0, NULL, NULL, 1, 1, 1, 1, '2019-04-30 13:28:23', '2019-04-30 13:28:50'),
(6, 'c74a0cac-f32c-46c8-a31f-278016fa73f6', NULL, 'Test User', '<EMAIL>', '$2y$10$dX2a4yigM1C9Qmr6ZA2WheJKpBKBO7fYT5vQvQUzWA4/Ln0lBOAUq', NULL, NULL, 'WMT', NULL, 0, NULL, NULL, 1, 1, 1, 1, '2019-05-01 05:47:33', '2019-05-01 05:48:40'),
(8, '5c082424-038d-4daf-9e23-773bc8fcb953', NULL, 'MBLion Oleochemicals', '<EMAIL>', '$2y$10$E.0RsOi2nflezJNqSOktV.cfujGOF87dibPBY9z.03IVXzZuNKbB2', NULL, NULL, NULL, NULL, 0, NULL, NULL, 1, 1, 1, 1, '2019-05-01 15:01:32', '2019-05-01 15:01:52'),
(9, 'c301c4a4-871c-49e6-ac6f-991b0ce07d03', NULL, 'Sam Sol', '<EMAIL>', NULL, NULL, NULL, NULL, 'Image/IMG_5cca9d8db91985.64803720.png', 1, NULL, NULL, 1, 1, 1, 1, '2019-05-02 07:34:37', '2019-05-02 07:34:37'),
(10, '08ecbc17-1e02-4a45-9da5-21b23d78bc2d', NULL, 'Jason Bourn', '<EMAIL>', '$2y$10$HOgk8ss1feAyNAuqX6.hS.vWNlZCh/OipMU4GjJNmJbBS9GKji34K', NULL, '7226807778', NULL, 'Image/IMG_5cd140ec11ad36.93589189.png', 1, NULL, '5d1345b238', 1, 1, 1, 1, '2019-05-07 08:25:16', '2019-06-26 04:45:14'),
(11, '57e35559-203e-439f-b088-f96b533e8b78', 98, 'Hdhdhd', '<EMAIL>', '$2y$10$ygEO0aRqEi/wfAB.5ZeTROJ21HvEh6LUqaVLKo2H/KXHk3snJAOk6', NULL, '123456789', 'ndnd', NULL, 0, 7833, NULL, 0, 1, 1, 1, '2019-05-10 07:18:50', '2019-05-10 07:18:50'),
(12, 'eef2098c-1d5c-4940-b127-74051a6ecd8e', NULL, 'John Thomas', '<EMAIL>', NULL, NULL, NULL, NULL, 'Image/IMG_5d13431614a153.30842143.png', 1, NULL, '5d13472957', 1, 1, 1, 1, '2019-06-26 04:34:06', '2019-06-26 04:51:29'),
(13, 'd2c5ae10-24fc-4c20-91bf-881b4f3b4566', NULL, 'Alan Walker', '<EMAIL>', NULL, NULL, NULL, NULL, 'Image/IMG_5d1343c5dbaff9.44139944.png', 1, NULL, NULL, 1, 1, 1, 1, '2019-06-26 04:37:01', '2019-06-26 04:37:01');

-- --------------------------------------------------------

--
-- Table structure for table `user_access`
--

CREATE TABLE `user_access` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `fcm_token` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `os` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'android,ios',
  `ip` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_access`
--

INSERT INTO `user_access` (`id`, `user_id`, `fcm_token`, `device_id`, `os`, `ip`, `status`, `created_at`, `updated_at`) VALUES
(10, 2, 'clkHjK7Rp-8:APA91bGAJ0gxWd2XvxoZbsVa9-FZy8xoMPnr1jML-1KQA1c7Q8YqTA1D5DbqIj7RL3vTk8armPGR55rFauO1dfCzOcZDAUunMdJm09plaDOQOyccI2UYP133_UpHoGmA_xfi3UIffSUN', 'fd36c282163eb3ff', 'android', '**********', 1, '2019-04-30 11:58:27', '2019-04-30 11:58:27'),
(18, 4, 'fSEBuFnJQrM:APA91bEPO6C5pZOu4PKUW2AxjmY3nRDUOcYh5w8Jzq_JJ6DQjGh-JVbKh6dqJXuKLQAmuwqxSSzfVnKC8V-z5UxhNvyT4RuEwknJt4I_aWhtHn_LBUvcxY9x86PZW3Ul2aOOZnGkv8YY', 'EA0B1270-0C73-4E7E-B189-D04C970E3871', 'ios', '**********', 1, '2019-04-30 14:15:50', '2019-05-03 13:11:58'),
(19, 4, 'cv7tEi6TdrA:APA91bGaMX7G-o1hwCcJHY9F6vMaKoSK08WyZeYtPJrS8LlboXgGadgyRDBA_yleoznZwb9YeMWWaZODE5ctzO9bB6bv-2smN7qA_lMkPRTjDtd5CcZBYk6mAaLidNzLJvhCDRM8L2PY', '564e292c14db0e21', 'android', '**********', 1, '2019-04-30 14:39:05', '2019-04-30 14:39:05'),
(34, NULL, 'cyJAcKiXsFc:APA91bFtuKC4stQgg_nTxheOsf6FzeVFKD079ldv3E7WrxoTyasj0BjsZQotmNNLCXCFZCV-MWS99n1rvNfz1FQ2GtDUvvL6CUIts8lQNaRODWa_eHtZZJBPsyv-HC-SH2wtMQYvAWhB', 'bfee8058baeb8269', 'android', '************', 1, '2019-05-01 12:35:41', '2019-06-26 01:46:12'),
(77, 6, 'eaMlKBgZAo8:APA91bEaD3C86e5IlnKTF-UcXKQkdC13_iWG0ZDSNVYR-fcKlYnskqEY_jmwym9yZysVNzUDMvLJd4srv9kz_8Q14B_jtJWVt9ocJ2w0zBFyaCwl6fhXF8U7XRhoIO4NFuMmArAg0atB', '22456E36-3FD9-4F8B-BE86-295226902D08', 'ios', '**********', 1, '2019-05-15 13:07:55', '2019-05-15 13:07:55'),
(97, NULL, 'e7rNeu0qf0o:APA91bHWnWUCjjumrYOODEsUZUEPaF1veaeHEgwNw7QpuLivj1Noz-lGiBpAtFTOhI5P-9ZwR4A_0-51mbPsrpa18ZkPmGuWD69h07EKtBGUUhFfXK9KOwyfL4BZh-EvaQA_p1X3C76I', 'EB749E1E-6D28-4052-AE79-B21C52CA1D3A', 'ios', '************', 1, '2019-06-12 09:41:43', '2019-06-26 02:01:16'),
(98, 10, 'fRIR22nHXlw:APA91bGeu_S2UjDsy0xTIY4ICE7Bklz7eVF9oxTtICw7u1pSXmsmthraRzjph4Lcmgo1Xe9yS2WIWd0SmrPOZJ8fsCHdkAADm3FdVylBeZoalV58ux5XEFVqat69PDBKmRqmf7YYomlz', '4aa467e92483dc42', 'android', '************', 1, '2019-06-12 11:17:17', '2019-06-25 11:40:01'),
(100, NULL, 'fy1-0vlZn-M:APA91bEhfFEcFPmhQy6HnlymIzYHOapDWabdSPyyuq4k_U0JUsHYy2t85TpeHbZYq7vtX6F8vBnfmSq9lE5Wy09vuXrFu8j_cajY6RWjWCGLvIhT6RVZQW5RJ-i0nDqIvVHSjY05dd2E', 'c34ac271b7cddc6b', 'android', NULL, 1, '2019-06-12 12:08:47', '2019-06-12 12:08:47'),
(101, NULL, 'd8qSEVDWIr0:APA91bEZo-s88kt0hCTJAcEoHC7WOo1Mcrhs8iPntnZ6VElRpfzpGrfWPePBS3SGUYRCK7FRNSdiswubjMsGQ9eouFQ1Xd0cXJZKCQouaF2Kz5YTQuAPn68RMZRUHOHeAAvT_7JbBviy', '42f607c7b0da2662', 'android', '************', 1, '2019-06-25 09:24:00', '2019-06-25 08:43:52'),
(102, NULL, 'd25bTE4OzCo:APA91bEStcoVF_6m5XQsFJM8bGwzyUq-ihW3OZF8n-mgYZ2HKwYFRjy50uWmcYpZapAgtkeoJImUAA9NzaQjHM2WKt3IqjjmBx-lN9bvlUPmyaOEQaX1YNl-b-CEDBCyY4imaLedBUa0', 'bf7f22193e61818a', 'android', NULL, 1, '2019-06-26 01:05:55', '2019-06-26 04:20:37');

-- --------------------------------------------------------

--
-- Table structure for table `user_contact_us`
--

CREATE TABLE `user_contact_us` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_contact_us`
--

INSERT INTO `user_contact_us` (`id`, `name`, `email`, `message`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Test User Domain', '<EMAIL>', 'hello sir', 1, '2019-04-30 12:36:20', '2019-04-30 12:36:20'),
(2, 'amol', '<EMAIL>', 'message from amol', 1, '2019-04-30 14:21:31', '2019-04-30 14:21:31'),
(3, 'amolt', '<EMAIL>', 'message from amolt', 1, '2019-04-30 14:22:14', '2019-04-30 14:22:14'),
(4, 'Oleochemicals', '<EMAIL>', 'Hello Sir', 1, '2019-04-30 17:04:12', '2019-04-30 17:04:12');

-- --------------------------------------------------------

--
-- Table structure for table `user_feedbacks`
--

CREATE TABLE `user_feedbacks` (
  `id` int(10) UNSIGNED NOT NULL,
  `email_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_feedbacks`
--

INSERT INTO `user_feedbacks` (`id`, `email_id`, `message`, `status`, `created_at`, `updated_at`) VALUES
(1, '<EMAIL>', 'hello sir, how are you', 1, '2019-04-30 14:21:37', '2019-04-30 14:21:37'),
(2, '<EMAIL>', 'hello', 1, '2019-05-01 06:24:34', '2019-05-01 06:24:34'),
(3, '<EMAIL>', 'hello well', 1, '2019-05-01 15:38:52', '2019-05-01 15:38:52'),
(4, '<EMAIL>', 'mblion', 1, '2019-05-01 15:40:32', '2019-05-01 15:40:32'),
(5, '<EMAIL>', 'hello testing', 1, '2019-05-02 13:12:05', '2019-05-02 13:12:05'),
(6, '<EMAIL>', 'hi sir', 1, '2019-05-06 05:05:56', '2019-05-06 05:05:56');

-- --------------------------------------------------------

--
-- Table structure for table `user_notifications`
--

CREATE TABLE `user_notifications` (
  `user_id` int(10) UNSIGNED NOT NULL,
  `category_id` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_notifications`
--

INSERT INTO `user_notifications` (`user_id`, `category_id`) VALUES
(1, 2),
(1, 3),
(1, 4),
(1, 5),
(1, 6),
(1, 7),
(1, 8),
(1, 9),
(2, 1),
(2, 2),
(2, 3),
(2, 4),
(2, 5),
(2, 6),
(2, 7),
(2, 8),
(2, 9),
(3, 1),
(3, 2),
(3, 3),
(3, 4),
(3, 5),
(3, 6),
(3, 7),
(3, 8),
(3, 9),
(4, 3),
(4, 4),
(4, 5),
(4, 6),
(4, 7),
(4, 8),
(4, 9),
(5, 1),
(5, 2),
(5, 3),
(5, 4),
(5, 5),
(5, 6),
(5, 7),
(5, 8),
(5, 9),
(4, 1),
(4, 2),
(6, 1),
(6, 2),
(6, 3),
(6, 4),
(6, 5),
(6, 6),
(6, 7),
(6, 8),
(6, 9),
(8, 2),
(8, 3),
(8, 4),
(8, 5),
(8, 6),
(8, 7),
(8, 8),
(8, 9),
(8, 1),
(9, 1),
(9, 2),
(9, 3),
(9, 4),
(9, 5),
(9, 6),
(9, 7),
(9, 8),
(9, 9),
(10, 4),
(10, 5),
(10, 6),
(10, 7),
(10, 8),
(10, 9),
(11, 1),
(11, 2),
(11, 3),
(11, 4),
(11, 5),
(11, 6),
(11, 7),
(11, 8),
(11, 9),
(1, 1),
(10, 1),
(10, 2),
(10, 3),
(12, 1),
(12, 2),
(12, 3),
(12, 4),
(12, 5),
(12, 6),
(12, 7),
(12, 8),
(12, 9),
(13, 1),
(13, 2),
(13, 3),
(13, 4),
(13, 5),
(13, 6),
(13, 7),
(13, 8),
(13, 9);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admin_email_id_unique` (`email_id`);

--
-- Indexes for table `app_content`
--
ALTER TABLE `app_content`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category`
--
ALTER TABLE `category`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cif_quotation`
--
ALTER TABLE `cif_quotation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cif_quotation_user_id_foreign` (`user_id`),
  ADD KEY `cif_quotation_packing_id_foreign` (`packing_id`),
  ADD KEY `cif_quotation_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `country`
--
ALTER TABLE `country`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `currency`
--
ALTER TABLE `currency`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `delivery_term`
--
ALTER TABLE `delivery_term`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `force_update_models`
--
ALTER TABLE `force_update_models`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `message`
--
ALTER TABLE `message`
  ADD PRIMARY KEY (`id`),
  ADD KEY `message_category_id_foreign` (`category_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_user_id_foreign` (`user_id`),
  ADD KEY `notifications_product_id_foreign` (`product_id`);

--
-- Indexes for table `packing`
--
ALTER TABLE `packing`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product`
--
ALTER TABLE `product`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_category_id_foreign` (`category_id`),
  ADD KEY `product_packing_id_foreign` (`packing_id`),
  ADD KEY `product_unit_id_foreign` (`unit_id`),
  ADD KEY `product_delivery_term_id_foreign` (`delivery_term_id`),
  ADD KEY `product_currency_id_foreign` (`currency_id`);

--
-- Indexes for table `product_documents`
--
ALTER TABLE `product_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_documents_product_id_foreign` (`product_id`);

--
-- Indexes for table `product_favourite`
--
ALTER TABLE `product_favourite`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_favourite_product_id_foreign` (`product_id`),
  ADD KEY `product_favourite_user_id_foreign` (`user_id`);

--
-- Indexes for table `product_loading_port`
--
ALTER TABLE `product_loading_port`
  ADD KEY `product_loading_port_country_id_foreign` (`country_id`),
  ADD KEY `product_loading_port_product_id_foreign` (`product_id`),
  ADD KEY `product_loading_port_user_id_foreign` (`user_id`);

--
-- Indexes for table `product_price`
--
ALTER TABLE `product_price`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_price_product_id_foreign` (`product_id`);

--
-- Indexes for table `social_login`
--
ALTER TABLE `social_login`
  ADD PRIMARY KEY (`id`),
  ADD KEY `social_login_user_id_foreign` (`user_id`);

--
-- Indexes for table `social_user`
--
ALTER TABLE `social_user`
  ADD PRIMARY KEY (`id`),
  ADD KEY `social_user_user_id_foreign` (`user_id`);

--
-- Indexes for table `ticker`
--
ALTER TABLE `ticker`
  ADD KEY `ticker_category_id_foreign` (`category_id`),
  ADD KEY `ticker_user_id_foreign` (`user_id`);

--
-- Indexes for table `unit`
--
ALTER TABLE `unit`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_email_id_unique` (`email_id`),
  ADD KEY `user_country_id_foreign` (`country_id`);

--
-- Indexes for table `user_access`
--
ALTER TABLE `user_access`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_access_user_id_foreign` (`user_id`);

--
-- Indexes for table `user_contact_us`
--
ALTER TABLE `user_contact_us`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_feedbacks`
--
ALTER TABLE `user_feedbacks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD KEY `user_notifications_user_id_foreign` (`user_id`),
  ADD KEY `user_notifications_category_id_foreign` (`category_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `app_content`
--
ALTER TABLE `app_content`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `category`
--
ALTER TABLE `category`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `cif_quotation`
--
ALTER TABLE `cif_quotation`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `country`
--
ALTER TABLE `country`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=243;

--
-- AUTO_INCREMENT for table `currency`
--
ALTER TABLE `currency`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `delivery_term`
--
ALTER TABLE `delivery_term`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `force_update_models`
--
ALTER TABLE `force_update_models`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `message`
--
ALTER TABLE `message`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=190;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=520;

--
-- AUTO_INCREMENT for table `packing`
--
ALTER TABLE `packing`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `product`
--
ALTER TABLE `product`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `product_documents`
--
ALTER TABLE `product_documents`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_favourite`
--
ALTER TABLE `product_favourite`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- AUTO_INCREMENT for table `product_price`
--
ALTER TABLE `product_price`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1144;

--
-- AUTO_INCREMENT for table `social_login`
--
ALTER TABLE `social_login`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `social_user`
--
ALTER TABLE `social_user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `unit`
--
ALTER TABLE `unit`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `user_access`
--
ALTER TABLE `user_access`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=103;

--
-- AUTO_INCREMENT for table `user_contact_us`
--
ALTER TABLE `user_contact_us`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `user_feedbacks`
--
ALTER TABLE `user_feedbacks`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `cif_quotation`
--
ALTER TABLE `cif_quotation`
  ADD CONSTRAINT `cif_quotation_packing_id_foreign` FOREIGN KEY (`packing_id`) REFERENCES `packing` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `cif_quotation_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `unit` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `cif_quotation_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `message`
--
ALTER TABLE `message`
  ADD CONSTRAINT `message_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `product`
--
ALTER TABLE `product`
  ADD CONSTRAINT `product_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currency` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_delivery_term_id_foreign` FOREIGN KEY (`delivery_term_id`) REFERENCES `delivery_term` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_packing_id_foreign` FOREIGN KEY (`packing_id`) REFERENCES `packing` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `unit` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `product_documents`
--
ALTER TABLE `product_documents`
  ADD CONSTRAINT `product_documents_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `product_favourite`
--
ALTER TABLE `product_favourite`
  ADD CONSTRAINT `product_favourite_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_favourite_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `product_loading_port`
--
ALTER TABLE `product_loading_port`
  ADD CONSTRAINT `product_loading_port_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_loading_port_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `product_loading_port_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `product_price`
--
ALTER TABLE `product_price`
  ADD CONSTRAINT `product_price_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `social_login`
--
ALTER TABLE `social_login`
  ADD CONSTRAINT `social_login_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `social_user`
--
ALTER TABLE `social_user`
  ADD CONSTRAINT `social_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Constraints for table `ticker`
--
ALTER TABLE `ticker`
  ADD CONSTRAINT `ticker_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `ticker_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user`
--
ALTER TABLE `user`
  ADD CONSTRAINT `user_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_access`
--
ALTER TABLE `user_access`
  ADD CONSTRAINT `user_access_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD CONSTRAINT `user_notifications_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `user_notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
