<?php

namespace App\Http\Middleware;

use App\Models\UserModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\JsonResponse;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;

class VerifyJWTToken
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $token = JWTAuth::getToken();

        if (!$token) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('jwt.jwt_absent'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
        }
        try {
            $user = $this->auth($token);
            if (!$user) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('jwt.jwt_invalid'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_SERVICE_UNAVAILABLE);
            }
        } catch (TokenExpiredException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('jwt.jwt_expire'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_SERVICE_UNAVAILABLE);
        } catch (TokenInvalidException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('jwt.jwt_invalid'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_SERVICE_UNAVAILABLE);
        } catch (JWTException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('jwt.jwt_invalid'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_SERVICE_UNAVAILABLE);
        }
        $request->merge(['user' => $user]);
        return $next($request);
    }

    public function auth($token = false)
    {
        $id = JWTAuth::getPayload($token)->get('sub');
        $uuid = JWTAuth::getPayload($token)->get('uuid');
        $tokenIssuedTime = JWTAuth::getPayload($token)->get('iat');
        $user = UserModel::where([
            'uuid' => $uuid,
            'status' => AppConstant::STATUS_ACTIVE,
        ])->first();

        // Check Password change or not if change invalid token
        if ($user != "") {
            if ($user->last_change_password_time != NULL || $user->last_change_password_time != "") {
                $changePasswordTime = Carbon::parse($user->last_change_password_time);
                $changePasswordTime = strtotime($changePasswordTime);
                if ($changePasswordTime > $tokenIssuedTime) {
                    JWTAuth::invalidate($token);
                    return false;
                }
            }
        }

        if ($user) {
            return $user;
        }
        return false;
    }
}
