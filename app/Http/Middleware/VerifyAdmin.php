<?php

namespace App\Http\Middleware;

use App\Classes\AdminCheck;
use Closure;
use Illuminate\Support\Facades\Route;

class VerifyAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!AdminCheck::verify()) {
            abort(403, 'Access denied');
        }
        return $next($request);
    }
}
