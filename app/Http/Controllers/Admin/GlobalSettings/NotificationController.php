<?php

namespace App\Http\Controllers\Admin\GlobalSettings;

use App\Models\Notification;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class NotificationController extends Controller
{
    public function index(){
        $data['notifications'] = Notification::where('status', AppConstant::STATUS_ACTIVE)->whereNull('user_id')->get();
        return view('admin.globalSettings.notification.list')->withdata($data);
    }

    public function create(){
        return view('admin.globalSettings.notification.add');
    }

    public function store(Request $request){
        try{
            // NotificationMessage::dispatch($request->message)->onQueue(AppConstant::RECOVER_PASSWORD_QUEUE);
        }catch (QueryException $e){
            Session::flash('error', __('message.serverError'));
            return response()->redirectTo('admin/globalSettings/notification/create');
        }
        Session::flash('success', __('message.sendSuccess'));
        return response()->redirectTo('admin/globalSettings/notification');
    }
}
