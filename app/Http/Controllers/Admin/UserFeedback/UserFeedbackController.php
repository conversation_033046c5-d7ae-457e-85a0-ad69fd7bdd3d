<?php

namespace App\Http\Controllers\Admin\UserFeedback;

//use App\Http\Controllers\Website\UserContactUs;
use App\Models\UserFeedback;
use App\Models\UserContactUs;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;
use Yajra\DataTables\DataTables;

class UserFeedbackController extends Controller
{
    public function index(){
        $data['feedbacks'] = UserFeedback::orderby('created_at', 'desc')->get();
        return view('admin.feedback.index')->withdata($data);
    }

    public function feedbackDetail($id){
        try {
            $data = UserFeedback::where('id', $id)->first();
        } catch (QueryException $e) {
            Session::flash('error', __('auth.server_error'));
            return redirect()->back();
        }
        return $data;
    }

    public function userFeedbackAjax(Request $request)
    {
        $feedback = UserFeedback::query();

        if ($request['order'][0]['column'] == '0') {
            $feedback = $feedback->orderBy('created_at', 'desc');
        }

        return DataTables::of($feedback)
            ->editColumn('message', function (UserFeedback $feedback) {
                return "<a href='javascript:void(0)' class='openFeedback' data-value='".$feedback->id."'>".$feedback->message."</a>";
            })
            ->addColumn('created_at', function (UserFeedback $feedback) {
                //$data = Carbon::parse($users->created_at)->getTimestamp();
                return Carbon::parse($feedback->created_at)->format('d/m/Y h:i A');
            })
            ->escapeColumns([])->make(true);
    }

    public function user_contact_us(){

        $data['user_contact_us'] = UserContactUs::orderby('created_at', 'desc')->get();
        return view('admin.userContactUs.index')->withdata($data);
    }
    public function user_contact_us_ajax(Request $request)
    {
        $contact_us = UserContactUs::query();

        if ($request['order'][0]['column'] == '0') {
            $contact_us = $contact_us->orderBy('created_at', 'desc');
        }

        return DataTables::of($contact_us)
            ->editColumn('message', function (UserContactUs $contact_us) {
                return "<a href='javascript:void(0)' class='openFeedback' data-value='".$contact_us->id."'>".$contact_us->message."</a>";
            })
            ->addColumn('created_at', function (UserContactUs $contact_us) {
                return Carbon::parse($contact_us->created_at)->format('d/m/Y h:i A');
            })

            ->escapeColumns([])->make(true);
    }

    public function userContactUsDetail($id){
        try {
            $data = UserContactUs::where('id', $id)->first();
        } catch (QueryException $e) {
            Session::flash('error', __('auth.server_error'));
            return redirect()->back();
        }
        return $data;
    }
}
