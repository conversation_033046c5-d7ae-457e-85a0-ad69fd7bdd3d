<?php

namespace App\Http\Controllers\Admin\AppContent;

use App\Models\AppContentModel;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class AppContentController extends Controller
{
    public function privacy()
    {
        $data['privacy'] = AppContentModel::where('type', AppConstant::PRIVACY_POLICY)->first();
        return view('admin.appContent.privacy')->withdata($data);
    }

    public function privacyPolicy(){
        $data['privacy'] = AppContentModel::where('type', AppConstant::PRIVACY_POLICY)->first();
        return view('admin.appContent.newPrivacyPolicy')->withdata($data);
    }

    public function terms()
    {
        $data['terms'] = AppContentModel::where('type', AppConstant::TERMS_AND_CONDITION)->first();
        return view('admin.appContent.terms')->withdata($data);
    }

    public function termsConditions(){
        $data['terms'] = AppContentModel::where('type', AppConstant::TERMS_AND_CONDITION)->first();
        return view('admin.appContent.termsConditions')->withdata($data);
    }

    public function update(Request $request, $id)
    {
        try {
            $desc = $request->description;
            AppContentModel::where('id', $id)->update(array(
                'description' => $desc
            ));
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.appContentUpdateSuccess'));
        return redirect()->route('admin.dashboard');
    }


}
