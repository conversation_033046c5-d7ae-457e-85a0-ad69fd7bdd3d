<?php

namespace App\Http\Controllers\Admin\Notifications;

use App\Models\UserModel;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class NotificationController extends Controller
{
    public function notifications()
    {
//        $data['notifications'] = UserNotificationsModel::orderby('user_id')->get();
        $data['user'] = UserModel::with('userNotifications')->get();
        return view('admin.users.notifications')->withdata($data);
    }
}
