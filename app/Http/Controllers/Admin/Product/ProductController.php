<?php

namespace App\Http\Controllers\Admin\Product;

use App\Exports\ProductsExport;
use App\Exports\QuotationExport;
use App\Models\ProductModel;
use App\Models\QuotationModel;
use App\Models\UserModel;
use App\Traits\GetUuid;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\DataTables;

class ProductController extends Controller
{
    use GetUuid;

    public function cifQuotation()
    {
        $data['quotations'] = QuotationModel::orderby('created_at', 'desc')->get();
        return view('admin.quotation')->withdata($data);
    }

    /****For CIF Quotations*******/

    public function QuotationShow($id)
    {
        return QuotationModel::where(array(
            'id' => $id
        ))->first();
    }

    public function cifQuotationFetch(Request $request)
    {
        $quotation = QuotationModel::query();

        if ($request['order'][0]['column'] == '0') {
            $quotation = $quotation->orderBy('created_at', 'DESC');
        }
        $quotation = $quotation->with('userName', 'packing', 'unit');

        // Status Active/Inactive Filter Condition
        if (request('status') != "") {
            switch (request('status')) {
                case 1:
                    $quotation->where("status", AppConstant::STATUS_ACTIVE);
                    break;
                case 2:
                    $quotation->where("status", AppConstant::STATUS_INACTIVE);
                    break;
                default:
                    break;
            }
        }
        return DataTables::of($quotation)
            ->addColumn('user', function (QuotationModel $quotation) {
                $data = array(
                    'user' => ($quotation->userName->fullname) ?? "Not Available"
                );
                return $data;
            })
            ->editColumn('message', function (QuotationModel $quotation) {
                return "<a href='javascript:void(0)' class='openMessage' data-value='" . $quotation->id . "'>" . $quotation->message . "</a>";
            })
            ->addColumn('packing', function (QuotationModel $quotation) {
                $data = array(
                    'packing' => ($quotation->packing->packing_type) ?? "Not Available"
                );
                return $data;
            })
            ->addColumn('unit', function (QuotationModel $quotation) {
                $data = array(
                    'unit' => ($quotation->unit->name) ?? "Not Available"
                );
                return $data;
            })
            ->addColumn('mobile_no', function (QuotationModel $quotation) {
                $data = array(
                    'mobile_no' => ($quotation->userName->mobile_no) ?? "Not Available"
                );
                return $data;
            })
            ->addColumn('created_at', function (QuotationModel $quotation) {
                return Carbon::parse($quotation->created_at)->format('d/m/Y h:i A');
            })
            ->addColumn('status', function (QuotationModel $quotation) {
                $data = array(
                    'uuid' => $quotation->uuid,
                    'status' => $quotation->status
                );
                return $data;
            })
            ->editColumn('action', function (QuotationModel $quotation) {
                $data = array(
                    'uuid' => $quotation->uuid,
                    'status' => $quotation->status
                );
                return $data;
            })
            ->filter(function ($query) {
                if (isset(request()->search['value']) && request()->search['value'] != null) {
                    $searchValue = request()->search['value'];
                    $query->whereHas('userName', function ($query) use ($searchValue) {
                        $query->where('fullname', 'like', '%' . $searchValue . '%');
                        $query->orWhere('mobile_no', 'like', '%' . $searchValue . '%');
                    });
                    $query->orWhereHas('packing', function ($query) use ($searchValue) {
                        $query->where('packing_type', 'like', '%' . $searchValue . '%');
                    });
                    $query->orWhereHas('unit', function ($query) use ($searchValue) {
                        $query->where('name', 'like', '%' . $searchValue . '%');
                    });
                    $query->orWhere('message', 'like', '%' . $searchValue . '%');
                    $query->orWhere('email_id', 'like', '%' . $searchValue . '%');
                    $query->orWhere('product_name', 'like', '%' . $searchValue . '%');
                    $query->orWhere('country', 'like', '%' . $searchValue . '%');
                    $query->orWhere('destination_port', 'like', '%' . $searchValue . '%');
                    $query->orWhere('trade_term', 'like', '%' . $searchValue . '%');
                    $query->orWhere('quantity', 'like', '%' . $searchValue . '%');

                }
            })->escapeColumns([])
            ->make(true);
    }

    public function productData(Request $request)
    {
        try {
            $status = $request->status;
            $id = $request->id;

            if ($request->format_id == AppConstant::CSV) {
                return Excel::download(new ProductsExport($id, $status), 'products.csv');
            } else {
                return Excel::download(new ProductsExport($id, $status), 'products.xlsx');
            }

        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
    }

    public function QuotationactiveInactive($id)
    {
        try {
            $data = $this->QuotationShow($id);
            if ($data == "") {
                Session::flash('error', __('messages.RecordNotFound'));
                return redirect()->back();
            }
            if ($data->status == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'status' => AppConstant::STATUS_INACTIVE
                );
                $message = __('messages.QuotationInActiveSuccess');
            } else {
                $updateArray = array(
                    'status' => AppConstant::STATUS_ACTIVE
                );
                $message = __('messages.QuotationActiveSuccess');
            }
            QuotationModel::where('id', $id)->update($updateArray);
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __($message, ['name' => $data->name]));
        return redirect()->back();
    }

    public function editquotation($id)
    {
        $data['quotations'] = $this->QuotationShow($id);
        if ($data['quotations'] == "") {
            Session::flash('error', __('messages.RecordNotFound'));
            return redirect()->back();
        }
        return view('admin.quotationEdit')->withdata($data);
    }

    public function QuotationUpdate(Request $request)
    {
        try {
            $name = ucwords($request->ProductName);
            QuotationModel::where('id', $request->id)->update(array(
                'email_id' => $request->email,
                'product_name' => $request->ProductName,
                'destination_port' => $request->destinationPort,
                'country' => $request->country,
                'quantity' => $request->quantity,
                'message' => $request->message,
            ));
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.QuotationUpdateSuccess', ['name' => $name]));
        return redirect()->route('admin.categories');
    }

    /*product filter*/
    public function filter(Request $request)
    {
        $datas = $this->filterQuery($request);
        $result = "";
        $i = 1;
        foreach ($datas as $product) {
            if ($product->status == AppConstant::STATUS_ACTIVE) {
                $class = 'label label-success';
                $name = __('labels.active');
                $url = route('categoryProduct.inactive', ['id' => $product->id]);
                $icon = 'icon-cross2';
                $label = __('labels.inactive');
            } else {
                $class = 'label label-danger';
                $name = __('labels.inactive');
                $url = route('categoryProduct.active', ['id' => $product->id]);
                $icon = 'icon-checkmark3';
                $label = __('labels.active');
            }
            if ($product->marquee == AppConstant::STATUS_ACTIVE) {
                $classMarquee = 'label label-success';
                $marquee = __('labels.show');
                $urlMarquee = route('categoryProduct.inactiveMarquee', ['id' => $product->id]);
                $labelMarquee = __('labels.marqueeHide');
            } else {
                $classMarquee = 'label label-danger';
                $marquee = __('labels.hide');
                $urlMarquee = route('categoryProduct.activeMarquee', ['id' => $product->id]);
                $labelMarquee = __('labels.marqueeShow');
            }

            $result .= "<tr>
                    <td class='srNO'>$i</td>
                    <td>$product->name</td>
                    <td>{$product->packing->packing_type}</td>
                    <td>{$product->unit->name}</td>
                    <td>{$product->deliveryTerm->name}</td>
                    <td>{$product->loading_port}</td>
                    <td>" . ((!$product->productPrice->max_price) ? " {$product->currency->symbol}  {$product->productPrice->min_price}  " : "{$product->currency->symbol} {$product->productPrice->min_price} - {$product->productPrice->max_price}") . " </td>
                    <td>{$product->hs_code}</td>
                    <td>{$product->cas_number}</td>
                    <td><span class='" . $classMarquee . "'>" . $marquee . "</span></td>
                    <td><span class='" . $class . "'>" . $name . "</span></td>
                    <td class='text-center'><ul class='icon-list' style='padding-left:0; margin-bottom:0;'><li class='dropdown' style='list-style: none'><a href='#' class='dropdown-toggle' data-toggle='dropdown' style='color: #000;'><i class='icon-menu9'></i></a><ul class='dropdown-menu dropdown-menu-right' style='font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;'><li><a href='" . route('category.editProduct', ['id' => $product->id]) . "'><i class='icon-pencil6'></i>" . __('labels.edit') . "</a></li><li><a href='" . $url . "'><i class='" . $icon . "'></i>" . $label . "</a></li><li><a href='" . $urlMarquee . "'><i class='" . $icon . "'></i>" . $labelMarquee . "</a></li><li><a href='" . route('category.productPrice', ['id' => $product->id]) . "'><i class='icon-coin-dollar'></i>" . __('labels.addPrice') . "</a></li><li><a href='" . route('category.addProductDocuments', ['id' => $product->id, 'uuid' => $product->uuid]) . "'><i class='icon-folder-open'></i>" . __('labels.addDocuments') . "</a></li></ul></li></ul>
                    </td>
                    </tr>";
            $i++;
        }
        echo $result;
    }

    public function filterQuery($request)
    {
        $data = ProductModel::query();
        $data = $data->orderby('name', 'ASC');
        if ($request->status != "") {
            switch ($request->status) {
                case AppConstant::STATUS_INACTIVE:
                    $data = $data->where(['category_id' => $request->category_id]);
                    break;
                case AppConstant::ACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_ACTIVE, 'category_id' => $request->category_id]);
                    break;
                case AppConstant::INACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_INACTIVE, 'category_id' => $request->category_id]);
                    break;
                default:
                    break;
            }
        }
        return $data->get();
    }

    public function quotationDetail($id)
    {
        try {
            $data = QuotationModel::with('userName', 'packing', 'unit')->where('id', $id)->first();
        } catch (QueryException $e) {
            Session::flash('error', __('auth.server_error'));
            return redirect()->back();
        }
        return $data;
    }

    public function quotationExport(Request $request)
    {
        try {
            if ($request->format_id == AppConstant::CSV) {
                return Excel::download(new QuotationExport(), 'quotation.csv');
            } else {
                return Excel::download(new QuotationExport(), 'quotation.xlsx');
            }

        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
    }
}
