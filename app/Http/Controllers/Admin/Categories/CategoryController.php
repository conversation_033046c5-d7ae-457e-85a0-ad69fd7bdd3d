<?php

namespace App\Http\Controllers\Admin\Categories;

use App\Exports\ProductExport;
use App\Http\Requests\Admin\Categories\CategoryRequest;
use App\Http\Requests\Admin\Messages\AddMessageRequest;
use App\Http\Requests\Admin\Products\AddProductRequest;
use App\Http\Requests\Admin\Products\ProductDocRequest;
use App\Http\Requests\Admin\Products\ProductPriceRequest;
use App\Http\Requests\Admin\Products\ProductRequest;
use App\Http\Requests\Admin\Products\ProductUpdateRequest;
use App\Jobs\SendPriceUpdateNotificationJob;
use App\Models\CategoryModel;
use App\Models\CountryModel;
use App\Models\CurrencyModel;
use App\Models\DeliveryTermModel;
use App\Models\MessageModel;
use App\Models\Notification;
use App\Models\PackingModel;
use App\Models\ProductDocumentsModel;
use App\Models\ProductFavouriteModel;
use App\Models\ProductLoadingPortModel;
use App\Models\ProductModel;
use App\Models\ProductPriceModel;
use App\Models\UnitModel;
use App\Models\UserAccess;
use App\Models\UserModel;
use App\Models\UserNotificationsModel;
use App\Traits\FCMNotification;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use LaravelFCM\Facades\FCM;
use LaravelFCM\Message\OptionsBuilder;
use LaravelFCM\Message\PayloadDataBuilder;
use LaravelFCM\Message\PayloadNotificationBuilder;
use Maatwebsite\Excel\Facades\Excel;
use Ramsey\Uuid\Uuid;
use App\Services\SNSNotificationService;

class CategoryController extends Controller
{
    // use FCMNotification;
    protected $snsNotificationService;

    public function __construct(SNSNotificationService $snsNotificationService)
    {
        $this->snsNotificationService = $snsNotificationService;
    }

    public function index()
    {
        $data['categories'] = CategoryModel::orderby('id')->get();
        return view('admin.categories.categories')->withdata($data);
    }

    public function show($id)
    {
        return CategoryModel::where(array(
            'id' => $id
        ))->first();
    }

    public function exportProducts($id)
    {
        return Excel::download(new ProductExport($id), 'products.xlsx');
    }

    public function showProductId($id)
    {
        return ProductModel::with('category')->where(array(
            'id' => $id
        ))->first();
    }

    public function showProductPriceId($id)
    {
        return ProductPriceModel::where(array(
            'product_id' => $id,
            'status' => AppConstant::STATUS_ACTIVE
        ))->first();
    }

    public function checkDoc($id)
    {
        return ProductDocumentsModel::where((array(
            'product_id' => $id,
            'status' => AppConstant::STATUS_ACTIVE
        )))->first();
    }

    public function activeInactive($id)
    {
        try {
            $data = $this->show($id);
            if ($data == "") {
                Session::flash('error', __('messages.RecordNotFound'));
                return redirect()->back();
            }
            if ($data->status == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'status' => AppConstant::STATUS_INACTIVE
                );
                $message = __('messages.CategoryInActiveSuccess');
            } else {
                $updateArray = array(
                    'status' => AppConstant::STATUS_ACTIVE
                );
                $message = __('messages.CategoryActiveSuccess');
            }
            CategoryModel::where('id', $id)->update($updateArray);
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __($message, ['name' => $data->name]));
        return redirect()->back();
    }

    public function edit($id)
    {
        $data['categories'] = $this->show($id);
        if ($data['categories'] == "") {
            Session::flash('error', __('messages.RecordNotFound'));
            return redirect()->back();
        }
        return view('admin.categories.edit')->withdata($data);
    }

    public function editProduct($id)
    {

        $data['products'] = $this->showProductId($id);
        $data['productPrice'] = $this->showProductPriceId($id);
        $data['packings'] = PackingModel::get();
        $data['units'] = UnitModel::get();
        $data['terms'] = DeliveryTermModel::get();
        $data['currencies'] = CurrencyModel::get();
        if ($data['products'] == "") {
            Session::flash('error', __('messages.RecordNotFound'));
            return redirect()->back();
        }
        return view('admin.categories.editProduct')->withdata($data);
    }

    public function categoryName($id)
    {
        return CategoryModel::where(array(
            'id' => $id
        ))->first();
    }
//
//    public function productUpdate(ProductUpdateRequest $request, $id)
//    {
//        try {
//            DB::beginTransaction();
//
//            // Update product data if no price change
//            if (!$request->price) {
//                $array = array(
//                    'name' => $request->product_name,
//                    'hs_code' => $request->hs_code,
//                    'cas_number' => $request->cas_number,
//                    'loading_port' => $request->loading_port,
//                    'currency_id' => $request->currency,
//                    'delivery_term_id' => $request->delivery_term,
//                    'unit_id' => $request->unit_name,
//                    'packing_id' => $request->packing_type
//                );
//                ProductModel::where('id', $id)->update($array);
//                DB::commit();
//            } else {
//                // Check and handle product price update
//                $check = $this->showProductPriceId($id);
//                if ($check != "") {
//                    if ($check->status == AppConstant::STATUS_ACTIVE) {
//                        $array = array(
//                            'status' => AppConstant::STATUS_INACTIVE
//                        );
//                        ProductPriceModel::where('id', $check->id)->update($array);
//                    }
//                }
//
//                $product = new ProductPriceModel();
//                $product->product_id = $id;
//
//                if ($request->price == AppConstant::DOUBLE_PRICE) {
//                    $prevMin = $check->min_price;
//                    $prevMax = $check->max_price;
//
//                    $newMinConst = ($prevMin + $request->min_price) / 2;
//                    $newMaxConst = ($prevMax + $request->max_price) / 2;
//
//                    $newMin = $newMinConst - $prevMin;
//                    $newMax = $newMaxConst - $prevMax;
//
//                    $newPriceDiff = ($newMin) + ($newMax);
//
//                    $product->min_price = $request->min_price;
//                    $product->max_price = $request->max_price;
//                    $product->price_diff = $newPriceDiff;
//                } else {
//                    $product->min_price = $request->min_price_single;
//                    $prevMin = $check->min_price;
//                    $newMinConst = ($product->min_price - $prevMin);
//                    $product->min_price = $request->min_price_single;
//                    $product->price_diff = $newMinConst;
//                }
//
//                $product->status = AppConstant::STATUS_ACTIVE;
//                $product->save();
//
//                DB::commit();
//
//                // Prepare notification details
//                $notificationTitle = 'Product Price Updated!';
//                $notificationBody = $request->product_name;
//
//                $categoryId = $request->category_id;
//
//                $users = ProductFavouriteModel::where(['product_id' => $id, 'status' => AppConstant::STATUS_ACTIVE])
//                    ->with(['usersAccess'])
//                    ->whereHas('user_notification', function ($a) use ($categoryId) {
//                        $a->where('category_id', $categoryId);
//                    })
//                    ->whereHas('usersAccess', function ($q) {
//                        $q->whereNotNull('id');
//                    })
//                    ->get();
//
//
//
//            // Process each user and save notification
//            foreach ($users as $u) {
//                Log::info("Processing user: {$u->user_id}");
//
//                try {
//                    // Save notification to database
//                    $notification = new Notification();
//                    $notification->user_id = $u->user_id;
//                    $notification->product_id = $id;
//                    $notification->message = $notificationTitle;
//                    $notification->min_price = $minPriceSingle ?? $minPriceRange;
//                    $notification->max_price = $maxPriceRange ?? null;
//                    $notification->price_diff = $priceDiffRange ?? $priceDiffSingle;
//                    $notification->message_type = AppConstant::NOTIFICATION_ALL;
//
//                    if ($notification->save()) {
//                        Log::info("✅ Notification saved successfully for User ID: {$u->user_id}");
//                    } else {
//                        Log::error("❌ Failed to save notification for User ID: {$u->user_id}");
//                    }
//
//                } catch (\Exception $e) {
//                    Log::error("❌ Error saving notification for User ID: {$u->user_id} - " . $e->getMessage());
//                }
//            }
//            }
//
//        } catch (QueryException $e) {
//            DB::rollBack();
//            Session::flash('error', __('messages.serverError'));
//            return redirect()->back();
//        }
//
//        Session::flash('success', __('messages.ProductUpdateSuccess', ['name' => $request->product_name]));
//        return redirect()->route('category.product', ['id' => $request->category_id]);
//    }


    /**
     * Update product information and price
     *
     * @param ProductUpdateRequest $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function productUpdate(ProductUpdateRequest $request, $id)
    {
        try {
            DB::beginTransaction();

            if (!$request->has('price') ) {
                $this->updateProductBasicInfo($request, $id);
            } else {
                $this->updateProductWithPrice($request, $id);
            }

            // Log the product update activity
            $this->logProductUpdate($request->product_name, $id, $request->price ? 'price_update' : 'info_update');

            DB::commit();

            Session::flash('success', __('messages.ProductUpdateSuccess', ['name' => $request->product_name]));
            return redirect()->route('category.product', ['id' => $request->category_id]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Product update error: ' . $e->getMessage());
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
    }

    /**
     * Update basic product information without price changes
     *
     * @param ProductUpdateRequest $request
     * @param int $id
     * @return void
     */
    private function updateProductBasicInfo(ProductUpdateRequest $request, $id)
    {
        $productData = [
            'name' => $request->product_name,
            'hs_code' => $request->hs_code,
            'cas_number' => $request->cas_number,
            'loading_port' => $request->loading_port,
            'currency_id' => $request->currency,
            'delivery_term_id' => $request->delivery_term,
            'unit_id' => $request->unit_name,
            'packing_id' => $request->packing_type
        ];

        ProductModel::where('id', $id)->update($productData);
    }

    /**
     * Update product with price changes and send notifications
     *
     * @param ProductUpdateRequest $request
     * @param int $id
     * @return void
     */
    private function updateProductWithPrice(ProductUpdateRequest $request, $id)
    {
        $currentActivePrice = $this->showProductPriceId($id);

        // Deactivate current price if exists and active
        if ($currentActivePrice && $currentActivePrice->status == AppConstant::STATUS_ACTIVE) {
            ProductPriceModel::where('id', $currentActivePrice->id)
                ->update(['status' => AppConstant::STATUS_INACTIVE]);
        }

        // Create new price record
        $priceData = $this->preparePriceData($request, $currentActivePrice, $id);
        $newPrice = ProductPriceModel::create($priceData);

        // Send notifications to users who favorited this product
        $this->sendPriceUpdateNotifications($request, $id, $newPrice);
    }

    /**
     * Prepare price data based on price type (single or double)
     *
     * @param ProductUpdateRequest $request
     * @param ProductPriceModel|null $currentPrice
     * @param int $productId
     * @return array
     */
    private function preparePriceData(ProductUpdateRequest $request, $currentPrice, $productId)
    {
        $priceData = [
            'product_id' => $productId,
            'status' => AppConstant::STATUS_ACTIVE
        ];

        if ($request->price == AppConstant::DOUBLE_PRICE) {
            $prevMin = $currentPrice ? $currentPrice->min_price : 0;
            $prevMax = $currentPrice ? $currentPrice->max_price : 0;

            $newMinAvg = ($prevMin + $request->min_price) / 2;
            $newMaxAvg = ($prevMax + $request->max_price) / 2;

            $priceDiff = ($newMinAvg - $prevMin) + ($newMaxAvg - $prevMax);

            $priceData['min_price'] = $request->min_price;
            $priceData['max_price'] = $request->max_price;
            $priceData['price_diff'] = $priceDiff;
        } else {
            $prevMin = $currentPrice ? $currentPrice->min_price : 0;
            $newMinPrice = $request->min_price_single;

            $priceData['min_price'] = $newMinPrice;
            $priceData['price_diff'] = $newMinPrice - $prevMin;
        }

        return $priceData;
    }

    /**
     * Send notifications to users who favorited this product
     *
     * @param ProductUpdateRequest $request
     * @param int $productId
     * @param ProductPriceModel $newPrice
     * @return void
     */
    private function sendPriceUpdateNotifications(ProductUpdateRequest $request, $productId, $newPrice)
    {
        $notificationTitle = AppConstant::PRODUCT_PRICE_UPDATE_TITLE;
        $categoryId = $request->category_id;

        $users = ProductFavouriteModel::with('product')->where([
            'product_id' => $productId,
            'status' => AppConstant::STATUS_ACTIVE
        ])
            ->with(['usersAccess'])
            ->whereHas('user_notification', function ($query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->whereHas('usersAccess', function ($query) {
                $query->whereNotNull('id');
            })
            ->get();

        foreach ($users as $user) {
            Log::info("Processing user: {$user->user_id}");
            try {
                // Save notification to database with expiration date (7 days)
                $notification = new Notification();
                $notification->user_id = $user->user_id;
                $notification->product_id = $productId;
                $notification->message = $notificationTitle;
                $notification->min_price = $newPrice->min_price;
                $notification->max_price = $newPrice->max_price ?? null;
                $notification->price_diff = $newPrice->price_diff;
                $notification->message_type = AppConstant::NOTIFICATION_ALL;
//                $notification->expires_at = now()->addDays(7); // Add 7-day expiration

                if ($notification->save()) {
                    Log::info("✅ Notification saved successfully for User ID: {$user->user_id}");

                    // Send to notification channel
                    $this->sendToNotificationChannel($user->user_id, $productId, $user->product->name );
                } else {
                    Log::error("❌ Failed to save notification for User ID: {$user->user_id}");
                }
            } catch (\Exception $e) {
                Log::error("❌ Error saving notification for User ID: {$user->user_id} - " . $e->getMessage());
            }
        }
    }

    /**
     * Send notification to the notification channel
     *
     * @param int $userId
     * @param int $productId
     * @param string $message
     * @param int $notificationId
     * @return void
     */
    private function sendToNotificationChannel($userId, $productId, $product_name)
    {
        try {

            $this->snsNotificationService->sendPushNotification(
                $userId,
                "Product Price Update",
                $product_name . 'Price Updated',
                NULL,
                $productId
            );

            Log::info("📢 Notification sent to channel for User ID: {$userId}, Product ID: {$productId}");
        } catch (\Exception $e) {
            Log::error("❌ Error sending notification to channel: " . $e->getMessage());
        }
    }

    /**
     * Log product update activity
     *
     * @param string $productName
     * @param int $productId
     * @param string $updateType
     * @return void
     */
    private function logProductUpdate($productName, $productId, $updateType)
    {
        $logData = [
            'product_id' => $productId,
            'product_name' => $productName,
            'update_type' => $updateType,
            'updated_by' => auth()->id(),
            'updated_at' => now()->toDateTimeString()
        ];

        Log::channel('notification')->info('Product updated', $logData);

    }

    public function productXUpdate(ProductUpdateRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            //            if (!$request->price) {
            $array = array(
                'name' => $request->product_name,
                'hs_code' => $request->hs_code,
                'cas_number' => $request->cas_number,
                'loading_port' => $request->loading_port,
                'currency_id' => $request->currency,
                'delivery_term_id' => $request->delivery_term,
                'unit_id' => $request->unit_name,
                'packing_id' => $request->packing_type
            );
            ProductModel::where('id', $id)->update($array);
            //            } else {
            // check product old price
            $check = $this->showProductPriceId($id);
            if ($check != "") {
                if ($check->status == AppConstant::STATUS_ACTIVE) {
                    $array = array(
                        'status' => AppConstant::STATUS_INACTIVE
                    );
                    ProductPriceModel::where('id', $check->id)->update($array);
                }
                //                }

                if (isset($request->price)) {
                    $product = new ProductPriceModel();
                    $product->product_id = $id;
                    if ($request->price == AppConstant::DOUBLE_PRICE) {
                        $prevMin = $check->min_price;
                        $prevMax = $check->max_price;

                        $newMinConst = ($prevMin + $request->min_price) / 2;
                        $newMaxConst = ($prevMax + $request->max_price) / 2;

                        $newMin = $newMinConst - $prevMin;
                        $newMax = $newMaxConst - $prevMax;

                        $newPriceDiff = ($newMin) + ($newMax);

                        $product->min_price = $request->min_price;
                        $product->max_price = $request->max_price;
                        $product->price_diff = $newPriceDiff;
                    } else {
                        $product->min_price = $request->min_price_single;
                        $prevMin = $check->min_price;
                        $newMinConst = ($product->min_price - $prevMin);
                        $product->min_price = $request->min_price_single;
                        $product->price_diff = $newMinConst;
                    }
                    $product->status = AppConstant::STATUS_ACTIVE;
                    $product->save();

                }
                $optionBuilder = new OptionsBuilder();
                $optionBuilder->setTimeToLive(60 * 20)->setPriority('high');

                $notificationTitle = 'Product Price Updated!';
                $notificationBody = $request->product_name;

                $notificationBuilder = new PayloadNotificationBuilder();
                $notificationBuilder->setTitle($notificationTitle)
                    ->setBody($notificationBody)
                    ->setSound('default');

                $dataBuilder = new PayloadDataBuilder();
                $dataBuilder->addData([
                    'statusCode' => AppConstant::STATUS_ACTIVE,
                    'title' => $notificationTitle,
                    'body' => $notificationBody . 'Price Updated',
                    'product_id' => $id,
                    'click_action' => 'pushAction'
                ]);
                $option = $optionBuilder->build();
                $notify = $notificationBuilder->build();
                $data = $dataBuilder->build();
                $downstreamResponse = null;

                $users = ProductFavouriteModel::where('product_id', $id)
                    ->with(['usersAccess'])
                    ->whereHas('usersAccess', function ($q) {
                        $q->whereNotNull('id');
                    })
                    ->get();
                foreach ($users as $u) {
                    switch ($u->usersAccess->os) {
                        /*case AppConstant::OS_ANDROID:
                            $downstreamResponse = FCM::sendTo($u->usersAccess->fcm_token, $option, $notify, $data);
                            break;
                        case AppConstant::OS_IOS:
                            $downstreamResponse = FCM::sendTo($u->usersAccess->fcm_token, $option, $notify, $data);
                            break;*/
                    }
                    if ($downstreamResponse != null) {
                        $notification = new Notification();
                        $notification->user_id = $u->user_id;
                        $notification->product_id = $request->id;
                        $notification->message = $notificationTitle;
                        if ($request->price == AppConstant::DOUBLE_PRICE) {
                            $notification->min_price = $request->min_price;
                            $notification->max_price = $request->max_price;
                            $notification->price_diff = $newPriceDiff;
                        } else {
                            $notification->min_price = $request->min_price_single;
                            $notification->price_diff = $newMinConst;
                        }
                        $notification->message_type = AppConstant::NOTIFICATION_ALL;
                        $notification->save();
                    }
                }


                /*foreach ($categories as $category) {
                    foreach ($category->users as $user) {
                    //                        $case =
                        if ($case) {
                            switch ($user->os) {
                                case AppConstant::OS_ANDROID:
                                    $downstreamResponse = FCM::sendTo($user->fcm_token, $option, $notify, $data);
                                    break;
                                case AppConstant::OS_IOS:
                                    $downstreamResponse = FCM::sendTo($user->fcm_token, $option, $notify, $data);
                                    break;
                            }
                            if ($downstreamResponse != null) {
                                DB::beginTransaction();
                                $notification = new Notification();
                                $notification->user_id = $category->users[0]->user_id;
                                $notification->product_id = $request->id;
                                $notification->message = $notificationTitle;
                                if ($request->price == AppConstant::DOUBLE_PRICE) {
                                    $notification->min_price = $request->min_price;
                                    $notification->max_price = $request->max_price;
                                    $notification->price_diff = $newPriceDiff;
                                } else {
                                    $notification->min_price = $request->min_price_single;
                                    $notification->price_diff = $newMinConst;
                                }
                                $notification->message_type = AppConstant::NOTIFICATION_ALL;
                                $notification->save();
                                DB::commit();
                            }
                        }
                    }
                }*/

            }

            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.ProductUpdateSuccess', ['name' => $request->product_name]));
        return redirect()->route('category.product', ['id' => $request->category_id]);
    }


    public function checkCategoryName(Request $request)
    {
        if ($request->has('id')) {
            if (CategoryModel::where('name', $request->name)->where('id', '!=', $request->id)->exists()) {
                return "false";
            }
        } else if (CategoryModel::where('name', $request->name)->exists()) {
            return "false";
        }
        return "true";
    }

    public function update(CategoryRequest $request, $id)
    {
        try {
            $name = ucwords($request->category_name);
            if ($request->has('category_image')) {
                $img = $request->category_image;
                $image = file_get_contents($request->category_image);
                $Img = ('category/' . uniqid($request->category_name) . "." . $img->getClientOriginalExtension());

                Storage::disk('public')->put($Img, $image);

                CategoryModel::where('id', $id)->update(array(
                    'name' => $name,
                    'image_path' => $Img
                ));
            } else {
                CategoryModel::where('id', $id)->update(array(
                    'name' => $request->category_name,
                ));
            }
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.CategoryUpdateSuccess', ['name' => $name]));
        return redirect()->route('category.categories');
    }

    public function product($id)
    {
        try {
            $data['products'] = ProductModel::with('category')->where('category_id', $id)->orderBy('updated_at', 'desc')->get();
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        $data['category_id'] = $id;
        $data['category_name'] = $this->categoryName($id);
        $data['price'] = ProductPriceModel::where('status', AppConstant::STATUS_ACTIVE)->get();
        return view('admin.categories.products')->withdata($data);
    }

    public function productActiveInactive($id)
    {
        try {
            $data = $this->showProductId($id);
            if ($data == "") {
                Session::flash('error', __('messages.RecordNotFound'));
                return redirect()->back();
            }
            if ($data->status == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'status' => AppConstant::STATUS_INACTIVE
                );
                $message = __('messages.ProductInActiveSuccess');
            } else {
                $updateArray = array(
                    'status' => AppConstant::STATUS_ACTIVE
                );
                $message = __('messages.ProductActiveSuccess');
            }
            ProductModel::where('id', $id)->update($updateArray);
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __($message, ['name' => $data->name]));
        return redirect()->back();
    }

    public function categoryProduct($id)
    {
        $data['products'] = $this->showProductId($id);
        if ($data['products'] == "") {
            Session::flash('error', __('messages.RecordNotFound'));
            return redirect()->back();
        }
        return view('admin.categories.editProduct')->withdata($data);
    }

    public function productAdd($id)
    {
        $data['packings'] = PackingModel::get();
        $data['units'] = UnitModel::get();
        $data['terms'] = DeliveryTermModel::get();
        $data['currencies'] = CurrencyModel::get();
        $data['countries'] = CountryModel::all();
        $data['category_id'] = $id;

        return view('admin.categories.productAdd')->withdata($data);
    }

    /*for adding products into database from admin panel*/
    public function addProduct(AddProductRequest $request, $id)
    {
        try {
            $uuid4 = Uuid::uuid4();
            $uuid = $uuid4->toString();

            DB::beginTransaction();

            $product = new ProductModel();
            $product->uuid = $uuid;
            $product->category_id = $id;
            $product->loading_port = $request->loading_port;
            $product->packing_id = $request->packing_type;
            $product->unit_id = $request->unit_name;
            $product->delivery_term_id = $request->delivery_term;
            $product->currency_id = $request->currency;
            $product->name = $request->product_name;
            $product->hs_code = $request->hs_code;
            $product->cas_number = $request->cas_number;
            $product->save();

            /*$loadingPort = new ProductLoadingPortModel();
            $loadingPort->country_id = $request->country_id;
            $loadingPort->product_id = $product->id;*/

            $price = new ProductPriceModel();
            $price->product_id = $product->id;
            if (!$request->max_price) {
                $price->min_price = $request->min_price_single;
            } else {
                $price->min_price = $request->min_price;
                $price->max_price = $request->max_price;
            }
            $price->save();


            DB::commit();

        } catch (QueryException $e) {
            DB::rollBack();
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.ProductAddSuccess', ['name' => $request->product_name]));
        return redirect()->route('category.product', ['id' => $product->category_id]);
    }

    public function marqueeActiveInactive($id)
    {
        try {
            $data = $this->showProductId($id);
            if ($data == "") {
                Session::flash('error', __('messages.RecordNotFound'));
                return redirect()->back();
            }
            if ($data->marquee == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'marquee' => AppConstant::STATUS_INACTIVE
                );
                $message = __('messages.MarqueeInActiveSuccess');
            } else {
                $updateArray = array(
                    'marquee' => AppConstant::STATUS_ACTIVE
                );
                $message = __('messages.MarqueeActiveSuccess');
            }
            ProductModel::where('id', $id)->update($updateArray);
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __($message, ['name' => $data->name]));
        return redirect()->back();
    }

    public function productPrice($id)
    {

        $data['id'] = $id;
        return view('admin.categories.productPrice')->withdata($data);
    }

    public function addProductPrice(ProductPriceRequest $request, $id)
    {
        try {
            $data = $this->showProductPriceId($id);
            if ($data == "") {
                $product = new ProductPriceModel();
                $product->product_id = $id;
                $product->min_price = $request->min_price;
                $product->max_price = $request->max_price;
                $product->price_diff = 0;
                $product->status = AppConstant::STATUS_ACTIVE;
                $product->save();

                Session::flash('success', __('messages.ProductPriceAddSuccess'));
                return redirect()->route('category.categories');
            }
            if ($data->status == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'status' => AppConstant::STATUS_INACTIVE
                );
                ProductPriceModel::where('id', $data->id)->update($updateArray);
            }
            $prevMin = $data->min_price;
            $prevMax = $data->max_price;

            $newMinConst = ($prevMin + $request->min_price) / 2;
            $newMaxConst = ($prevMax + $request->max_price) / 2;

            $newMin = $newMinConst - $prevMin;
            $newMax = $newMaxConst - $prevMax;

            $newPriceDiff = ($newMin) + ($newMax);

            $product = new ProductPriceModel();
            $product->product_id = $id;
            $product->min_price = $request->min_price;
            $product->max_price = $request->max_price;
            $product->price_diff = $newPriceDiff;
            $product->status = AppConstant::STATUS_ACTIVE;
            $product->save();

        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.ProductPriceUpdateSuccess'));
        return redirect()->route('category.categories');
    }

    public function addProductDocuments(Request $request, $id, $category_id)
    {
        $data['id'] = $id;
        $data['uuid'] = $request->uuid;
        $data['category_id'] = $category_id;
        return view('admin.categories.addProductDocuments')->withdata($data);
    }

    public function addProductDoc(ProductDocRequest $request, $id)
    {
        try {
            $data = $this->checkDoc($id);
            $uuid = $request->uuid;
            if ($data == "") {
                $specs = file_get_contents($request->specifications);
                $Specifications = ('ProductDocs/' . $uuid . '/' . uniqid("ProdSpec_", 15) . '.pdf');

                Storage::disk('public')->put($Specifications, $specs);

                $docObj = new ProductDocumentsModel();
                $docObj->product_id = $id;
                $docObj->document_type = AppConstant::SPECIFICATIONS;
                $docObj->document_path = $Specifications;
                $docObj->status = AppConstant::STATUS_ACTIVE;
                $docObj->save();

                $tds = file_get_contents($request->tds);
                $TDS = ('ProductDocs/' . $uuid . '/' . uniqid("TDS_", 15) . '.pdf');

                Storage::disk('public')->put($TDS, $tds);

                $docObj = new ProductDocumentsModel();
                $docObj->product_id = $id;
                $docObj->document_type = AppConstant::TDS;
                $docObj->document_path = $TDS;
                $docObj->status = AppConstant::STATUS_ACTIVE;
                $docObj->save();

                $coa = file_get_contents($request->coa);
                $COA = ('ProductDocs/' . $uuid . '/' . uniqid("COA_", 15) . '.pdf');

                Storage::disk('public')->put($COA, $coa);

                $docObj = new ProductDocumentsModel();
                $docObj->product_id = $id;
                $docObj->document_type = AppConstant::COA;
                $docObj->document_path = $COA;
                $docObj->status = AppConstant::STATUS_ACTIVE;
                $docObj->save();

                Session::flash('success', __('messages.ProductDocAddSuccess', ['name' => $request->product_name]));
            }
            if ($data) {
                $specs = file_get_contents($request->specifications);
                $Specifications = ('ProductDocs/' . $uuid . '/' . uniqid("ProdSpec_", 15) . '.pdf');

                Storage::disk('public')->put($Specifications, $specs);

                $updateSpecArray = array(
                    'product_id' => $id,
                    'document_type' => AppConstant::SPECIFICATIONS,
                    'document_path' => $Specifications,
                    'status' => AppConstant::STATUS_ACTIVE
                );
                ProductDocumentsModel::where('id', $id)->update($updateSpecArray);
                $tds = file_get_contents($request->tds);
                $TDS = ('ProductDocs/' . $uuid . '/' . uniqid("TDS_", 15) . '.pdf');

                Storage::disk('public')->put($TDS, $tds);

                $updateTdsArray = array(
                    'product_id' => $id,
                    'document_type' => AppConstant::TDS,
                    'document_path' => $TDS,
                    'status' => AppConstant::STATUS_ACTIVE
                );
                ProductDocumentsModel::where('id', $id)->update($updateTdsArray);

                $coa = file_get_contents($request->coa);
                $COA = ('ProductDocs/' . $uuid . '/' . uniqid("COA_", 15) . '.pdf');

                Storage::disk('public')->put($COA, $coa);

                $updateCoaArray = array(
                    'product_id' => $id,
                    'document_type' => AppConstant::COA,
                    'document_path' => $COA,
                    'status' => AppConstant::STATUS_ACTIVE
                );
                ProductDocumentsModel::where('id', $id)->update($updateCoaArray);

            }


        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.ProductDocUpdateSuccess', ['name' => $request->product_name]));
        return redirect()->route('category.product', ['id' => $request->category_id]);
    }

    public function editPrice($id)
    {
        try {
            $data['products'] = ProductModel::with('packing')->where('category_id', $id)->get();
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        $data['category_id'] = $id;
        $data['price'] = ProductPriceModel::where('status', AppConstant::STATUS_ACTIVE)->get();
        return view('admin.categories.editPrice')->withdata($data);
    }

    public function multiplePrice(Request $request)
    {
        Log::info('Starting multiplePrice operation', [
            'category_id' => $request->category_id,
            'product_count' => count($request->get('product_id') ?? [])
        ]);

        $array = $request->get('product_id');
        $product_ids = array_map('intval', $array);
        $product_prices = $request->get('product_price');

        try {
            $changedProducts = [];

            foreach ($product_ids as $key => $id) {
                Log::debug("Processing product ID: {$id}", [
                    'index' => $key,
                    'price_input' => $request->product_price[$key] ?? 'N/A'
                ]);

                $str = $request->product_price[$key];
                $split = array_map('floatval', explode('-', $str));
                $minprice = $split[0];
                $max = AppConstant::INITIAL_PRICE_DIFF;
                $check = $this->showProductPriceId($id);
                $newPriceDiff = AppConstant::INITIAL_PRICE_DIFF;

                if (array_key_exists(1, $split)) {
                    $max = $split[1];

                    /*for price diff*/
                    $prevMin = $check->min_price;
                    $prevMax = $check->max_price;

                    $newMinConst = ($prevMin + $minprice) / 2;
                    $newMaxConst = ($prevMax + $max) / 2;

                    $newMin = $newMinConst - $prevMin;
                    $newMax = $newMaxConst - $prevMax;
                    $newPriceDiff = ($newMin) + ($newMax);

                    $prevRecord = $this->showProductPriceId($id);

                    if ($prevRecord->max_price != $max) {
                        if ($prevRecord->status == AppConstant::STATUS_ACTIVE) {
                            Log::info("Updating price range for product ID: {$id}", [
                                'prev_min' => $prevRecord->min_price,
                                'prev_max' => $prevRecord->max_price,
                                'new_min' => $minprice,
                                'new_max' => $max,
                                'price_diff' => $newPriceDiff
                            ]);

                            $updateArray = array(
                                'status' => AppConstant::STATUS_INACTIVE
                            );
                            ProductPriceModel::where('id', $prevRecord->id)->update($updateArray);

                            $product = new ProductPriceModel();
                            $product->product_id = $id;
                            $product->min_price = $minprice;
                            $product->max_price = $max;
                            $product->price_diff = $newPriceDiff;
                            $product->status = AppConstant::STATUS_ACTIVE;
                            $product->save();

                            array_push($changedProducts, $id);
                            Log::info("Price range updated successfully for product ID: {$id}");
                        }
                    } else {
                        Log::debug("No price change needed for product ID: {$id} - same max price");
                    }
                } else {
                    $min = $split[0];
                    $prevMin = $check->min_price;

                    if ($prevMin == $minprice) {
                        $newPriceDiff = $check->price_diff;
                    } else {
                        $newPriceDiff = ($min) - ($prevMin);
                    }

                    $prevRecord = $this->showProductPriceId($id);
                    if ($prevRecord->min_price != $min) {
                        if ($prevRecord->status == AppConstant::STATUS_ACTIVE) {
                            Log::info("Updating single price for product ID: {$id}", [
                                'prev_min' => $prevRecord->min_price,
                                'new_min' => $min,
                                'price_diff' => $newPriceDiff
                            ]);

                            $updateArray = array(
                                'status' => AppConstant::STATUS_INACTIVE
                            );
                            ProductPriceModel::where('id', $prevRecord->id)->update($updateArray);

                            $product = new ProductPriceModel();
                            $product->product_id = $id;
                            $product->min_price = $min;
                            $newMinConst = ($min - $prevRecord->min_price);
                            $product->price_diff = $newMinConst;
                            $product->status = AppConstant::STATUS_ACTIVE;
                            $product->save();

                            array_push($changedProducts, $id);
                            Log::info("Single price updated successfully for product ID: {$id}");
                        }
                    } else {
                        Log::debug("No price change needed for product ID: {$id} - same min price");
                    }
                }
            }

            if (!empty($changedProducts)) {
                Log::info("Sending push notifications for updated products", [
                    'changed_products_count' => count($changedProducts),
                    'product_ids' => $changedProducts
                ]);

                foreach ($changedProducts as $changeProduct) {
                    try {
                        $this->sendMultiplePush($changeProduct);
                        Log::debug("Push notification sent for product ID: {$changeProduct}");
                    } catch (\Exception $e) {
                        Log::error("Failed to send push notification for product ID: {$changeProduct}", [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            } else {
                Log::info("No products were changed during this operation");
            }

        } catch (QueryException $e) {
            Log::error("Database error in multiplePrice operation", [
                'error' => $e->getMessage(),
                'sql' => $e->getSql() ?? 'N/A',
                'bindings' => $e->getBindings() ?? [],
                'code' => $e->getCode()
            ]);

            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        } catch (\Exception $e) {
            Log::error("Unexpected error in multiplePrice operation", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'code' => $e->getCode()
            ]);

            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }

        Log::info("multiplePrice operation completed successfully", [
            'changed_products_count' => count($changedProducts),
            'category_id' => $request->category_id
        ]);

        Session::flash('success', __('messages.ProductPriceUpdateSuccess'));
        return redirect()->route('category.product', ['id' => $request->category_id]);
    }

    public function sendMultiplePush($id)
    {
        $price = ProductPriceModel::with('productName')->where(['product_id' => $id, 'status' => AppConstant::STATUS_ACTIVE])->get();

        foreach ($price as $p) {
            $productName = $p->productName->name;
            $catId = $p->productName->category_id;

            $priceData = [];
            if (!$p->max_price) {
                $priceData['min_price'] = $p->min_price;
                $priceData['max_price'] = null;
                $priceData['price_diff'] = $p->price_diff;
            } else {
                $priceData['min_price'] = $p->min_price;
                $priceData['max_price'] = $p->max_price;
                $priceData['price_diff'] = $p->price_diff;
            }

            $notificationTitle = 'Product Price Updated!';
            $notificationBody = $productName;

            // Get users who should receive the notification
            $users = ProductFavouriteModel::where(['product_id' => $id, 'status' => AppConstant::STATUS_ACTIVE])
                ->with(['usersAccess'])
                ->whereHas('user_notification', function ($a) use ($catId) {
                    $a->where('category_id', $catId);
                })
                ->whereHas('usersAccess', function ($q) {
                    $q->whereNotNull('id');
                })
                ->get();

            if ($users->isEmpty()) {
                Log::info("No users found for price update notification - Product ID: {$id}, Product: {$productName}");
                continue;
            }

            $totalUsers = $users->count();
            Log::info("📋 Preparing to send price update notifications to {$totalUsers} users for Product: {$productName}");

            // Convert users to array for job serialization
            $usersArray = $users->map(function ($user) {
                return [
                    'user_id' => $user->user_id,
                ];
            })->toArray();

            // Chunk users and dispatch jobs
            $chunkSize = 25; // Adjust based on your server capacity
            $chunks = array_chunk($usersArray, $chunkSize);

            foreach ($chunks as $index => $chunk) {
                SendPriceUpdateNotificationJob::dispatch(
                    $chunk,
                    $id,
                    $productName,
                    $notificationTitle,
                    $notificationBody,
                    $priceData
                )->onQueue('sendPriceUpdateAlerts'); // 3-second delay between chunks
            }

            Log::info("🎯 Successfully dispatched price update notification jobs for {$totalUsers} users - Product: {$productName}");
        }
    }

}
