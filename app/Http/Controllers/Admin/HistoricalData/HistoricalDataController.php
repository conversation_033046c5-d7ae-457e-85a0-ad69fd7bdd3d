<?php

namespace App\Http\Controllers\Admin\HistoricalData;

use App\Models\ProductModel;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HistoricalDataController extends Controller
{
    public function index(){
        try{
            $data['products'] = ProductModel::orderby('category_id')->get();
            return view('admin.historicalData.index')->withdata($data);
        }catch (QueryException $exception){

        }
    }
}
