<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Requests\Admin\Auth\AdminPasswordChangeRequest;
use App\Models\AdminModel;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class AuthController extends Controller
{
    use AuthenticatesUsers;

    public function __construct()
    {
        $this->middleware('auth.admin', ['except' => 'logout']);
    }

    protected $redirectTo = '/admin/dashboard';

    public function showLoginForm()
    {
        return view('admin.login');
    }

    public function username()
    {
        return 'email_id';
    }

    public function show($id)
    {
        return AdminModel::where(array(
            'uuid' => $id
        ))->first();
    }

    public function logout(Request $request)
    {
        $this->guard()->logout();
        $request->session()->forget(AppConstant::ADMIN_GUARD);
        $request->session()->regenerate();
        return redirect('/admin');
    }

    protected function guard()
    {
        return Auth::guard(AppConstant::ADMIN_GUARD);
    }
}
