<?php

namespace App\Http\Controllers\Admin\Messages;

use App\Http\Requests\Admin\Messages\AddMessageRequest;
use App\Http\Requests\Api\Category\sendPushNotificationRequest;
use App\Jobs\NotificationMessage;
use App\Jobs\ProcessEndpointAndNotifyJob;
use App\Jobs\SendBulkPushNotificationJob;
use App\Jobs\SendMessageNotification;
use App\Jobs\SendMessageTopicNotification;
use App\Jobs\UpdateSnsEndpointsBatchJob;
use App\Models\CategoryModel;
use App\Models\MessageModel;
use App\Models\Notification;
use App\Models\UserAccess;
use App\Models\UserModel;
use App\Models\UserNotificationsModel;
use App\Traits\FCMNotification;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Yajra\DataTables\DataTables;
use function GuzzleHttp\Promise\all;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use LaravelFCM\Facades\FCM;
use LaravelFCM\Message\OptionsBuilder;
use LaravelFCM\Message\PayloadDataBuilder;
use LaravelFCM\Message\PayloadNotificationBuilder;
use LaravelFCM\Message\Topics;
use App\Services\SNSNotificationService;
use Illuminate\Support\Facades\Log;

class MessageController extends Controller {

    // use FCMNotification;
    protected $snsNotificationService;

    public function __construct(SNSNotificationService $snsNotificationService)
    {
        $this->snsNotificationService = $snsNotificationService;
    }

    public function messages() {
        $data['messages'] = MessageModel::with('category')->orderby('created_at', 'desc')->get();
        return view('admin.users.messages')->withdata($data);
    }

    public function show($id) {
        return MessageModel::where(array(
                    'id' => $id
                ))->first();
    }

    public function activeInactive($id) {
        try {
            $data = $this->show($id);
            if ($data == "") {
                Session::flash('error', __('messages.RecordNotFound'));
                return redirect()->back();
            }
            if ($data->status == AppConstant::STATUS_ACTIVE) {
                $updateArray = array(
                    'status' => AppConstant::STATUS_INACTIVE
                );
                $message = __('messages.MessageInActiveSuccess');
            } else {
                $updateArray = array(
                    'status' => AppConstant::STATUS_ACTIVE
                );
                $message = __('messages.MessageActiveSuccess');
            }
            MessageModel::where('id', $id)->update($updateArray);
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __($message, ['name' => $data->name]));
        return redirect()->back();
    }

    public function edit($id) {
        $data['messages'] = $this->show($id);
        if ($data['messages'] == "") {
            Session::flash('error', __('messages.RecordNotFound'));
            return redirect()->back();
        }
        return view('admin.users.editmessage')->withdata($data);
    }

    /* public function checkCategoryName(Request $request)
      {
      if ($request->has('id')) {
      if (CategoryModel::where('name', $request->name)->where('id', '!=', $request->id)->exists()) {
      return "false";
      }
      } else if (CategoryModel::where('name', $request->name)->exists()) {
      return "false";
      }
      return "true";
      } */

    public function update($request, $id) {
        try {
            $title = $request->title;
            MessageModel::where('id', $id)->update(array(
                'title' => $request->title,
                'body' => $request->body
            ));
        } catch (QueryException $e) {
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
        Session::flash('success', __('messages.EquipmentUpdateSuccess', ['name' => $title]));
        return redirect()->route('admin.categories');
    }

    /* public function notifications()
      {
      //        $data['notifications'] = UserNotificationsModel::orderby('user_id')->get();
      $data['user'] = UserModel::with('userNotifications')->get();
      return view('admin.users.notifications')->withdata($data);
      } */


    /* user filter */

    public function filter(Request $request) {
        $datas = $this->filterQuery($request);
        $result = "";
        $i = 1;
        foreach ($datas as $message) {
            if ($message->status == AppConstant::STATUS_ACTIVE) {
                $class = 'label label-success';
                $name = __('labels.active');
                $icon = 'icon-cross2';
                $label = __('labels.inactive');
                $url = route('message.inactive', ['id' => $message->id]);
            } else {
                $class = 'label label-danger';
                $name = __('labels.inactive');
                $icon = 'icon-checkmark3';
                $label = __('labels.active');
                $url = route('message.active', ['id' => $message->id]);
            }

            $result .= "<tr>
                    <td class='srNO'>$i</td>
                    <td>" . ((!$message->category) ? " General Message " : " {$message->category->name}") . " </td>
                    <td><a href='javascript:void(0)' class='openMessage' data-value='{$message->id}'>$message->title</a></td>
                    <td>$message->subtitle</td>
                    <td class='timezone' data-time='" . $message->created_at . "'>" . $message->created_at . "</td>
                    <td><span class='" . $class . "'>" . $name . "</span></td>
                    <td class='text-center'><ul class='icon-list' style='padding-left:0; margin-bottom:0;'><li class='dropdown' style='list-style: none'><a href='#' class='dropdown-toggle' data-toggle='dropdown' style='color: #000;'><i class='icon-menu9'></i></a><ul class='dropdown-menu dropdown-menu-right' style='font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;'><li><a href='" . $url . "'><i class='" . $icon . "'></i>" . $label . "</a></li></ul></li></ul></td>
                    </tr>";

            $i++;
        }
        /* to show message category on filter. */
        /* <td>" . ($message->category !== "") ? $message->category : 'General Message' . "</td> */
        echo $result;
    }

    public function filterQuery($request) {
        $data = MessageModel::query();
        $data = $data->orderby('created_at', 'DESC');
        if ($request->status != "") {
            switch ($request->status) {
                case AppConstant::ACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_ACTIVE]);
                    break;
                case AppConstant::INACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_INACTIVE]);
                    break;
                default:
                    break;
            }
        }
        return $data->get();
    }

    public function fetchMessage(Request $request) {
        $message = MessageModel::query();
        if ($request['order'][0]['column'] == '0') {
            $message = $message->orderBy('created_at', 'DESC');
        }
        $message = $message->with('category');


        // Status Active/Inactive Filter Condition
        if (request('status') != "") {
            switch (request('status')) {
                case 1:
                    $message->where("status", AppConstant::STATUS_ACTIVE);
                    break;
                case 2:
                    $message->where("status", AppConstant::STATUS_INACTIVE);
                    break;
                default:
                    break;
            }
        }
        return DataTables::of($message)
                        ->editColumn('title', function (MessageModel $message) {
                            return "<a href='javascript:void(0)' class='openMessage' data-value='" . $message->id . "'>" . $message->title . "</a>";
                        })
                        ->addColumn('category', function (MessageModel $message) {

                            $data = array(
                                'category' => ($message->category) ? $message->category->name : "General Message");
                            return $data;
                        })
                        ->addColumn('created_at', function (MessageModel $message) {
                            $data = array(
                                'created_at' => date('d/m/Y H:m A', $message->created_at)
                            );
                            return $data;
                        })
                        ->addColumn('status', function (MessageModel $message) {
                            $data = array(
                                'uuid' => $message->id,
                                'status' => $message->status
                            );
                            return $data;
                        })
                        ->editColumn('action', function (MessageModel $message) {
                            $data = array(
                                'uuid' => $message->id,
                                'status' => $message->status
                            );
                            return $data;
                        })
                        ->filter(function ($query) {
                            if (isset(request()->search['value']) && request()->search['value'] != null) {
                                $searchValue = request()->search['value'];
                                $query->whereHas('category', function ($query) use ($searchValue) {
                                    $query->where('name', 'like', '%' . $searchValue . '%');
                                });
                                $query->orWhere('title', 'like', '%' . $searchValue . '%');
                                $query->orWhere('subtitle', 'like', '%' . $searchValue . '%');
                            }
                        })
                        ->orderColumn('status', 'status $1')
                        ->escapeColumns([])->make(true);
    }

    public function pushNotifications() {
        $data['user'] = UserModel::with('userNotifications')->get();
        $data['category'] = CategoryModel::all();
        return view('admin.users.pushNotifications')->withdata($data);
    }

    public function sendPushNotification(sendPushNotificationRequest $request) {
        $optionBuilder = new OptionsBuilder();
        $optionBuilder->setTimeToLive(60 * 20)->setPriority('high');

        $notificationBuilder = new PayloadNotificationBuilder();
        $notificationBuilder->setTitle($request['notification_text'])
                ->setBody($request['notification_text'])
                ->setSound(__('auth.signup_sound'));

        $dataBuilder = new PayloadDataBuilder();
        $dataBuilder->addData([
            'statusCode' => AppConstant::STATUS_ACTIVE,
            'messageText' => $request['notification_text'],
            'click_action' => 'pushAction'
        ]);
        $option = $optionBuilder->build();
        $notification = $notificationBuilder->build();
        $data = $dataBuilder->build();
        $downstreamResponse = null;

        $categories = UserNotificationsModel::where('category_id', $request->category)->get();

        foreach ($categories as $category) {
            foreach ($category->users as $user) {
                switch ($user->os) {
                    case AppConstant::OS_ANDROID:
                        $downstreamResponse = FCM::sendTo($user->fcm_token, $option, $notification, $data);
                        break;
                    case AppConstant::OS_IOS:
                        $downstreamResponse = FCM::sendTo($user->fcm_token, $option, $notification, $data);
                        break;
                    /* default:
                      $downstreamResponse = FCM::sendTo($user->fcm_token, $option, $notification, $data);
                      break; */
                }

                DB::beginTransaction();
                $notification = new Notification();
                $notification->user_id = $category->users[0]->user_id;
                $notification->message = $request->notification_text;
                $notification->message_type = AppConstant::NOTIFICATION_ALL;
                $notification->save();
                DB::commit();

                return $downstreamResponse->numberSuccess();
            }
        }
    }
    public function messageDetail($id) 
    {
        try {
            $data = MessageModel::where('id', $id)->first();
        } catch (QueryException $e) {
            Session::flash('error', __('auth.server_error'));
            return redirect()->back();
        }
        return $data;
    }

    public function messageAdd() {
        $data['category'] = CategoryModel::get();
        return view('admin.categories.messageAdd')->withdata($data);
    }

//  public function addMessage(AddMessageRequest $request) {
//         try {
// //            print_r($_REQUEST);exit;
//             $message = new MessageModel();
//             $message->title = $request->title;
//             $message->subtitle = $request->subtitle;
//             $message->category_id = $request->category_id;
//             $message->body = $request->body;
//             $message->status = AppConstant::STATUS_ACTIVE;
//             $message->save();
//             $messageId = $message->id;

// //            $optionBuilder = new OptionsBuilder();
// //            $optionBuilder->setTimeToLive(60 * 20)->setPriority('high');

//             /* remove html tags from push notifications */
//             $topicName = '';
//             if(isset($request->general_category) && $request->general_category == 'on'){
//                 $topicName = 'General';
//             } else if(!empty ($request->category_id)) {
//                 $cat = CategoryModel::find($request->category_id);
//                 if(isset($cat->name)){
//                     $topicName = str_replace(' ', '', $cat->name);
//                 }
//             }
//             $notificationTitle = strip_tags($request->title);
//             $notificationBody = strip_tags($request->body);


// //            $notificationBuilder = new PayloadNotificationBuilder();
// //            $notificationBuilder->setTitle($notificationTitle)
// //                    ->setBody($notificationBody)
// //                    ->setSound('default');

//             $dataBuilder = new PayloadDataBuilder();
//             $dataBuilder->addData([
//                 'statusCode' => AppConstant::STATUS_ACTIVE,
//                 'title' => $notificationTitle,
//                 'body' => $notificationBody,
//                 'message_id' => $messageId,
//                 'push_notification_type' => AppConstant::MESSAGE_PUSH,
//                 'category_id' => $request->category_id,
//                 'click_action' => 'pushAction'
//             ]);

// //            $option = $optionBuilder->build();
// //            $notify = $notificationBuilder->build();
//             $data = $dataBuilder->build();

// //            $downstreamResponse = null;
            
            
//             $notificationBuilder = new PayloadNotificationBuilder($notificationTitle);
//             $notificationBuilder->setBody($notificationBody)
//                     ->setSound('default');

//             $notification = $notificationBuilder->build();

//             $topic = new Topics();
//             $topic->topic($topicName);
//             SendMessageTopicNotification::dispatch($topic, $notification, $data)->onQueue(AppConstant::APP_QUEUE)->delay(now()->addMinutes(2));;
// //            $topicResponse = FCM::sendToTopic($topic, null, $notification, $data);

// //            $topicResponse->isSuccess();
// //            $topicResponse->shouldRetry();
// //            $topicResponse->error();
// //            $users = UserModel::where(['message_notifications' => AppConstant::STATUS_ACTIVE, 'status' => AppConstant::STATUS_ACTIVE])
// //                ->with(['usersAccess'])
// //                ->whereHas('usersAccess', function ($q) {
// //                    $q->whereNotNull('id');
// //                })
// //                ->get();
// //            print_r($users);
// //            exit;
// //            $user_collection = new Collection($users);
// //            $chunk_users = $user_collection->chunk(250);
// //            foreach ($chunk_users AS $key => $user) {
// //                SendMessageNotification::dispatch($user, $option, $notify, $data)->onQueue(AppConstant::APP_QUEUE);
// //            }
// //            foreach ($users as $u) {
// //                $tokens = UserAccess::where('user_id', $u->usersAccess->user_id)->pluck('fcm_token')->toArray();
// //                // issue for message sent
// //                FCM::sendTo($tokens, $option, $notify, $data);
// //            }
// //            $tokensUserNull = UserAccess::orWhereNull('user_id')->pluck('fcm_token')->toArray();
// //            FCM::sendTo($tokensUserNull, $option, $notify, $data);
//         } catch (QueryException $e) {
//             Session::flash('error', __('messages.serverError'));
//             return redirect()->back();
//         }
//         Session::flash('success', __('messages.messageAddSuccess'));
//         return redirect()->route('message.messages');
//     }
    // public function addMessage(AddMessageRequest $request)
    // {
    //     try {
    //         Log::info("📢 Adding New Message: {$request->title}");

    //         $message = new MessageModel();
    //         $message->title = $request->title;
    //         $message->subtitle = $request->subtitle;
    //         $message->category_id = $request->category_id;
    //         $message->body = $request->body;
    //         $message->status = AppConstant::STATUS_ACTIVE;
    //         $message->save();
    //         $messageId = $message->id;

    //         $notificationTitle = strip_tags($request->title);
    //         $notificationBody = strip_tags($request->body);

    //         // Fetch users with valid SNS endpoints
    //         $users = DB::table('user_access')
    //             ->whereNotNull('sns_endpoint_arn')
    //             ->pluck('user_id', 'sns_endpoint_arn');
    //         if ($users->isEmpty()) {
    //             Log::warning("⚠️ No users with valid SNS endpoints found.");
    //             return response()->json(['message' => 'No users found with valid SNS endpoints'], 400);
    //         }

    //         // Send notification to all users
    //         // foreach ($users as $snsEndpointArn => $userId) {
    //         //     Log::info("🚀 Sending AWS SNS notification to User ID: $userId", ['ARN' => $snsEndpointArn]);

    //         //     $result = $this->snsNotificationService->sendPushNotification($userId, $notificationTitle, $notificationBody);
    //         //     if ($result) {
    //         //         Log::info("✅ SNS Notification sent successfully for User ID: $userId");
    //         //         // Save the notification in the database
    //         //         Notification::create([
    //         //             'user_id' => $userId,  // Ensure this value exists
    //         //             'message' => $message,
    //         //             'min_price' => $minPrice ?? 0,
    //         //             'max_price' => $maxPrice ?? null,
    //         //             'price_diff' => $priceDiff ?? 0,
    //         //             'message_type' => AppConstant::NOTIFICATION_ALL,
    //         //             'is_read' => 0,
    //         //             'created_at' => now(),
    //         //             'updated_at' => now(),
    //         //         ]);

    //         //     } else {
    //         //         Log::error("❌ SNS Notification failed for User ID: $userId");
    //         //     }
    //         // }
    //         foreach ($users as $snsEndpointArn => $userId) {
    //             try {
    //                 // Verify user exists
    //                 $user = DB::table('user')->find($userId);
    //                 if (!$user) {
    //                     Log::warning("⚠️ User not found for ID: $userId. Skipping notification.");
    //                     continue;
    //                 }

    //                 Log::info("🚀 Sending AWS SNS notification to User ID: $userId", ['ARN' => $snsEndpointArn]);

    //                 $result = $this->snsNotificationService->sendPushNotification($userId, $notificationTitle, $notificationBody);
                    
    //                 if ($result) {
    //                     Log::info("✅ SNS Notification sent successfully for User ID: $userId");
                        
    //                     // Save the notification in the database
    //                     Notification::create([
    //                         'user_id' => $userId,
    //                         'message' => $notificationBody, // Changed from $message to $notificationBody
    //                         'message_type' => AppConstant::NOTIFICATION_ALL,
    //                         'is_read' => 0,
    //                         'created_at' => now(),
    //                         'updated_at' => now()
    //                     ]);            
    //                 } else {
    //                     Log::error("❌ SNS Notification failed for User ID: $userId");
    //                 }
    //             } catch (\Exception $e) {
    //                 Log::error("❌ Error processing notification for User ID: $userId", [
    //                     'error' => $e->getMessage(),
    //                     'file' => $e->getFile(),
    //                     'line' => $e->getLine()
    //                 ]);
    //                 continue; // Continue with next user even if current one fails
    //             }
    //         }
    //         Session::flash('success', __('messages.messageAddSuccess'));
    //         return redirect()->route('message.messages');

    //     } catch (QueryException $e) {
    //         Log::error("❌ Error in addMessage: " . $e->getMessage());
    //         return redirect()->back()->with('error', __('messages.serverError'));
    //     }
    // }
//    public function addMessage(AddMessageRequest $request)
//    {
//        try {
//            $message = new MessageModel();
//            $message->title = $request->title;
//            $message->subtitle = $request->subtitle;
//            $message->category_id = $request->category_id;
//            $message->body = $request->body;
//            $message->status = AppConstant::STATUS_ACTIVE;
//            $message->save();
//
//            $notificationTitle = strip_tags($request->title);
//            $notificationBody = strip_tags($request->body);
//
//            // Fetch users with existing valid SNS endpoints (updated by scheduled job)
//            $users = DB::table('user_access')
//                ->whereNotNull('sns_endpoint_arn')
//                ->groupBy('user_id')
//                ->get();
//
//            if ($users->isEmpty()) {
//                Log::warning("⚠️ No users with valid SNS endpoints found. SNS endpoints are updated every 12 hours.");
//                Session::flash('warning', __('messages.noUsersFound') . ' SNS endpoints are updated automatically every 12 hours.');
//                return redirect()->route('message.messages');
//            }
//
//            $totalUsers = $users->count();
//            Log::info("📋 Sending notifications to {$totalUsers} users for Message ID: {$message->id}");
//
//            // Send notifications synchronously in chunks to avoid memory issues
//            $chunkSize = 10;
//            $successCount = 0;
//            $failureCount = 0;
//
//            // Option 1: Chunk approach (recommended for large batches)
//            $chunkSize = 25; // Adjust based on your server capacity
//            $chunks = $users->chunk($chunkSize);
//
//            foreach ($chunks as $index => $chunk) {
//                // Dispatch job to your existing notification queue
//                SendBulkPushNotificationJob::dispatch(
//                    $chunk->toArray(),
//                    $notificationTitle,
//                    $notificationBody,
//                    $message->id,
//                    null
//                )
//                    ->onQueue('SendMessageNotificationUser');
//            }
//
//
//            Log::info("🎯 Notification sending completed. Success: {$successCount}, Failures: {$failureCount}");
//            Session::flash('success', __('messages.messageAddSuccess') . " Notifications sent to {$successCount} users.");
//
//            return redirect()->route('message.messages');
//
//        } catch (QueryException $e) {
//            Log::error("❌ Error in addMessage: " . $e->getMessage());
//            Session::flash('error', __('messages.serverError'));
//            return redirect()->back();
//        }
//    }

    public function addMessage(AddMessageRequest $request)
    {
        try {
            $message = new MessageModel();
            $message->title = $request->title;
            $message->subtitle = $request->subtitle;
            $message->category_id = $request->category_id;
            $message->body = $request->body;
            $message->status = AppConstant::STATUS_ACTIVE;
            $message->save();

            $notificationTitle = strip_tags($request->title);
            $notificationBody = strip_tags($request->body);

            // Get total count for progress tracking
            $totalUsers = DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->count();

            Log::info("📋 Starting notification process for {$totalUsers} users with Message ID: {$message->id}");

            // Dispatch chunked jobs for endpoint management and notifications
            $chunkSize = 100;
            $offset = 0;
            $jobCount = 0;

            while ($offset < $totalUsers) {
                ProcessEndpointAndNotifyJob::dispatch(
                    $message->id,
                    $notificationTitle,
                    $notificationBody,
                    $offset,
                    $chunkSize
                )->onQueue('SendMessageNotificationUser');

                $offset += $chunkSize;
                $jobCount++;
            }

            Log::info("🎯 Queued {$jobCount} jobs for {$totalUsers} users");
            Session::flash('success',
                __('messages.messageAddSuccess') .
                " Processing notifications for {$totalUsers} users in {$jobCount} batches."
            );

            return redirect()->route('message.messages');

        } catch (QueryException $e) {
            Log::error("❌ Error in addMessage: " . $e->getMessage());
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }
    }


//    private function updateSnsEndpointsSynchronously()
//    {
//        $chunkSize = 500; // Process 500 users at a time
//        $totalProcessed = 0;
//
//        DB::table('user_access')
//            ->whereNotNull('fcm_token')
//            ->select('user_id', 'fcm_token', 'sns_endpoint_arn')
//            ->orderBy('user_id')
//            ->chunk($chunkSize, function ($users) use (&$totalProcessed) {
//                foreach ($users as $user) {
//                    try {
//                        $this->snsNotificationService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
//                        $totalProcessed++;
//                    } catch (\Exception $e) {
//                        Log::error("SNS Endpoint update failed for user {$user->user_id}: " . $e->getMessage());
//                    }
//                }
//
//                Log::info("🔄 Processed {$totalProcessed} users for SNS endpoint updates");
//
//                // Small delay to prevent overwhelming AWS API
//                usleep(100000); // 0.1 second delay between chunks
//            });
//
//        Log::info("✅ Completed SNS endpoint updates for {$totalProcessed} users");
//    }



}

