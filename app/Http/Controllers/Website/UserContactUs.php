<?php

namespace App\Http\Controllers\Website;

use App\Jobs\WebsiteFeedback;
use App\Mail\WebsiteFeedbackMail;
use App\Utils\AppConstant;
use http\Message;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;

class UserContactUs extends Controller
{
    // no more in use
    public function contactUsUser(Request $request)
    {
        try {
            // return $request->recaptcha_response; exit;
            if ($request->recaptcha_response) {
                $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
                $recaptcha_secret = env('RECAPTCHA_SECRET_KEY');
                $recaptcha_response = $request->recaptcha_response;
                $recaptcha = file_get_contents($recaptcha_url . '?secret=' . $recaptcha_secret . '&response=' . $recaptcha_response);
                $recaptcha = json_decode($recaptcha);
                
                if ($recaptcha->success != true)
                    // Session::put('message', 'Internal Server Error.');
                    // Session::put('alert', 'alert-danger');
                    // Session::put('clear', true);
                    return back()->with('message', 'Captcha Error');
            }
            $userMessage = new \App\Models\UserContactUs();
            $userMessage->name = $request->name;
            $userMessage->email = $request->email;
            $userMessage->message = $request->message;
            $userMessage->status = AppConstant::STATUS_ACTIVE;
            $userMessage->save();
            $email = new WebsiteFeedbackMail($userMessage);
    
            $clientMail = AppConstant::CLIENT_EMAIL;
            Mail::to($clientMail)->send($email);
            //dispatch(new WebsiteFeedback($userMessage))->onQueue(AppConstant::APP_QUEUE);
            Session::put('message', 'Message sent successfully!');
            Session::put('alert', 'alert-success');
            Session::put('clear', true);
            return redirect()->back();
        } catch (QueryException $e) {
            Session::put('message', 'Internal Server Error.');
            Session::put('alert', 'alert-danger');
            Session::put('clear', true);
            return redirect()->back();
        }
    }
}
