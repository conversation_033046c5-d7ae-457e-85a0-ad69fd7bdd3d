<?php

namespace App\Http\Controllers\Common;

use App\Http\Requests\Api\ForgotPassword\forgotPasswordRequest;
use App\Models\UserModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;

class AuthController extends Controller
{
    use ApiResponse;

    public function resetPasswordView(Request $request, $uuid, $forgotPasswordCode)
    {
        $isValid = URL::hasValidSignature($request);
        $checkForgotCode = UserModel::where(array(
            'uuid' => $uuid,
            'status' => AppConstant::STATUS_ACTIVE,
            'forgot_password_code' => $forgotPasswordCode
        ))->first();
        if ($checkForgotCode == "") {
            return view('web.forbidden');
            /*return view('web.forbidden');*/
            /*$this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('message.recordNotFound'));
            return response()->json($this->setResponse(), AppConstant::NOT_FOUND);*/
        }

        $now = Carbon::now('UTC');
        $expire = Carbon::createFromTimestamp($request->expires);
        $diff = $expire->diffInHours($now, true);
        if ($diff > 0 && $diff <= 1) {
            $data = array(
                'uuid' => $uuid
            );
            return view('web.resetPassword')->withdata($data);
        } else {
            return view('web.forbidden');
        }

    }


    public function resetPassword(forgotPasswordRequest $request)
    {
        try {
            DB::beginTransaction();
            UserModel::where('uuid', $request->uuid)->update(array(
                'password' => Hash::make($request->password),
                'forgot_password_code' => NULL
            ));
            DB::commit();

//            Session::flash('success', __('auth.password_update_success'));
            return redirect('reset_success');
//            return view('web.passwordUpdateSuccess');
        } catch (QueryException $e) {
            DB::rollBack();
            Session::flash('error', __('auth.server_error'));
            return redirect()->back()->withInput();
        }
    }


}
