<?php

namespace App\Http\Controllers\Api\v3\User;

use App\Http\Requests\Api\Auth\changePasswordRequest;
use App\Http\Requests\Api\Auth\changeProfilePictureRequest;
use App\Http\Requests\Api\Auth\deleteUserRequest;
use App\Http\Requests\Api\Auth\TickerToggleRequest;
use App\Http\Requests\Api\Guest\GuestRequest;
use App\Models\CategoryModel;
use App\Http\Requests\Api\Auth\notificationToggleRequest;
use App\Models\CountryModel;
use App\Models\UserNotificationsModel;
use App\Rules\uuid;
use App\Models\DeliveryTermModel;
use App\Models\Notification;
use App\Models\PackingModel;
use App\Models\ProductFavouriteModel;
use App\Models\ProductLoadingPortModel;
use App\Models\ProductModel;
use App\Models\TickerModel;
use App\Models\UnitModel;
use App\Models\UserAccess;
use App\Models\UserModel;
use App\Services\SNSNotificationService;
use App\Traits\ApiResponse;
use App\Traits\GetUuidDetail;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Auth\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
class UserController extends Controller
{
    use ApiResponse;
    use GetUuidDetail;

    protected  $snsService, $snsClient;

    function __construct(SNSNotificationService $snsService)
    {

        $this->snsService = $snsService;

        // Initialize the SNS client
        $this->snsClient = new \Aws\Sns\SnsClient([
            'version' => 'latest',
            'region'  => config('services.sns.region'),
            'credentials' => [
                'key'    => config('services.sns.key'),
                'secret' => config('services.sns.secret'),
            ],
        ]);
    }
    public function guest_user(GuestRequest $request)
    {
        try {
            $new_guest = UserAccess::updateOrCreate(
                ['device_id' => $request->device_id],
                [
                    'user_id' => $request->user_id,
                    'fcm_token' => $request->fcm_token,
                    'device_id' => $request->device_id,
                    'os' => $request->os,
                    'status' => AppConstant::STATUS_ACTIVE,
                ]
            );

            // Enable or create SNS endpoint
            if ($new_guest->sns_endpoint_arn) {
                // Re-enable existing endpoint
                try {
                    $this->snsClient->setEndpointAttributes([
                        'EndpointArn' => $new_guest->sns_endpoint_arn,
                        'Attributes' => [
                            'Enabled' => 'true',
                            'Token' => $request->fcm_token, // update token if changed
                        ],
                    ]);
                    Log::info('SNS endpoint re-enabled.', ['EndpointArn' => $new_guest->sns_endpoint_arn]);
                } catch (\Exception $e) {
                    Log::error('Error re-enabling SNS endpoint: ' . $e->getMessage());
                }
            } else {
                try {
                    $result = $this->snsClient->createPlatformEndpoint([
                        'PlatformApplicationArn' => config('services.sns.platform_application_arn'),
                        'Token' => $request->fcm_token,
                        'CustomUserData' => 'guest_user_' . $new_guest->user_id,
                    ]);

                    $endpointArn = $result->get('EndpointArn');

                    // Save endpoint ARN to DB
                    $new_guest->sns_endpoint_arn = $endpointArn;
                    $new_guest->save();

                    Log::info('New SNS endpoint created.', ['EndpointArn' => $endpointArn]);
                } catch (\Exception $e) {
                    Log::error('Error creating new SNS endpoint: ' . $e->getMessage());
                }
            }

        } catch (QueryException $e) {
            Log::error('DB error during guest_user:', ['error' => $e->getMessage()]);

            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.guestUserSuccess'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }


    public function notifications_options(Request $request)
    {
        try {

            $userId = $request->user->id;
            $category = CategoryModel::with(['user_notification' => function ($q) use ($userId) {
                $q->where('user_id', $userId);
            }])->get();
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.RecordFound'));
        $this->setData('notification_options', $category);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function ticker_options(Request $request)
    {
        try {
            $userId = $request->user->id;
            $ticker = CategoryModel::with(['ticker_options' => function ($q) use ($userId) {
                $q->where('user_id', $userId);
            }])->get();
        } catch (QueryException $q) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.RecordFound'));
        $this->setData('category_option', $ticker);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function notifications_toggle(Request $request)
    {
        $category_id = $request->category_id;
        $userId = $request->user->id;
        try {
            $request->user->notifications()->sync($category_id);
            /* $valid_category = CategoryModel::where([
                 'id' =>$category_id
             ])->first();

             if ($valid_category == ''){
                 $this->setMeta('status', AppConstant::STATUS_FAIL);
                 $this->setMeta('message', __('apimessages.RecordNotFound'));
                 return response()->json($this->setResponse(), JsonResponse::HTTP_NOT_FOUND);
             }

             $category = UserNotificationsModel::where([
                 'category_id' => $category_id,
                 'user_id' => $request->user->id
             ])->first();

             if ($category == ''){


                 DB::table('user_notifications')->insert([
                     ['category_id' => $category_id, 'user_id' => $request->user->id],
                 ]);

                 $message = __('apimessages.NotificationsON');

             }else{
                 UserNotificationsModel::where([
                     'category_id' => $category_id,
                     'user_id' => $request->user->id
                 ])->delete();

                 $message = __('apimessages.NotificationsOFF');
             }*/
            $category = CategoryModel::with(['user_notification' => function ($q) use ($userId) {
                $q->where('user_id', $userId);
            }])->get();

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', __('apimessages.NotificationsUpdated'));
            $this->setData('notification_options', $category);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);

        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function ticker_toggle(TickerToggleRequest $request)
    {
        $category_id = $request->category_id;
        $userId = $request->user->id;
        try {
            $request->user->ticker()->sync($category_id);

            $ticker = TickerModel::with('category')->where([
                'user_id' => $request->user->id
            ])->get();

            $ticker_options = CategoryModel::with(['ticker_options' => function ($q) use ($userId) {
                $q->where('user_id', $userId);
            }])->get();

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', __('apimessages.tickerStatusUpdated'));
            $this->setData('dashboard_option', $ticker);
            $this->setData('category_option', $ticker_options);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);

        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function message_status_update(Request $request)
    {
        $user = $request->user->id;
        $message_status = $request->user->message_notifications;
        try {
            if ($message_status == AppConstant::STATUS_ACTIVE) {
                $message_status = AppConstant::STATUS_INACTIVE;

                $message = __('apimessages.messageStatusOFF');
            } else {
                $message_status = AppConstant::STATUS_ACTIVE;

                $message = __('apimessages.messageStatusON');
            }

            UserModel::where('id', $user)->update(['message_notifications' => $message_status]);

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', $message);
            $this->setData('message_notifications', $message_status);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function favourite_status_update(Request $request)
    {
        $user = $request->user->id;
        $favourite_option = $request->user->favourite_option;

        try {
            if ($favourite_option == AppConstant::STATUS_ACTIVE) {
                $favourite_option = AppConstant::STATUS_INACTIVE;

                $message = __('apimessages.favouriteStatusOFF');
            } else {
                $favourite_option = AppConstant::STATUS_ACTIVE;

                $message = __('apimessages.favouriteStatusON');
            }

            UserModel::where('id', $user)->update(['favourite_option' => $favourite_option]);

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', $message);
            $this->setData('favourite_option', $favourite_option);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function change_password(changePasswordRequest $request)
    {
        $old_password = $request->old_password;
        try {
            if (!Hash::check($old_password, $request->user->password)) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.wrongPassword'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_UNAUTHORIZED);
            }

            // Update new password
            UserModel::where('uuid', $request->user->uuid)->update(array(
                'password' => Hash::make($request->new_password)
            ));

        } catch (QueryException $q) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.passwordChanged'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function change_profile_picture(changeProfilePictureRequest $request)
    {
        $file = $request->file('profile_pic');
        $image = rand(111111, 999999) . "_" . $file->getClientOriginalName();
        $uuid = $request->user->uuid;
        $imagePath = $request->file('profile_pic')->storeAs(
            'user/' . $uuid, $request->uuid . '.' . $image,
            'public');

        try {

            UserModel::where('id', $request->user->id)->update(['profile_pic' => $imagePath]);

            $user = UserModel::where('id', $request->user->id)->first();

        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.profilePictureChanged'));
        $this->setData('user', $user->makeHidden(['id', 'uuid', 'fullname', 'email_id', 'country_code', 'mobile_no', 'company_name', 'is_email_verified', 'message_notifications', 'favourite_option']));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
    public function remove_profile_picture(Request $request)
    {
        $uuid = $request->user->uuid;
        
        try {
            // Get the current profile picture path
            $user = UserModel::where('id', $request->user->id)->first();
            $currentProfilePic = $user->profile_pic;
            
            // Delete the file if it exists
            if ($currentProfilePic && Storage::disk('public')->exists($currentProfilePic)) {
                Storage::disk('public')->delete($currentProfilePic);
            }
            
            // Update the user record to remove the profile picture reference
            UserModel::where('id', $request->user->id)->update(['profile_pic' => null]);
            
            // Get the updated user data
            $user = UserModel::where('id', $request->user->id)->first();
            
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.profilePictureRemoved'));
        $this->setData('user', $user->makeHidden(['id', 'uuid', 'country_code', 'mobile_no', 'company_name', 'is_email_verified', 'message_notifications', 'favourite_option']));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
    public function dashboardOptions(Request $request)
    {
        try {
            /*$ticker = TickerModel::with('category')->where([
                'user_id' => $request->user->id
            ])->get();*/
            $dashboard = CategoryModel::where('status', AppConstant::STATUS_ACTIVE)->get();

        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.categoriesSuccess'));
        $this->setData('dashboard_option', $dashboard);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function quotationList()
    {
        try {
            $port = ProductLoadingPortModel::with('country'/*, 'product'*/)->where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();

            /*$country = CountryModel::where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();*/

            $unit = UnitModel::where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();

            $packing = PackingModel::where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();

            $category = CategoryModel::where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();

            $term = DeliveryTermModel::where([
                'status' => AppConstant::STATUS_ACTIVE
            ])->get();


        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.quotationListSuccess'));
        $this->setData('destination_port', $port);
        $this->setData('unit', $unit);
        $this->setData('packing', $packing);
        $this->setData('category', $category);
        $this->setData('delivery_term', $term);
        /*$this->setData('country', $country);*/
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function product_list(Request $request)
    {
        try {
            // $products = ProductModel::with('category', 'currency')
            //     ->where([
            //         'category_id' => $request->category_id,
            //         'status' => AppConstant::STATUS_ACTIVE])
            //     ->orderBy('updated_at', 'desc')
            //     ->get();
            $query = ProductModel::with('category', 'currency')
            ->where('status', AppConstant::STATUS_ACTIVE);

            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }
            $products = $query->orderBy('updated_at', 'desc')
            ->get();

            /*->paginate(AppConstant::PAGINATE_PRODUCT);*/
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        /*$pagination = [
            "total" => $products->total(),
            "current_page" => $products->currentPage(),
            "next_page_url" => $products->nextPageUrl(),
            "previous_page_url" => $products->previousPageUrl(),
        ];*/
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.productListSuccess'));
//        $this->setPaginate($pagination);
        $this->setData('products', $products->makeHidden(
            ['first_page_url', 'last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function favouriteProdList(Request $request)
    {
        try {
            $userId = $request->user->id;
            $userFavourites = ProductFavouriteModel::with('product', 'product.category', 'product.packing', 'product.unit', 'product.deliveryTerm', 'product.currency', 'product.productLoadingPort', 'product.productPrice')->where(['user_id' => $userId, 'status' => AppConstant::STATUS_ACTIVE])
                ->orderBy('updated_at', 'desc')
                ->paginate(AppConstant::PAGINATE);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $pagination = [
            "total" => $userFavourites->total(),
            "current_page" => $userFavourites->currentPage(),
            "next_page_url" => $userFavourites->nextPageUrl(),
            "previous_page_url" => $userFavourites->previousPageUrl(),
        ];

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.productListSuccess'));
        $this->setPaginate($pagination);
        $this->setData('favouriteProducts', $userFavourites->makeHidden(
            ['first_page_url', 'last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    /*for fetching profile picture*/
    public function user_details(Request $request)
    {
        try {
            $user = UserModel::where('id', $request->user->id)->get();
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setData('profile_picture', $user->makeHidden(
            ['id', 'uuid', 'email_id', 'is_email_verified', 'message_notifications', 'favourite_option']
        ));
        $this->setMeta('message', __('apimessages.userDetailsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    /*push notifications list API*/
    public function push_notifications(Request $request)
    {
        try {

            $id = $request->user->id;
            $notifications = Notification::with(['user', 'product', 'product.category', 'product.currency', 'product.unit'])
                ->where(['user_id' => $id, 'status' => AppConstant::STATUS_ACTIVE])
                ->orderBy('created_at', 'desc')
                ->paginate(AppConstant::PAGINATE);

//            dd($notifications);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $pagination = [
            "total" => $notifications->total(),
            "current_page" => $notifications->currentPage(),
            "next_page_url" => $notifications->nextPageUrl(),
            "previous_page_url" => $notifications->previousPageUrl(),
        ];
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setPaginate($pagination);
        $this->setData('data', $notifications->makeHidden(
            ['id', 'is_read', 'status', 'updated_at', 'first_page_url', 'last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        $this->setMeta('message', __('apimessages.pushNotificationsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function guest_push_notifications(Request $request)
    {
        try {
            $notifications = Notification::with(['user', 'product', 'product.category', 'product.currency', 'product.unit'])
                ->where(['user_id' => null, 'status' => AppConstant::STATUS_ACTIVE])
                ->orderBy('created_at', 'desc')
                ->paginate(AppConstant::PAGINATE);
//            dd($notifications);

        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $pagination = [
            "total" => $notifications->total(),
            "current_page" => $notifications->currentPage(),
            "next_page_url" => $notifications->nextPageUrl(),
            "previous_page_url" => $notifications->previousPageUrl(),
        ];
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setPaginate($pagination);
        $this->setData('data', $notifications->makeHidden(
            ['id', 'is_read', 'status', 'updated_at', 'first_page_url', 'last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        $this->setMeta('message', __('apimessages.pushNotificationsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
    // Delete user account
    public function deleteUser(deleteUserRequest $request) {
        try {
            DB::beginTransaction();
            $user_id = $request->user_id;
            $name = $request->user->full_name;
            //$user = UserModel::where('id', $request->user_id)->first();
            if ($request->user_id != $request->user->id) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message',$request->user->id);
                return response()->json($this->setResponse(), JsonResponse::HTTP_UNAUTHORIZED);
            }

            if($request->user->is_social==AppConstant::STATUS_INACTIVE  && isset($request->password) && !Hash::check($request->password, $request->user->password)) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.wrongPassword'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_UNAUTHORIZED);
            }

            $userData = UserModel::where('uuid', $request->user->uuid)->delete();

            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', $e->getMessage());
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.userDeleteSuccess', ['name' => $name]));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

}