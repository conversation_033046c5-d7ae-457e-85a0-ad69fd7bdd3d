<?php

namespace App\Http\Controllers\Api\v3\UserFeedback;

use App\Http\Requests\Api\UserFeedback\UserFeedbackRequest;
use App\Jobs\FeedbackReceived;
use App\Mail\FeedbackReceivedMail;
use App\Models\UserFeedback;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;

class UserFeedbackController extends Controller
{
    use ApiResponse;

    public function store(UserFeedbackRequest $request){
        try{
            // no more in use
            // $feedback = new UserFeedback();
            // $feedback->email_id = $request->email_id;
            // $feedback->message = $request->message;
            // $feedback->save();
            $email = new FeedbackReceivedMail($request->email_id, $request->message);

            $clientMail = AppConstant::CLIENT_EMAIL;
            Mail::to($clientMail)->send($email);
           // dispatch(new FeedbackReceived($request->email_id, $request->message))->onQueue(AppConstant::APP_QUEUE);
        }catch (QueryException $e){
            Session::flash('error', __('messages.serverError'));
            return redirect()->back();
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.feedback'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }
}
