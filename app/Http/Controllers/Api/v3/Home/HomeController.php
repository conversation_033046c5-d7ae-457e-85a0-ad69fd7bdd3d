<?php

namespace App\Http\Controllers\Api\v3\Home;

use App\Models\TickerModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    use ApiResponse;

    public function ticker(Request $request){
        try{
            DB::beginTransaction();
            $ticker = TickerModel::where([
                'user_id' => $request->user_id
            ])->get();

            DB::commit();

        }catch (QueryException $e){
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.RecordFound'));
        $this->setData('type', $ticker);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
}
