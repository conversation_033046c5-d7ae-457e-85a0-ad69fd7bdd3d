<?php

namespace App\Http\Controllers\Api\v3\Category;
use App\Http\Requests\Api\Category\fetchProductRequest;
use App\Models\ProductModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CategoryController extends Controller
{
    use ApiResponse;

    public function fetchProducts(fetchProductRequest $request){
//        dd($request->user);

        $userId = $request->user->id;
        try{
            $products = ProductModel::with(['category', 'packing', 'unit', 'deliveryTerm', 'currency',
                'productFavorite' => function($q) use ($userId) {
                $q->where(['user_id' => $userId, 'status' => AppConstant::STATUS_ACTIVE]);
            },
                'productLoadingPort', 'productPrice' => function($p){
                $p->where('status', AppConstant::STATUS_ACTIVE);
            }])
                ->where(['category_id' => $request->category_id,
                'status' => AppConstant::STATUS_ACTIVE])
                ->paginate(AppConstant::PAGINATE_PRODUCT);
        }catch (QueryException $e){
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $pagination = [
            "total" => $products->total(),
            "current_page" => $products->currentPage(),
            "next_page_url" => $products->nextPageUrl(),
            "previous_page_url" => $products->previousPageUrl(),
        ];

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setPaginate($pagination);
        $this->setData('products', $products->makeHidden(
            ['first_page_url','last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        $this->setMeta('message', __('apimessages.productsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function fetchGuestProducts(fetchProductRequest $request){

        try{
            $products = ProductModel::with(['category', 'packing', 'unit', 'deliveryTerm', 'currency', 'productLoadingPort', 'productPrice' => function($p){
                $p->where('status', AppConstant::STATUS_ACTIVE);
            }])->where(['category_id' => $request->category_id,
                'status' => AppConstant::STATUS_ACTIVE])->paginate(AppConstant::PAGINATE_PRODUCT);
        }catch (QueryException $e){
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $pagination = [
            "total" => $products->total(),
            "current_page" => $products->currentPage(),
            "next_page_url" => $products->nextPageUrl(),
            "previous_page_url" => $products->previousPageUrl(),
        ];

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setPaginate($pagination);
        $this->setData('products', $products->makeHidden(
            ['first_page_url','last_page', 'last_page_url', 'next_page_url',
                'per_page', 'prev_page_url', 'total']
        ));
        $this->setMeta('message', __('apimessages.productsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
}