<?php

namespace App\Http\Controllers\Api\v3\Product;

use App\Http\Requests\Api\Product\cifQuotationRequest;
use App\Http\Requests\Api\Product\productDetailsRequest;
use App\Http\Requests\Api\Product\productFavoriteRequest;
use App\Http\Requests\Api\Product\productGraphRequest;
use App\Jobs\QuotationReceived;
use App\Mail\QuotationReceivedMail;
use App\Models\MessageModel;
use App\Models\ProductFavouriteModel;
use App\Models\ProductModel;
use App\Models\ProductPriceModel;
use App\Models\QuotationModel;
use App\Models\TickerModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use phpDocumentor\Reflection\DocBlock\Description;

class ProductController extends Controller
{
    use ApiResponse;

    public function cifQuotation(cifQuotationRequest $request)
    {
        $userID = $request->user->id;
        Log::info("CIF Quotation request started by user ID: {$userID}");

        try {
            // Initialize new quotation
            $quotation = new QuotationModel();
            $quotation->user_id = $userID;
            $quotation->packing_id = $request->packing_id;
            $quotation->unit_id = $request->unit_id;
            $quotation->email_id = $request->email_id;
            $quotation->product_name = $request->product_name;
            $quotation->destination_port = $request->destination_port;
            $quotation->trade_term = $request->trade_term;
            $quotation->country = $request->country;
            $quotation->quantity = $request->quantity;
            $quotation->message = $request->message;

            // Save quotation to database
            $quotation->save();
            Log::info("Quotation saved successfully. ID: {$quotation->id}");

            // Send email notification to client
            $email = new QuotationReceivedMail($quotation);
            $clientMail = AppConstant::CLIENT_EMAIL;

            Mail::to($clientMail)->send($email);
            Log::info("Quotation email sent to client: {$clientMail}");

            // Optional: Dispatch job (currently commented out)
            // dispatch(new QuotationReceived($quotation))->onQueue(AppConstant::APP_QUEUE);
            // Log::info("Quotation job dispatched to queue.");

        } catch (QueryException $e) {
            Log::error("Database error while saving quotation: " . $e->getMessage());

            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            Log::error("General error in CIF Quotation: " . $e->getMessage());

            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        // Success response
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.quotationSaved'));
        Log::info("CIF Quotation process completed successfully for user ID: {$userID}");

        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }


    public function get_messages(Request $request)
    {
        //        if(!$request->user->message_notifications){
        //            $this->setMeta('message', __('apimessages.message_notifications_off'));
        //            return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
        //        }
        $category = MessageModel::where('status', AppConstant::STATUS_ACTIVE)->with('category')->orderby('created_at', 'desc')->paginate(AppConstant::PAGINATE);
        $pagination = [
            "total" => $category->total(),
            "current_page" => $category->currentPage(),
            "next_page_url" => $category->nextPageUrl(),
            "previous_page_url" => $category->previousPageUrl(),
        ];

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setPaginate($pagination);
        $this->setData('messages', $category->makeHidden(
            [
                'first_page_url',
                'last_page',
                'last_page_url',
                'next_page_url',
                'per_page',
                'prev_page_url',
                'total'
            ]
        ));
        $this->setMeta('message', __('apimessages.messagesFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }

    /*for message detail in admin push notification for message*/
    public function message_details(Request $request)
    {

        $message = MessageModel::where(['id' => $request->message_id, 'status' => AppConstant::STATUS_ACTIVE])->first();

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setData('message', $message);
        $this->setMeta('message', __('apimessages.messageDetailsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function product_favorite(productFavoriteRequest $request)
    {
        $productID = $request->product_id;
        $userID = $request->user->id;

        try {
            $validProduct = ProductModel::where(['id' => $productID])->first();

            if ($validProduct == '') {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.RecordNotFound'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_NOT_FOUND);
            }

            $product = ProductFavouriteModel::where([
                'product_id' => $productID,
                'user_id' => $request->user->id
            ])->first();

            if ($product == '') {
                $productFavorite = new ProductFavouriteModel();
                $productFavorite->product_id = $productID;
                $productFavorite->user_id = $userID;
                $productFavorite->status = AppConstant::STATUS_ACTIVE;
                $productFavorite->save();
                $this->setData('status', $productFavorite->status);
                $message = __('apimessages.productFavoriteAdded');
            } else {
                if ($product && $product->status == AppConstant::STATUS_INACTIVE) {
                    $product->status = AppConstant::STATUS_ACTIVE;
                    $product->save();
                    $this->setData('status', $product->status);
                    $message = __('apimessages.productFavoriteAdded');
                } else {
                    $product->status = AppConstant::STATUS_INACTIVE;
                    $product->save();
                    $this->setData('status', $product->status);
                    $message = __('apimessages.productFavoriteRemoved');
                }
            }

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', $message);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function product_details(productDetailsRequest $request)
    {
        $userId = $request->user->id;
        $productId = $request->product_id;
        try {
            $products = ProductModel::with(['category', 'packing', 'unit', 'deliveryTerm', 'currency', 'productFavorite' => function ($q) use ($userId) {
                $q->where('user_id', $userId);
            }, 'productLoadingPort', 'productPrice' => function ($p) {
                $p->where('status', AppConstant::STATUS_ACTIVE);
            }, 'documents'])->where([
                'id' => $productId,
                'status' => AppConstant::STATUS_ACTIVE
            ])->first();


            $productGraph = ProductModel::where('created_at', '>', Carbon::now()->startOfWeek())->where('created_at', '<', Carbon::now()->endOfWeek())->get()->groupBy(function ($date) {
                return Carbon::parse($date->created_at)->format('Y-m-d');
            });

            if ($products == '') {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.RecordNotFound'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_NOT_FOUND);
            }
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }


        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setData('product', $products);
        $this->setData('graphDetails', $productGraph);
        $this->setMeta('message', __('apimessages.productDetailsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }

    public function product_graph(Request $request)
    {
        try {
            if ($request->graph_type > 5 || $request->graph_type < 1) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.invalidGraphType'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
            }

            $productId = $request->product_id;
            $product = $this->fetchProductData($productId, $request->graph_type);

            // Initialize empty groupedProducts array with all expected time periods
            $groupedProducts = $this->initializeTimePeriods($request->graph_type);

            // Fill the groupedProducts with actual data
            $groupedProducts = $this->populateProductData($product, $groupedProducts, $request->graph_type);

            // Remove empty arrays from groupedProducts
            $groupedProducts = array_filter($groupedProducts, function ($value) {
                return !empty($value);
            });

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setData('productDetails', $groupedProducts);
            $this->setMeta('message', __('apimessages.productGraphSuccess'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
//    public function product_graph(Request $request)
//    {
//        try {
//            if ($request->graph_type > 3 || $request->graph_type < 1) {
//                $this->setMeta('status', AppConstant::STATUS_FAIL);
//                $this->setMeta('message', __('apimessages.invalidGraphType'));
//                return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
//            }
//
//            $prductId = $request->product_id;
//
//            switch ($request->graph_type) {
//                case 1:
//
//                    /*$product = ProductPriceModel::where(['product_id' => $prductId])
//                        ->selectRaw('DATE(created_at) as date, min_price, max_price, price_diff')->orderBy('created_at', 'desc')->groupBy('date')->get();*/
//
//                    /*$product = ProductPriceModel::where(['product_id' => $request->product_id])->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->latest()->get();*/
//                    /*$product = ProductPriceModel::where(['product_id' => $request->product_id])->whereDateBetween('created_at',(new Carbon)->subDays(7)->toDateString(),(new Carbon)->now()->toDateString() )->get();*/
//
//                    //for past seven days data
//                    $product = ProductPriceModel::where(['product_id' => $request->product_id])
//                        ->where( 'created_at', '>', Carbon::now()->subDays(7))
//                        ->get();
//                    break;
//
//                case 2:
//                    /*$product = ProductPriceModel::where(['product_id' => $request->product_id])
//                        ->where('created_at', '>', Carbon::now()->startOfYear())
//                        ->where('created_at', '<', Carbon::now()->endOfYear())->get();*/
//
//                    $product = ProductPriceModel::where(['product_id' => $request->product_id])
//                        ->where( 'created_at', '>', Carbon::now()->subDays(30))
//                        ->get();
//                    break;
//
//                case 3:
//                    /*$product = ProductPriceModel::where(['product_id' => $request->product_id])->where('created_at', '>', Carbon::now()->startOfCentury())->where('created_at', '<', Carbon::now()->endOfCentury())->get();*/
//
//                    $product = ProductPriceModel::where(['product_id' => $request->product_id])
//                        ->where( 'created_at', '>', Carbon::now()->subDays(365))
//                        ->get();
//                    break;
//            }
//        } catch (QueryException $e) {
//
//            $this->setMeta('status', AppConstant::STATUS_FAIL);
//            $this->setMeta('message', __('apimessages.serverError'));
//            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
//        }
//        $this->setMeta('status', AppConstant::STATUS_OK);
//        $this->setData('productDetails', $product);
//        $this->setMeta('message', __('apimessages.productGraphSuccess'));
//        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
//    }

    /**
     * Fetch product price data based on graph type
     *
     * @param int $productId Product ID
     * @param int $graphType Graph type (1-5)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function fetchProductData($productId, $graphType)
    {
        $query = ProductPriceModel::where('product_id', $productId);

        switch ($graphType) {
            case 1: // Weekly - last 7 days
                $query->where('created_at', '>', Carbon::now()->subDays(7));
                break;
            case 2: // Monthly - last 30 days
                $query->where('created_at', '>', Carbon::now()->subDays(30));
                break;
            case 3: // Last 3 months
                $query->where('created_at', '>', Carbon::now()->subMonths(3));
                break;
            case 4: // Last 6 months
                $query->where('created_at', '>', Carbon::now()->subMonths(6));
                break;
            case 5: // Yearly - last 365 days
                $query->where('created_at', '>', Carbon::now()->subDays(365));
                break;
        }

        return $query->get();
    }

    /**
     * Initialize time periods based on graph type
     *
     * @param int $graphType Graph type (1-5)
     * @return array Empty structure for time periods
     */
    private function initializeTimePeriods($graphType)
    {
        $groupedProducts = [];

        switch ($graphType) {
            case 1: // Weekly - last 7 days
                $groupedProducts = $this->initializeDailyPeriods(7);
                break;
            case 2: // Monthly - last 30 days
                $groupedProducts = $this->initializeMonthlyPeriods(1);
                break;
            case 3: // Last 3 months
                $groupedProducts = $this->initializeMonthlyPeriods(3);
                break;
            case 4: // Last 6 months
                $groupedProducts = $this->initializeMonthlyPeriods(6);
                break;
            case 5: // Yearly - last 12 months
                $groupedProducts = $this->initializeMonthlyPeriods(12);
                break;
        }

        return $groupedProducts;
    }

    /**
     * Initialize daily time periods
     *
     * @param int $days Number of days
     * @return array
     */
    private function initializeDailyPeriods($days)
    {
        $periods = [];
        $startDate = Carbon::now()->subDays($days - 1)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        for ($date = clone $startDate; $date->lte($endDate); $date->addDay()) {
            $periods[$date->format('d M')] = [];
        }

        return $periods;
    }

    /**
     * Initialize monthly time periods
     *
     * @param int $months Number of months
     * @return array
     */
    private function initializeMonthlyPeriods($months)
    {
        $periods = [];
        $currentMonth = Carbon::now()->startOfMonth();

        for ($i = $months - 1; $i >= 0; $i--) {
            $month = (clone $currentMonth)->subMonths($i);
            $periods[$month->format('M Y')] = [];
        }

        return $periods;
    }

    /**
     * Populate product data into the time periods
     *
     * @param \Illuminate\Database\Eloquent\Collection $product Product data
     * @param array $groupedProducts Empty structure for time periods
     * @param int $graphType Graph type (1-5)
     * @return array Populated product data grouped by time periods
     */
    private function populateProductData($product, $groupedProducts, $graphType)
    {
        foreach ($product as $item) {
            if (isset($item->created_at)) {
                $createdAt = Carbon::parse($item->created_at);

                // Determine the grouping key based on graph type
                $groupKey = '';
                switch ($graphType) {
                    case 1: // Weekly
                    case 2: // Monthly
                        $groupKey = $createdAt->format('d M');
                        break;
                    case 3: // Last 3 months
                    case 4: // Last 6 months
                    case 5: // Yearly
                        $groupKey = $createdAt->format('M Y');
                        break;
                }
            }

            // Only add to valid groups (in case there's data outside our range)
            if (isset($groupedProducts[$groupKey])) {
                $groupedProducts[$groupKey][] = [
                    'id' => $item->id,
                    'product_id' => $item->product_id,
                    'min_price' => $item->min_price,
                    'max_price' => $item->max_price,
                    'price_diff' => $item->price_diff,
                    'status' => $item->status,
                    'created_at' => $createdAt->format('Y-m-d H:i:s')
                ];
            }
        }

        return $groupedProducts;
    }



    public function marquee(Request $request)
    {
        try {
            $marqueeProducts = '';
            $ticker = TickerModel::with('category')->where([
                'user_id' => $request->user->id
            ])->pluck('category_id');

            $marqueeProducts = ProductModel::with(['productPrice', 'currency'])->where(['status' => AppConstant::STATUS_ACTIVE, 'marquee' => AppConstant::STATUS_ACTIVE])
                ->whereIn('category_id', $ticker)
                ->get();
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        /*$this->setData('marqueeProducts', $marqueeProducts->makeHidden(
            ['uuid', 'category_id', 'packing_id', 'unit_id',
                'delivery_term_id', 'currency_id', 'hs_code', 'cas_number', 'marquee']
        ));*/
        $this->setData('marquee', $marqueeProducts);
        $this->setMeta('message', __('apimessages.productsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function get_marquee_guest()
    {
        try {
            $marqueeProductsAll = ProductModel::with(['productPrice', 'currency'])->where(['status' => AppConstant::STATUS_ACTIVE])
                ->get();
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setData('marquee', $marqueeProductsAll);
        $this->setMeta('message', __('apimessages.productsFetched'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
}
