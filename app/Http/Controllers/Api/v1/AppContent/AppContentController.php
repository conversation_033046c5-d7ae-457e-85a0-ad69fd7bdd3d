<?php

namespace App\Http\Controllers\Api\v1\AppContent;

use App\Models\AppContentModel;
use App\Models\CountryModel;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AppContentController extends Controller
{
    use ApiResponse;

    public function appContent(Request $request)
    {
        try {
            $type = AppContentModel::where('type', $request->type)->first();

            if ($type == ''){
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.RecordNotFound'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_NOT_FOUND);
            }
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.RecordFound'));
        $this->setData('type', $type);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }


    public function fetchCountry(){
        try{
            $country = CountryModel::all();
        }catch (QueryException $e){
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.RecordFound'));
        $this->setData('country', $country);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

}
