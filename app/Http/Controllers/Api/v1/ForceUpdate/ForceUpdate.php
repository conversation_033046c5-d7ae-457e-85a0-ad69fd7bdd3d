<?php

namespace App\Http\Controllers\Api\v1\ForceUpdate;

use App\Models\ForceUpdateModel;
use App\Rules\ValidOSType;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class ForceUpdate extends Controller
{
    use ApiResponse;

    public function checkAppVersion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'device_type' => ['required', new ValidOSType()]
        ]);
        if ($validator->fails()) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', $validator->messages()->first());
            return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
        }

        $checkCurrentVersion = ForceUpdateModel::where(array(
            'device_type' => $request->device_type,
            'status' => AppConstant::STATUS_ACTIVE
        ))->first();
        if (!$checkCurrentVersion)
        {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.NotFound'));
            return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('data', $checkCurrentVersion);

        return response()->json($this->setResponse(), AppConstant::OK);
    }
}
