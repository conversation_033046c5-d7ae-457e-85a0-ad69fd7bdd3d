<?php

namespace App\Http\Requests\Admin\Categories;

use App\Rules\AlphaSpace;
use App\Rules\AntiXssFinder;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class CategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'category_name' => ['bail', 'required', 'min:3', 'max:90' . $this->input('id'), new AntiXssFinder, new AlphaSpace],
            'category_image' => ['bail', 'mimes:jpeg,jpg,png', 'max:1000']
        ];
    }

    public function filters()
    {
        return [
            'category_name' => 'trim|capitalize|escape'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        Session::flash('error', $validator->messages()->first());
        throw (new ValidationException($validator))->errorBag($this->errorBag)->redirectTo($this->getRedirectUrl());
    }
}
