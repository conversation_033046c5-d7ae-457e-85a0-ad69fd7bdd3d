<?php

namespace App\Http\Requests\Admin\Messages;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class AddMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
                return [
                    'title' => ['bail', 'required'],
                    'subtitle' => ['bail', 'required'],
                    'body' => ['bail', 'required']
                ];
                break;
            case 'PUT':
                return [
                    'title' => ['bail', 'required'],
                    'subtitle' => ['bail', 'required'],
                    'body' => ['bail', 'required']
                ];
                break;
        }
    }

    protected function failedValidation(Validator $validator)
    {
        Session::flash('error', $validator->messages()->first());
        throw (new ValidationException($validator))->errorBag($this->errorBag)->redirectTo($this->getRedirectUrl());
    }
}
