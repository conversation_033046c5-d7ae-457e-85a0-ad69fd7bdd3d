<?php

namespace App\Http\Requests\Admin\Products;

use App\Rules\AlphaSpace;
use App\Rules\AntiXssFinder;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class ProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
                return [
                    'product_name' => ['bail', 'required', 'min:3', 'max:50', new AntiXssFinder]
                ];
                break;
            case 'PUT':
                return [
                    'product_name' => ['bail', 'required', 'min:3', 'max:50', new AntiXssFinder],
                    'hs_code' => ['bail', 'required', 'numeric', 'unique:product', new AntiXssFinder],
                    'cas_number' => ['bail', 'required', 'numeric', 'unique:product', new AntiXssFinder]
                ];
                break;
        }
    }

    public function filters()
    {
        return [
            'category_name' => 'trim|capitalize|escape'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        Session::flash('error', $validator->messages()->first());
        throw (new ValidationException($validator))->errorBag($this->errorBag)->redirectTo($this->getRedirectUrl());
    }
}
