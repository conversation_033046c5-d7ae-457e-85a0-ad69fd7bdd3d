<?php

namespace App\Http\Requests\Admin\Products;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class ProductDocRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
                return [
                    'specifications' => ['bail', 'required', 'mimes:pdf', 'max:500000'],
                    'tds' => ['bail', 'required', 'mimes:pdf', 'max:500000'],
                    'coa' => ['bail', 'required', 'mimes:pdf', 'max:500000'],
                ];
                break;
            case 'PUT':
                return [
                    'specifications' => ['bail', 'required', 'mimes:pdf', 'max:500000'],
                    'tds' => ['bail', 'required', 'mimes:pdf', 'max:500000'],
                    'coa' => ['bail', 'required', 'mimes:pdf', 'max:500000']
                ];
                break;
        }
    }

    /*public function filters()
    {
        return [
            'category_name' => 'trim|capitalize|escape'
        ];
    }*/

    protected function failedValidation(Validator $validator)
    {
        Session::flash('error', $validator->messages()->first());
        throw (new ValidationException($validator))->errorBag($this->errorBag)->redirectTo($this->getRedirectUrl());
    }
}
