<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FacebookLoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'provider' => 'required',
            'provider_id' => 'required',
            'provider_token' => 'required',

        ];
    }

    public function filters()
    {
        return [

            'provider_id' => 'escape',
            'provider_token' => 'escape',

        ];
    }

}
