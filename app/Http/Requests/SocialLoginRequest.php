<?php

namespace App\Http\Requests;

use App\Rules\AntiXssFinder;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class SocialLoginRequest extends FormRequest
{
    use ApiResponse;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email_id' => 'nullable|email|unique:user|max:150',
            'provider' => 'required|numeric',
            'provider_token' => 'required',
            'fcm_token' => ['bail', 'required', new AntiXssFinder()],
            'device_id' => ['bail', 'required'],
        ];
    }

    public function filters()
    {
        return [
            'email_id' => 'trim|lowercase|escape',
            'provider' => 'escape',
            'provider_id' => 'escape',
            'provider_token' => 'escape',

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $this->setMeta("status", AppConstant::STATUS_FAIL);
        $this->setMeta("message", $validator->messages()->first());
        throw new HttpResponseException(response()->json($this->setResponse(), JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

}
