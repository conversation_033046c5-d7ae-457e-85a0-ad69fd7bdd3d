<?php

namespace App\Http\Requests\Api\Guest;

use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;


class GuestRequest extends FormRequest
{
    use ApiResponse;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fcm_token' => ['required'],
            'device_id' => ['required'],
            'os' => ['required'],

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $this->setMeta("status", AppConstant::STATUS_FAIL);
        $this->setMeta("message", $validator->messages()->first());
        throw new HttpResponseException(response()->json($this->setResponse(), JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
