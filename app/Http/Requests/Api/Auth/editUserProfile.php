<?php

namespace App\Http\Requests\Api\Auth;

use App\Rules\AlphaSpace;
use App\Rules\AntiXssFinder;
use App\Traits\ApiResponse;
use App\Utils\AppConstant;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class editUserProfile extends FormRequest
{
    use ApiResponse;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fullname' => ['bail', 'required', 'min:3', 'max:100', new AntiXssFinder(), new AlphaSpace()],
            /*removing REQUIRED from [country_id, mobile_no and company_name due to app store policies.]*/
            'country_id' => ['nullable', 'min:1', new AntiXssFinder()],
            'mobile_no' => ['nullable', 'min:10', 'numeric', new AntiXssFinder()],
            'company_name' => ['nullable', 'min:3', 'max:100', new AntiXssFinder()],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $this->setMeta("status", AppConstant::STATUS_FAIL);
        $this->setMeta("message", $validator->messages()->first());
        throw new HttpResponseException(response()->json($this->setResponse(), JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
