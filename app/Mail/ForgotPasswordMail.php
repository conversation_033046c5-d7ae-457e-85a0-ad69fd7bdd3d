<?php

namespace App\Mail;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;

class ForgotPasswordMail extends Mailable
{
    use Queueable, SerializesModels;
    public $user, $uuid;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $uuid)
    {
        $this->user = $user;
        $this->uuid = $uuid;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.forgotPassword.forgotPassword')->with([
            'url' => Url::signedRoute('forgotPassword',['uuid' => $this->user->uuid, 'forgotPasswordCode' => $this->user->forgot_password_code], Carbon::now()->addHours(2)),
            'name' => $this->user->fullname,
        ])->subject('Reset Password');
    }
}
