<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class WebsiteFeedbackMail extends Mailable
{
    use Queueable, SerializesModels;
    public $userMessage;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($userMessage)
    {
        $this->userMessage = $userMessage;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.websiteFeedback.websiteFeedback')->with([
            'name' => $this->userMessage->name,
            'email' => $this->userMessage->email,
            'message' => $this->userMessage->message,
        ])->subject('Website Feedback | MB Lion Oleochemicals');
    }
}
