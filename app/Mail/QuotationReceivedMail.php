<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\QuotationModel;
use App\Utils\AppConstant;

class QuotationReceivedMail extends Mailable
{
    use Queueable, SerializesModels;
    public $quotation;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(QuotationModel $quotation)
    {
        $this->quotation = $quotation;
    }

    /**
     * Build the message.
     *
     * @return $this
     */

    public function build()
    {
        return $this->markdown('emails.quotation.quotationReceived')
                    ->with([
                        'email' => $this->quotation->email_id,
                        'product' => $this->quotation->product_name,
                        'destination_port' => $this->quotation->destination_port,
                        'country' => $this->quotation->country,
                        // 'trade_term' => $this->quotation->trade_term,
                        'message' => $this->quotation->message,
                        'quantity' => $this->quotation->quantity,
                        'unit' => optional($this->quotation->unit)->name,  // Safe access
                        // 'packing' => optional($this->quotation->packing)->packing_type, // Safe access
                        'user' => optional($this->quotation->userName)->fullname, // Safe access
                    ])
                    ->subject('New Quotation Received | MB Lion Oleochemicals');
    }

}
