<?php

namespace App\Utils;

class AppConstant
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const PAGINATE = 10;

    const PAGINATE_PRODUCT = 5;

    const STATUS_FAIL = 'fail';
    const STATUS_OK = 'ok';

    const MALE = 1;
    const FEMALE = 2;

//    const API_BASE_URL = 'http://192.168.1.28:8001/api/v1/';
    const API_BASE_URL = 'https://mblion.webmobtech.com/api/v1/';

    CONST ADMIN_GUARD = "admin";
    CONST USER_GUARD = "user";
    CONST ADMIN_MAIL = "<EMAIL>";
    const VERIFY_EMAIL = "mblion_oleochemicals_verifyEmail";
    const CLIENT_EMAIL = "<EMAIL>";

    // Application Config
    const PRIVACY_POLICY = 1;
    const TERMS_AND_CONDITION = 2;

    // Filter Status
    const ACTIVE = 1;
    const INACTIVE = 2;

    //API Status Codes
    const OK = 200;
    const CREATED = 201;
    const BAD_REQUEST = 400;
    const UNAUTHORIZED = 401;
    const FORBIDDEN = 403;
    const NOT_FOUND = 404;
    const METHOD_NOT_ALLOWED = 405;
    const UNPROCESSABLE_REQUEST = 422;
    const INTERNAL_SERVER_ERROR = 500;
    const TOKEN_INVALID = 503;

    /*social*/
    const FACEBOOK = 1;
    const GOOGLE = 2;
    const APPLE = 3;

    const OS_TYPE = ['android', 'ios'];

    const OS_ANDROID = "android";
    const OS_IOS = "ios";

    const PROVIDER_FACEBOOK = 'facebook';
    const PROVIDER_GOOGLE = 'google';
    const PROVIDER_APPLE = 'apple';

    /*document types*/
    const SPECIFICATIONS = 1;
    const TDS = 2;
    const COA = 3;

    // Notification Type
    const NOTIFICATION_ALL = 1;

    // FCM Topic Name
    const FCM_TOPIC_ALL = "mblionAllUser";

    const RECOVER_PASSWORD_QUEUE = 'RecoverPassword';

    const SEND_MESSAGE_NOTIFICATION_QUEUE = 'SendMessageNotification';
    const SEND_GENERAL_MESSAGE_NOTIFICATION_QUEUE = 'SendGeneralMessageNotification';


    const INITIAL_PRICE_DIFF = 0;

    const APP_QUEUE = "MasterQueue";

    const CSV = 1;
    const XLSX = 2;

    /*user filter*/
    const FILTER_ACTIVE = 1;
    const FILTER_INACTIVE = 2;

    const SINGLE_PRICE = 1;
    const DOUBLE_PRICE = 2;

    /*push notification type*/
    const PRICE_PUSH = 1;
    const MESSAGE_PUSH = 2;
    const DOWNSTREAM_RESP_ZERO = 0;

    const ADMIN_MAIN_EMAIL = ['<EMAIL>', '<EMAIL>'];

    const PRODUCT_PRICE_UPDATE_TITLE = 'Product Price Updated!';
}