<?php

namespace App\Notifications;

use App\Utils\AppConstant;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\AndroidConfig;
use NotificationChannels\Fcm\Resources\AndroidMessagePriority;
use NotificationChannels\Fcm\Resources\AndroidNotification;
use NotificationChannels\Fcm\Resources\ApnsConfig;
use NotificationChannels\Fcm\Resources\Notification as ResourcesNotification;

class ProductPriceUpdate extends Notification
{
    use Queueable;

    protected $notificationTitle;
    protected $productName;
    protected $productId;
    protected $productPrice;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($notificationTitle, $productName, $productId, $productPrice)
    {
        $this->notificationTitle = $notificationTitle;
        $this->productName = $productName;
        $this->productId = $productId;
        $this->productPrice = $productPrice;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [
            // 'database',
            FcmChannel::class
        ];
    }

    /**
     * Get the firebase representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return FcmMessage
     */
    public function toFcm($notifiable): FcmMessage
    {
        return FcmMessage::create()
            ->setData([
                'statusCode' => (string) AppConstant::STATUS_ACTIVE,
                'title' => $this->notificationTitle,
                'body' => $this->productName . 'Price Updated',
                'push_notification_type' => (string) AppConstant::PRICE_PUSH,
                'product_id' => (string) $this->productId,
                'click_action' => 'pushAction'
            ])
            ->setNotification(
                ResourcesNotification::create()
                    ->setTitle($this->notificationTitle)
                    ->setBody($this->productName)
            )
            ->setAndroid(
                AndroidConfig::create()
                    ->setNotification(
                        AndroidNotification::create()
                            ->setSound('default')
                    )
                    ->setPriority(AndroidMessagePriority::HIGH())
                    ->setTtl((60 * 20).'s')
            );
            // ->setApns(
            //     ApnsConfig::create()
            //         ->setHeaders([
            //             'apns-priority' => '10'
            //         ])
            // );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'notificationTitle' => $this->notificationTitle,
            'productName' => $this->productName,
            'productId' => $this->productId,
            'productPrice' => $this->productPrice
        ];
    }
}
