<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\AndroidConfig;
use NotificationChannels\Fcm\Resources\AndroidMessagePriority;
use NotificationChannels\Fcm\Resources\AndroidNotification;
use NotificationChannels\Fcm\Resources\ApnsConfig;
use NotificationChannels\Fcm\Resources\Notification as ResourcesNotification;

class MessageUserNotification extends Notification
{
    use Queueable;

    protected $notificationTitle;
    protected $notificationBody;
    protected $data;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($notificationTitle, $notificationBody, $data)
    {
        $this->notificationTitle = $notificationTitle;
        $this->notificationBody = $notificationBody;
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [
            FcmChannel::class
        ];
    }

    /**
     * Get the firebase representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return FcmMessage
     */
    public function toFcm($notifiable): FcmMessage
    {
        return FcmMessage::create()
            ->setData($this->data)
            ->setNotification(
                ResourcesNotification::create()
                    ->setTitle($this->notificationTitle)
                    ->setBody($this->notificationBody)
            )
            ->setAndroid(
                AndroidConfig::create()
                    ->setNotification(
                        AndroidNotification::create()
                            ->setSound('default')
                    )
                    ->setPriority(AndroidMessagePriority::HIGH())
                    ->setTtl((60 * 20).'s')
            );
            // ->setApns(
            //     ApnsConfig::create()
            //         ->setHeaders([
            //             'apns-priority' => '10'
            //         ])
            // );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'notificationTitle' => $this->notificationTitle,
            'notificationBody' => $this->notificationBody,
            'data' => $this->data
        ];
    }
}
