<?php

namespace App\Exports;

use App\Models\ProductModel;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ProductExport implements FromView
{
    public function __construct($id){
        $this->cat_id = $id;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function view(): View
    {
        $data['products'] = ProductModel::where('category_id', $this->cat_id)->get();
        return view('admin.exports.products')->with($data);
    }
}