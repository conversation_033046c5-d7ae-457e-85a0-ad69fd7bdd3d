<?php

namespace App\Exports;

use App\Models\UserModel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class UsersDataExport implements FromQuery, WithMapping, WithHeadings
{
    public function query()
    {
        return UserModel::query()->with('country');
    }

    public function headings(): array
    {
        return [
            'ID',
            'Full Name',
            'Email',
            'Mobile No',
            'Country',
            'status',
            'Created At',
            'Updated At',
        ];
    }

    public function map($user): array
    {
        return [
            $user->id,
            $user->fullname,
            $user->email_id,
            $user->mobile_no,
            optional($user->country)->country_name,
            $user->status,
            $user->created_at,
            $user->updated_at,
        ];
    }
}
