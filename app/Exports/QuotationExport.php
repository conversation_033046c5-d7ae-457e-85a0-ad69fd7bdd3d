<?php

namespace App\Exports;

use App\Models\QuotationModel;
use App\Utils\AppConstant;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class QuotationExport implements FromView
{
    /*protected $filter, $status;
    public function __construct($id, $status)
    {
        $this->id = $id;
        $this->status = $status;
    }*/
    /**
     * @return \Illuminate\Support\Collection
     */
    public function view(): View
    {

        $data['quotation'] = QuotationModel::with(['userName', 'packing', 'unit'])->where('status', AppConstant::STATUS_ACTIVE)->get();
//        dd($data);
        return view('admin.exports.quotation')->with($data);
    }
}
