<?php
namespace App\Exports;
use App\Models\UserModel;
use App\Utils\AppConstant;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class UsersExport implements FromView
{
    protected $filter, $status;
    public function __construct($filter, $status)
    {
        $this->filter = $filter;
        $this->status = $status;
    }

    public function view(): View
    {
        $data = UserModel::query();
        $data = $data->orderby('fullname', 'ASC');

        if ($this->status != "") {
            switch ($this->status) {
                case AppConstant::ACTIVE:
                    $data = $data->with('country')->where(['status' => AppConstant::STATUS_ACTIVE])->get();
                    break;
                case AppConstant::INACTIVE:
                    $data = $data->with('country')->where(['status' => AppConstant::STATUS_INACTIVE])->get();
                    break;
                default:
                    break;
            }
        }

        if ($this->filter != "") {
            $data = $data->with('country')->where(['country_id' => $this->filter])->get();
        }

//        dd($data);

        return view('admin.exports.users')->with($data);
    }
}