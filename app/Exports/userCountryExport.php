<?php

namespace App\Exports;

use App\Models\UserModel;
use App\Utils\AppConstant;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class userCountryExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $filter, $status;

    public function __construct($filter, $status)
    {
        $this->filter = $filter;
        $this->status = $status;
    }

    public function collection()
    {
        $data = UserModel::query();
        $data = $data->orderby('fullname', 'ASC');

        if ($this->status != "") {
            switch ($this->status) {
                case AppConstant::ACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_ACTIVE]);
                    break;
                case AppConstant::INACTIVE:
                    $data = $data->where(['status' => AppConstant::STATUS_INACTIVE]);
                    break;
                default:
                    break;
            }
        }

        if ($this->filter != "") {
            $data = $data->where(['country_id' => $this->filter]);
        }
        $userData = $data->select('fullname', 'email_id', 'country_id', 'mobile_no', 'company_name', 'created_at')->with('country')->get();
        foreach ($userData as $userDataVal) {
            $country = "";
            if($userDataVal->country != null) {
                $country = '(' . $userDataVal->country->country_code . ') ' . $userDataVal->country->country_name;
            }
            $userDataVal->country_id = $country;
        }
        return $userData;
//        if ($this->filter != "") {
//            $data = $data->where(['country_id' => $this->filter]);
//        }
//        return $data->select('fullname', 'email_id', 'mobile_no', 'company_name', 'created_at')->get();


    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Country',
            'Mobile',
            'Company',
            'Member Since'
        ];
    }
}