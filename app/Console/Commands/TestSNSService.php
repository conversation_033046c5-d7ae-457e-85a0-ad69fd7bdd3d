<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SNSNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestSNSService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sns:test {--user-id=} {--validate-all} {--cleanup-invalid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test SNS notification service and validate endpoints';

    protected $snsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(SNSNotificationService $snsService)
    {
        parent::__construct();
        $this->snsService = $snsService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('🚀 Starting SNS Service Test...');

        // Test AWS configuration
        $this->testAWSConfiguration();

        if ($this->option('validate-all')) {
            $this->validateAllEndpoints();
        }

        if ($this->option('cleanup-invalid')) {
            $this->cleanupInvalidEndpoints();
        }

        if ($this->option('user-id')) {
            $this->testUserNotification($this->option('user-id'));
        }

        $this->info('✅ SNS Service Test completed!');
    }

    private function testAWSConfiguration()
    {
        $this->info('🔧 Testing AWS Configuration...');

        $region = config('services.sns.region');
        $key = config('services.sns.key');
        $secret = config('services.sns.secret');
        $fcmArn = config('services.sns.fcm_application_arn');

        $this->line("Region: {$region}");
        $this->line("Access Key: " . (empty($key) ? '❌ Missing' : '✅ Present'));
        $this->line("Secret Key: " . (empty($secret) ? '❌ Missing' : '✅ Present'));
        $this->line("FCM ARN: " . (empty($fcmArn) ? '❌ Missing' : '✅ Present'));

        if (empty($key) || empty($secret) || empty($fcmArn)) {
            $this->error('❌ AWS configuration is incomplete!');
            return false;
        }

        $this->info('✅ AWS configuration looks good!');
        return true;
    }

    private function validateAllEndpoints()
    {
        $this->info('🔍 Validating all SNS endpoints...');

        $users = DB::table('user_access')
            ->whereNotNull('fcm_token')
            ->whereNotNull('sns_endpoint_arn')
            ->get();

        $validCount = 0;
        $invalidCount = 0;

        foreach ($users as $user) {
            try {
                $endpointArn = $this->snsService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
                
                if ($endpointArn) {
                    $validCount++;
                    $this->line("✅ User {$user->user_id}: Valid endpoint");
                } else {
                    $invalidCount++;
                    $this->line("❌ User {$user->user_id}: Invalid endpoint");
                }
            } catch (\Exception $e) {
                $invalidCount++;
                $this->line("❌ User {$user->user_id}: Error - " . $e->getMessage());
            }
        }

        $this->info("📊 Validation Results: {$validCount} valid, {$invalidCount} invalid");
    }

    private function cleanupInvalidEndpoints()
    {
        $this->info('🧹 Cleaning up invalid endpoints...');

        $users = DB::table('user_access')
            ->whereNotNull('fcm_token')
            ->get();

        $cleanedCount = 0;

        foreach ($users as $user) {
            try {
                // This will automatically clean up invalid endpoints and create new ones
                $endpointArn = $this->snsService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
                
                if ($endpointArn) {
                    $cleanedCount++;
                }
            } catch (\Exception $e) {
                $this->line("❌ Failed to cleanup user {$user->user_id}: " . $e->getMessage());
            }
        }

        $this->info("✅ Cleaned up {$cleanedCount} endpoints");
    }

    private function testUserNotification($userId)
    {
        $this->info("📱 Testing notification for user {$userId}...");

        try {
            $result = $this->snsService->sendPushNotification(
                $userId,
                'Test Notification',
                'This is a test notification from SNS service validation',
                'test-' . time(),
                'test-product'
            );

            if ($result && !empty($result)) {
                $this->info("✅ Notification sent successfully!");
                $this->line("Results: " . json_encode($result, JSON_PRETTY_PRINT));
            } else {
                $this->error("❌ Failed to send notification");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error sending notification: " . $e->getMessage());
        }
    }
}
