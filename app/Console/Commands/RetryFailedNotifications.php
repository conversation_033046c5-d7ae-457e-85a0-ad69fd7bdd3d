<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationMonitoringService;
use App\Services\SNSNotificationService;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RetryFailedNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:retry {--max-retries=3} {--hours=24} {--dry-run} {--user-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed notifications';

    protected $monitoringService;
    protected $snsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(NotificationMonitoringService $monitoringService, SNSNotificationService $snsService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
        $this->snsService = $snsService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $maxRetries = (int) $this->option('max-retries');
        $hours = (int) $this->option('hours');
        $dryRun = $this->option('dry-run');
        $userId = $this->option('user-id');

        $this->info('🔄 Starting Failed Notification Retry Process...');
        
        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No notifications will be sent');
        }

        // Get failed notifications that need retry
        $failedNotifications = $this->getFailedNotifications($hours, $maxRetries, $userId);

        if ($failedNotifications->isEmpty()) {
            $this->info('✅ No failed notifications found that need retry');
            return;
        }

        $this->info("Found {$failedNotifications->count()} failed notifications to retry");

        $retryCount = 0;
        $successCount = 0;
        $failureCount = 0;

        foreach ($failedNotifications as $notification) {
            $retryCount++;
            
            $this->line("Retrying notification {$retryCount}/{$failedNotifications->count()} for user {$notification->user_id}");

            if ($dryRun) {
                $this->line("  🔍 Would retry notification ID: {$notification->id}");
                continue;
            }

            try {
                $details = json_decode($notification->details, true) ?? [];
                
                // Extract notification details
                $title = $details['title'] ?? 'Retry Notification';
                $message = $details['message'] ?? 'This is a retry of a previously failed notification';
                $messageId = $details['messageId'] ?? 'retry-' . time();
                $productId = $details['productId'] ?? null;

                // Attempt to send the notification
                $result = $this->snsService->sendPushNotification(
                    $notification->user_id,
                    $title,
                    $message,
                    $messageId,
                    $productId
                );

                if ($result && !empty($result)) {
                    $successCount++;
                    $this->info("  ✅ Retry successful for user {$notification->user_id}");
                    
                    // Mark original notification as retried successfully
                    $this->markAsRetried($notification->id, 'success');
                } else {
                    $failureCount++;
                    $this->error("  ❌ Retry failed for user {$notification->user_id}");
                    
                    // Mark as retry failed
                    $this->markAsRetried($notification->id, 'failed');
                }

            } catch (\Exception $e) {
                $failureCount++;
                $this->error("  ❌ Exception during retry for user {$notification->user_id}: " . $e->getMessage());
                
                // Mark as retry failed with exception details
                $this->markAsRetried($notification->id, 'failed', [
                    'retry_error' => $e->getMessage(),
                    'retry_exception' => get_class($e)
                ]);
            }

            // Small delay between retries to avoid overwhelming the system
            usleep(100000); // 100ms
        }

        $this->info('');
        $this->info('📊 Retry Summary:');
        $this->line("Total processed: {$retryCount}");
        $this->line("Successful: {$successCount}");
        $this->line("Failed: {$failureCount}");
        
        if ($retryCount > 0) {
            $retrySuccessRate = round(($successCount / $retryCount) * 100, 2);
            $this->line("Retry success rate: {$retrySuccessRate}%");
        }

        $this->info('✅ Retry process completed!');
    }

    /**
     * Get failed notifications that need retry
     */
    private function getFailedNotifications($hours, $maxRetries, $userId = null)
    {
        $since = Carbon::now()->subHours($hours);
        
        $query = DB::table('notification_logs')
            ->select('id', 'user_id', 'details', 'retry_count', 'created_at')
            ->where('status', 'failed')
            ->where('created_at', '>=', $since)
            ->where('retry_count', '<', $maxRetries)
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                      ->from('notification_logs as retry_logs')
                      ->whereRaw('retry_logs.user_id = notification_logs.user_id')
                      ->where('retry_logs.status', 'success')
                      ->whereRaw('retry_logs.created_at > notification_logs.created_at')
                      ->whereRaw('retry_logs.created_at >= DATE_SUB(notification_logs.created_at, INTERVAL 1 HOUR)');
            })
            ->orderBy('created_at', 'desc');

        if ($userId) {
            $query->where('user_id', $userId);
        }

        return $query->get();
    }

    /**
     * Mark notification as retried
     */
    private function markAsRetried($notificationId, $status, $additionalDetails = [])
    {
        try {
            $notification = DB::table('notification_logs')->find($notificationId);
            if (!$notification) {
                return;
            }

            $details = json_decode($notification->details, true) ?? [];
            $details['retry_status'] = $status;
            $details['retry_at'] = now()->toISOString();
            
            if (!empty($additionalDetails)) {
                $details = array_merge($details, $additionalDetails);
            }

            DB::table('notification_logs')
                ->where('id', $notificationId)
                ->update([
                    'retry_count' => $notification->retry_count + 1,
                    'details' => json_encode($details),
                    'updated_at' => now()
                ]);

        } catch (\Exception $e) {
            $this->error("Failed to mark notification {$notificationId} as retried: " . $e->getMessage());
        }
    }
}
