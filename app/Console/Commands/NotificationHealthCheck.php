<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationMonitoringService;
use App\Services\SNSNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class NotificationHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:health-check {--fix} {--alert-threshold=95}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Perform comprehensive health check on notification system';

    protected $monitoringService;
    protected $snsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(NotificationMonitoringService $monitoringService, SNSNotificationService $snsService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
        $this->snsService = $snsService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fix = $this->option('fix');
        $alertThreshold = (float) $this->option('alert-threshold');

        $this->info('🏥 Notification System Health Check');
        $this->info('═══════════════════════════════════════');
        $this->line('');

        $issues = [];
        $warnings = [];

        // Check 1: AWS SNS Connection
        $this->checkAWSConnection($issues, $warnings, $fix);

        // Check 2: Database Connection
        $this->checkDatabaseConnection($issues, $warnings);

        // Check 3: Success Rates
        $this->checkSuccessRates($issues, $warnings, $alertThreshold);

        // Check 4: FCM Token Health
        $this->checkFCMTokenHealth($issues, $warnings, $fix);

        // Check 5: Endpoint Health
        $this->checkEndpointHealth($issues, $warnings, $fix);

        // Check 6: System Performance
        $this->checkSystemPerformance($issues, $warnings);

        // Check 7: Recent Activity
        $this->checkRecentActivity($issues, $warnings);

        // Summary
        $this->displaySummary($issues, $warnings);

        return empty($issues) ? 0 : 1;
    }

    private function checkAWSConnection(&$issues, &$warnings, $fix)
    {
        $this->info('🔗 Checking AWS SNS Connection...');

        try {
            // Check if in mock mode
            if ($this->snsService->isMockMode()) {
                $warnings[] = 'AWS SNS is running in mock mode - real notifications will not be sent';
                $this->warn('  ⚠️  Running in mock mode');
            } else {
                $this->info('  ✅ AWS SNS connection active');
            }
        } catch (\Exception $e) {
            $issues[] = 'AWS SNS connection failed: ' . $e->getMessage();
            $this->error('  ❌ AWS SNS connection failed');
        }
    }

    private function checkDatabaseConnection(&$issues, &$warnings)
    {
        $this->info('🗄️  Checking Database Connection...');

        try {
            DB::connection()->getPdo();
            $this->info('  ✅ Database connection active');

            // Check if notification_logs table exists
            if (DB::getSchemaBuilder()->hasTable('notification_logs')) {
                $this->info('  ✅ Notification logs table exists');
            } else {
                $issues[] = 'notification_logs table does not exist';
                $this->error('  ❌ notification_logs table missing');
            }
        } catch (\Exception $e) {
            $issues[] = 'Database connection failed: ' . $e->getMessage();
            $this->error('  ❌ Database connection failed');
        }
    }

    private function checkSuccessRates(&$issues, &$warnings, $threshold)
    {
        $this->info('📈 Checking Success Rates...');

        $rate24h = $this->monitoringService->getSuccessRate(24);
        $rate1h = $this->monitoringService->getSuccessRate(1);

        if ($rate24h < $threshold) {
            if ($rate24h < 90) {
                $issues[] = "24h success rate too low: {$rate24h}%";
                $this->error("  ❌ 24h success rate: {$rate24h}% (below {$threshold}%)");
            } else {
                $warnings[] = "24h success rate below threshold: {$rate24h}%";
                $this->warn("  ⚠️  24h success rate: {$rate24h}% (below {$threshold}%)");
            }
        } else {
            $this->info("  ✅ 24h success rate: {$rate24h}%");
        }

        if ($rate1h < $threshold && $rate1h > 0) {
            $warnings[] = "1h success rate below threshold: {$rate1h}%";
            $this->warn("  ⚠️  1h success rate: {$rate1h}% (below {$threshold}%)");
        } else if ($rate1h > 0) {
            $this->info("  ✅ 1h success rate: {$rate1h}%");
        } else {
            $this->line("  ℹ️  No notifications in the last hour");
        }
    }

    private function checkFCMTokenHealth(&$issues, &$warnings, $fix)
    {
        $this->info('📱 Checking FCM Token Health...');

        $totalTokens = DB::table('user_access')->whereNotNull('fcm_token')->count();
        $validTokens = DB::table('user_access')
            ->whereNotNull('fcm_token')
            ->where('fcm_token', '!=', '')
            ->whereRaw('LENGTH(fcm_token) >= 100')
            ->count();

        $invalidTokens = $totalTokens - $validTokens;

        $this->line("  Total tokens: {$totalTokens}");
        $this->line("  Valid tokens: {$validTokens}");

        if ($invalidTokens > 0) {
            $warnings[] = "{$invalidTokens} invalid FCM tokens found";
            $this->warn("  ⚠️  Invalid tokens: {$invalidTokens}");

            if ($fix) {
                $this->line("  🔧 Cleaning up invalid tokens...");
                $cleaned = DB::table('user_access')
                    ->where(function ($query) {
                        $query->whereNull('fcm_token')
                            ->orWhere('fcm_token', '')
                            ->orWhereRaw('LENGTH(fcm_token) < 100');
                    })
                    ->update(['fcm_token' => null, 'sns_endpoint_arn' => null]);
                $this->info("  ✅ Cleaned {$cleaned} invalid tokens");
            }
        } else {
            $this->info("  ✅ All FCM tokens are valid");
        }
    }

    private function checkEndpointHealth(&$issues, &$warnings, $fix)
    {
        $this->info('🎯 Checking SNS Endpoint Health...');

        $totalEndpoints = DB::table('user_access')->whereNotNull('sns_endpoint_arn')->count();
        $orphanedEndpoints = DB::table('user_access')
            ->whereNotNull('sns_endpoint_arn')
            ->where(function ($query) {
                $query->whereNull('fcm_token')->orWhere('fcm_token', '');
            })
            ->count();

        $this->line("  Total endpoints: {$totalEndpoints}");

        if ($orphanedEndpoints > 0) {
            $warnings[] = "{$orphanedEndpoints} orphaned endpoints found";
            $this->warn("  ⚠️  Orphaned endpoints: {$orphanedEndpoints}");

            if ($fix) {
                $this->line("  🔧 Cleaning up orphaned endpoints...");
                $cleaned = DB::table('user_access')
                    ->whereNotNull('sns_endpoint_arn')
                    ->where(function ($query) {
                        $query->whereNull('fcm_token')->orWhere('fcm_token', '');
                    })
                    ->update(['sns_endpoint_arn' => null]);
                $this->info("  ✅ Cleaned {$cleaned} orphaned endpoints");
            }
        } else {
            $this->info("  ✅ No orphaned endpoints found");
        }
    }

    private function checkSystemPerformance(&$issues, &$warnings)
    {
        $this->info('⚡ Checking System Performance...');

        // Check average processing time from recent logs
        $avgTime = DB::table('notification_logs')
            ->where('created_at', '>=', Carbon::now()->subHours(24))
            ->where('status', 'success')
            ->whereRaw("JSON_EXTRACT(details, '$.processingTime') IS NOT NULL")
            ->selectRaw('AVG(CAST(REPLACE(JSON_EXTRACT(details, "$.processingTime"), "ms", "") AS DECIMAL(10,2))) as avg_time')
            ->value('avg_time');

        if ($avgTime) {
            $avgTime = round($avgTime, 2);
            if ($avgTime > 5000) { // 5 seconds
                $issues[] = "High average processing time: {$avgTime}ms";
                $this->error("  ❌ Average processing time: {$avgTime}ms (too high)");
            } else if ($avgTime > 2000) { // 2 seconds
                $warnings[] = "Elevated average processing time: {$avgTime}ms";
                $this->warn("  ⚠️  Average processing time: {$avgTime}ms");
            } else {
                $this->info("  ✅ Average processing time: {$avgTime}ms");
            }
        } else {
            $this->line("  ℹ️  No performance data available");
        }
    }

    private function checkRecentActivity(&$issues, &$warnings)
    {
        $this->info('🔄 Checking Recent Activity...');

        $recentActivity = DB::table('notification_logs')
            ->where('created_at', '>=', Carbon::now()->subHours(1))
            ->count();

        $this->line("  Notifications in last hour: {$recentActivity}");

        if ($recentActivity === 0) {
            $warnings[] = 'No notification activity in the last hour';
            $this->warn('  ⚠️  No recent activity');
        } else {
            $this->info('  ✅ System is active');
        }
    }

    private function displaySummary($issues, $warnings)
    {
        $this->line('');
        $this->info('📋 HEALTH CHECK SUMMARY');
        $this->info('═══════════════════════');

        if (empty($issues) && empty($warnings)) {
            $this->info('🎉 All systems are healthy!');
        } else {
            if (!empty($issues)) {
                $this->error('❌ CRITICAL ISSUES:');
                foreach ($issues as $issue) {
                    $this->line("  • {$issue}");
                }
                $this->line('');
            }

            if (!empty($warnings)) {
                $this->warn('⚠️  WARNINGS:');
                foreach ($warnings as $warning) {
                    $this->line("  • {$warning}");
                }
                $this->line('');
            }

            $this->line('💡 Run with --fix to automatically resolve some issues');
        }

        $this->line('');
        $this->info('Health check completed at ' . now()->format('Y-m-d H:i:s'));
    }
}
