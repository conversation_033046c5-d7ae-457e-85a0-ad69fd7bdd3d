<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationMonitoringService;
use App\Services\SNSNotificationService;

class NotificationDashboard extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:dashboard {--refresh=5} {--test-user=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display real-time notification monitoring dashboard';

    protected $monitoringService;
    protected $snsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(NotificationMonitoringService $monitoringService, SNSNotificationService $snsService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
        $this->snsService = $snsService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $refreshInterval = (int) $this->option('refresh');
        $testUser = $this->option('test-user');

        if ($testUser) {
            $this->runTestNotification($testUser);
            return;
        }

        $this->info('🚀 Notification Monitoring Dashboard');
        $this->info('Press Ctrl+C to exit');
        $this->line('');

        while (true) {
            // Clear screen (works on most terminals)
            system('clear');
            
            $this->displayHeader();
            $this->displayStats();
            $this->displayRecentFailures();
            $this->displaySystemHealth();
            
            $this->line('');
            $this->info("🔄 Refreshing every {$refreshInterval} seconds... (Press Ctrl+C to exit)");
            
            sleep($refreshInterval);
        }
    }

    private function displayHeader()
    {
        $this->info('📊 NOTIFICATION MONITORING DASHBOARD');
        $this->info('═══════════════════════════════════════');
        $this->line('Last updated: ' . now()->format('Y-m-d H:i:s'));
        $this->line('');
    }

    private function displayStats()
    {
        $stats = $this->monitoringService->getDashboardStats();
        
        $this->info('📈 SUCCESS RATES');
        $this->line('─────────────────');
        
        // Color code success rates
        $rate24h = $stats['success_rate_24h'];
        $rate1h = $stats['success_rate_1h'];
        
        $color24h = $rate24h >= 95 ? 'info' : ($rate24h >= 90 ? 'comment' : 'error');
        $color1h = $rate1h >= 95 ? 'info' : ($rate1h >= 90 ? 'comment' : 'error');
        
        $this->line("Last 24 hours: <{$color24h}>{$rate24h}%</{$color24h}>");
        $this->line("Last 1 hour:   <{$color1h}>{$rate1h}%</{$color1h}>");
        
        $this->line('');
        
        $this->info('📊 CURRENT HOUR METRICS');
        $this->line('─────────────────────────');
        $current = $stats['current_hour'];
        $this->line("Total:    {$current['total']}");
        $this->line("Success:  {$current['success']}");
        $this->line("Failed:   {$current['failed']}");
        
        if ($current['total'] > 0) {
            $currentRate = round(($current['success'] / $current['total']) * 100, 2);
            $currentColor = $currentRate >= 95 ? 'info' : ($currentRate >= 90 ? 'comment' : 'error');
            $this->line("Rate:     <{$currentColor}>{$currentRate}%</{$currentColor}>");
        }
        
        $this->line('');
        
        $this->info('👥 SYSTEM OVERVIEW');
        $this->line('────────────────────');
        $this->line("Users with tokens:  {$stats['total_users_with_tokens']}");
        $this->line("Active endpoints:   {$stats['total_endpoints']}");
        $this->line('');
    }

    private function displayRecentFailures()
    {
        $failures = $this->monitoringService->getFailureStats(24);
        
        $this->info('❌ TOP FAILURES (Last 24h)');
        $this->line('─────────────────────────────');
        
        if ($failures->isEmpty()) {
            $this->line('<info>No failures recorded! 🎉</info>');
        } else {
            foreach ($failures->take(5) as $failure) {
                $this->line("• {$failure['error']} ({$failure['count']}x)");
            }
        }
        
        $this->line('');
    }

    private function displaySystemHealth()
    {
        $this->info('🏥 SYSTEM HEALTH');
        $this->line('──────────────────');
        
        // Check AWS connection
        try {
            $awsStatus = $this->snsService->testConnection() ? '✅ Connected' : '❌ Disconnected';
        } catch (\Exception $e) {
            $awsStatus = '⚠️  Mock Mode';
        }
        
        $this->line("AWS SNS:     {$awsStatus}");
        
        // Check database
        try {
            \DB::connection()->getPdo();
            $dbStatus = '✅ Connected';
        } catch (\Exception $e) {
            $dbStatus = '❌ Disconnected';
        }
        
        $this->line("Database:    {$dbStatus}");
        
        // Check recent activity
        $recentActivity = \DB::table('notification_logs')
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();
            
        $activityStatus = $recentActivity > 0 ? "✅ Active ({$recentActivity} in last 5min)" : '⚠️  No recent activity';
        $this->line("Activity:    {$activityStatus}");
        
        $this->line('');
    }

    private function runTestNotification($userId)
    {
        $this->info("🧪 Testing notification for user {$userId}...");
        
        try {
            $result = $this->snsService->sendPushNotification(
                $userId,
                'Test Notification',
                'This is a test notification from the monitoring dashboard',
                'dashboard-test-' . time(),
                'test-product'
            );

            if ($result && !empty($result)) {
                $this->info("✅ Test notification sent successfully!");
                $this->line("Results: " . json_encode($result, JSON_PRETTY_PRINT));
                
                // Show recent stats for this user
                $this->line('');
                $this->info('📊 Recent stats for this user:');
                
                $userStats = \DB::table('notification_logs')
                    ->where('user_id', $userId)
                    ->where('created_at', '>=', now()->subHours(24))
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->get();
                    
                foreach ($userStats as $stat) {
                    $this->line("  {$stat->status}: {$stat->count}");
                }
            } else {
                $this->error("❌ Test notification failed");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error sending test notification: " . $e->getMessage());
        }
    }
}
