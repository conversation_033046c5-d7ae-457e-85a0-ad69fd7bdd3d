<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckSnsStatusCommand extends Command
{
    protected $signature = 'sns:status';
    protected $description = 'Check SNS endpoint status';

    public function handle()
    {
        $totalUsers = DB::table('user_access')->whereNotNull('fcm_token')->count();
        $usersWithEndpoints = DB::table('user_access')->whereNotNull('sns_endpoint_arn')->count();

        $this->info("📊 SNS Endpoint Status:");
        $this->info("Total users with FCM tokens: {$totalUsers}");
        $this->info("Users with SNS endpoints: {$usersWithEndpoints}");
        $this->info("Coverage: " . round(($usersWithEndpoints / $totalUsers) * 100, 2) . "%");
    }
}