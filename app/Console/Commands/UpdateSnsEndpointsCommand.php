<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SNSNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateSnsEndpointsCommand extends Command
{
    protected $signature = 'sns:update-endpoints';
    protected $description = 'Update SNS endpoints for all users with FCM tokens';

    protected $snsNotificationService;

    public function __construct(SNSNotificationService $snsNotificationService)
    {
        parent::__construct();
        $this->snsNotificationService = $snsNotificationService;
    }

    public function handle()
    {
        try {
            Log::channel('stack_sns')->info("🔄 Starting SNS endpoint updates...");

            // Increase execution limits for large batch processing
            set_time_limit(0);
            ini_set('memory_limit', '512M');

            $startTime = microtime(true);
            $chunkSize = 500;
            $totalProcessed = 0;
            $successCount = 0;
            $errorCount = 0;

            Log::channel('stack_sns')->info("📋 Starting scheduled SNS endpoint updates");

            DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->select('user_id', 'fcm_token', 'sns_endpoint_arn')
                ->orderBy('user_id')
                ->chunk($chunkSize, function ($users) use (&$totalProcessed, &$successCount, &$errorCount) {
                    foreach ($users as $user) {
                        try {
                            $result = $this->snsNotificationService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
                            if ($result) {
                                $successCount++;
                            } else {
                                $errorCount++;
                            }
                            $totalProcessed++;
                        } catch (\Exception $e) {
                            $errorCount++;
                            $totalProcessed++;
                            Log::channel('stack_sns')->error("❌ SNS Endpoint update failed for user {$user->user_id}: " . $e->getMessage());
                        }
                    }

                    Log::channel('stack_sns')->info("🔄 Processed {$totalProcessed} users (Success: {$successCount}, Errors: {$errorCount})");

                    // Small delay to prevent overwhelming AWS API
                    usleep(100000); // 0.1 second delay between chunks
                });

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::channel('stack_sns')->info("✅ SNS endpoint updates completed!");
            Log::channel('stack_sns')->info("📊 Total: {$totalProcessed}, Success: {$successCount}, Errors: {$errorCount}");
            Log::channel('stack_sns')->info("⏱️  Execution time: {$executionTime} seconds");

            Log::channel('stack_sns')->info("✅ Scheduled SNS endpoint updates completed", [
                'total_processed' => $totalProcessed,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'execution_time' => $executionTime
            ]);
        } catch (\Exception $e) {
            Log::channel('stack_sns')->error("💥 Fatal error during SNS update job: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            $this->error("❌ An unexpected error occurred. Check the log file for details.");
        }
    }

}