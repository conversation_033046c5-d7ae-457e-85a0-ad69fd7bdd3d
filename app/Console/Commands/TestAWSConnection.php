<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Illuminate\Support\Facades\Log;

class TestAWSConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aws:test-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test AWS SNS connection and credentials';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('🔧 Testing AWS SNS Connection...');

        // Test configuration
        $this->testConfiguration();

        // Test SNS client creation
        $snsClient = $this->createSnsClient();
        if (!$snsClient) {
            return;
        }

        // Test basic SNS operations
        $this->testSnsOperations($snsClient);
    }

    private function testConfiguration()
    {
        $this->info('📋 Checking Configuration...');

        $config = [
            'Region' => config('services.sns.region'),
            'Access Key' => config('services.sns.key'),
            'Secret Key' => config('services.sns.secret'),
            'FCM Application ARN' => config('services.sns.fcm_application_arn'),
            'General Topic ARN' => config('services.sns.general_topic_arn'),
        ];

        foreach ($config as $key => $value) {
            $status = empty($value) ? '❌ Missing' : '✅ Present';
            $displayValue = $key === 'Secret Key' ? (empty($value) ? 'Missing' : '***Hidden***') : $value;
            $this->line("{$key}: {$displayValue} {$status}");
        }

        // Check if all required configs are present
        $missing = array_filter($config, function($value) {
            return empty($value);
        });

        if (!empty($missing)) {
            $this->error('❌ Missing required configuration: ' . implode(', ', array_keys($missing)));
            return false;
        }

        $this->info('✅ Configuration looks complete');
        return true;
    }

    private function createSnsClient()
    {
        $this->info('🔗 Creating SNS Client...');

        try {
            $snsClient = new SnsClient([
                'version' => 'latest',
                'region' => config('services.sns.region'),
                'credentials' => [
                    'key'    => config('services.sns.key'),
                    'secret' => config('services.sns.secret'),
                ],
            ]);

            $this->info('✅ SNS Client created successfully');
            return $snsClient;
        } catch (\Exception $e) {
            $this->error('❌ Failed to create SNS Client: ' . $e->getMessage());
            return null;
        }
    }

    private function testSnsOperations($snsClient)
    {
        $this->info('🧪 Testing SNS Operations...');

        // Test 1: List topics (basic connectivity test)
        $this->testListTopics($snsClient);

        // Test 2: Get platform application attributes
        $this->testPlatformApplication($snsClient);

        // Test 3: Test topic existence
        $this->testTopicExistence($snsClient);
    }

    private function testListTopics($snsClient)
    {
        $this->line('📋 Testing List Topics...');

        try {
            $result = $snsClient->listTopics();
            $topicCount = count($result['Topics'] ?? []);
            $this->info("✅ Successfully listed {$topicCount} topics");
            
            // Show first few topics
            if ($topicCount > 0) {
                $this->line('First few topics:');
                foreach (array_slice($result['Topics'], 0, 3) as $topic) {
                    $this->line('  - ' . $topic['TopicArn']);
                }
            }
        } catch (AwsException $e) {
            $this->error('❌ Failed to list topics: ' . $e->getAwsErrorCode() . ' - ' . $e->getMessage());
        }
    }

    private function testPlatformApplication($snsClient)
    {
        $this->line('📱 Testing Platform Application...');

        $platformArn = config('services.sns.fcm_application_arn');
        
        try {
            $result = $snsClient->getPlatformApplicationAttributes([
                'PlatformApplicationArn' => $platformArn
            ]);

            $this->info('✅ Platform application is accessible');
            $this->line('Platform Application Details:');
            foreach ($result['Attributes'] as $key => $value) {
                $displayValue = in_array($key, ['GCMApiKey']) ? '***Hidden***' : $value;
                $this->line("  {$key}: {$displayValue}");
            }
        } catch (AwsException $e) {
            $this->error('❌ Failed to access platform application: ' . $e->getAwsErrorCode() . ' - ' . $e->getMessage());
            
            if ($e->getAwsErrorCode() === 'NotFound') {
                $this->warn('💡 The platform application ARN might be incorrect or the application might not exist');
            }
        }
    }

    private function testTopicExistence($snsClient)
    {
        $this->line('📢 Testing Topic Existence...');

        $topicArn = config('services.sns.general_topic_arn');
        
        try {
            $result = $snsClient->getTopicAttributes([
                'TopicArn' => $topicArn
            ]);

            $this->info('✅ Topic is accessible');
            $this->line('Topic Details:');
            foreach ($result['Attributes'] as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
        } catch (AwsException $e) {
            $this->error('❌ Failed to access topic: ' . $e->getAwsErrorCode() . ' - ' . $e->getMessage());
            
            if ($e->getAwsErrorCode() === 'NotFound') {
                $this->warn('💡 The topic ARN might be incorrect or the topic might not exist');
            }
        }
    }
}
