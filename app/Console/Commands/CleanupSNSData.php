<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupSNSData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sns:cleanup {--dry-run} {--remove-duplicates} {--remove-invalid-tokens}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup SNS data and remove invalid tokens';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('🧹 Starting SNS Data Cleanup...');

        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        // Show current statistics
        $this->showStatistics();

        if ($this->option('remove-duplicates')) {
            $this->removeDuplicateTokens($dryRun);
        }

        if ($this->option('remove-invalid-tokens')) {
            $this->removeInvalidTokens($dryRun);
        }

        // Clean up null/empty FCM tokens
        $this->cleanupEmptyTokens($dryRun);

        // Show final statistics
        $this->info('📊 Final Statistics:');
        $this->showStatistics();

        $this->info('✅ SNS Data Cleanup completed!');
    }

    private function showStatistics()
    {
        $totalUsers = DB::table('user_access')->count();
        $usersWithTokens = DB::table('user_access')->whereNotNull('fcm_token')->where('fcm_token', '!=', '')->count();
        $usersWithEndpoints = DB::table('user_access')->whereNotNull('sns_endpoint_arn')->count();
        $duplicateTokens = DB::table('user_access')
            ->select('fcm_token')
            ->whereNotNull('fcm_token')
            ->where('fcm_token', '!=', '')
            ->groupBy('fcm_token')
            ->havingRaw('COUNT(*) > 1')
            ->count();

        $this->table(['Metric', 'Count'], [
            ['Total user access records', $totalUsers],
            ['Records with FCM tokens', $usersWithTokens],
            ['Records with SNS endpoints', $usersWithEndpoints],
            ['Duplicate FCM tokens', $duplicateTokens],
        ]);
    }

    private function removeDuplicateTokens($dryRun)
    {
        $this->info('🔍 Finding duplicate FCM tokens...');

        $duplicates = DB::table('user_access')
            ->select('fcm_token', DB::raw('COUNT(*) as count'), DB::raw('MIN(id) as keep_id'))
            ->whereNotNull('fcm_token')
            ->where('fcm_token', '!=', '')
            ->groupBy('fcm_token')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $totalDuplicates = 0;

        foreach ($duplicates as $duplicate) {
            $duplicateIds = DB::table('user_access')
                ->where('fcm_token', $duplicate->fcm_token)
                ->where('id', '!=', $duplicate->keep_id)
                ->pluck('id');

            $totalDuplicates += $duplicateIds->count();

            $this->line("Token: " . substr($duplicate->fcm_token, 0, 20) . "... has {$duplicate->count} duplicates");

            if (!$dryRun) {
                DB::table('user_access')->whereIn('id', $duplicateIds)->delete();
                $this->line("  ✅ Removed {$duplicateIds->count()} duplicate records");
            } else {
                $this->line("  🔍 Would remove {$duplicateIds->count()} duplicate records");
            }
        }

        if ($totalDuplicates > 0) {
            $action = $dryRun ? 'Would remove' : 'Removed';
            $this->info("{$action} {$totalDuplicates} duplicate token records");
        } else {
            $this->info('✅ No duplicate tokens found');
        }
    }

    private function removeInvalidTokens($dryRun)
    {
        $this->info('🔍 Finding invalid FCM tokens...');

        // Find tokens that are too short (valid FCM tokens are typically 152+ characters)
        $shortTokens = DB::table('user_access')
            ->whereNotNull('fcm_token')
            ->where('fcm_token', '!=', '')
            ->whereRaw('LENGTH(fcm_token) < 100')
            ->count();

        // Find tokens with invalid characters
        $invalidChars = DB::table('user_access')
            ->whereNotNull('fcm_token')
            ->where('fcm_token', '!=', '')
            ->whereRaw("fcm_token REGEXP '[^a-zA-Z0-9_:-]'")
            ->count();

        $totalInvalid = $shortTokens + $invalidChars;

        if ($totalInvalid > 0) {
            $this->line("Found {$shortTokens} tokens that are too short");
            $this->line("Found {$invalidChars} tokens with invalid characters");

            if (!$dryRun) {
                // Clear invalid tokens
                DB::table('user_access')
                    ->whereNotNull('fcm_token')
                    ->where('fcm_token', '!=', '')
                    ->where(function($query) {
                        $query->whereRaw('LENGTH(fcm_token) < 100')
                              ->orWhereRaw("fcm_token REGEXP '[^a-zA-Z0-9_:-]'");
                    })
                    ->update([
                        'fcm_token' => null,
                        'sns_endpoint_arn' => null
                    ]);

                $this->info("✅ Cleared {$totalInvalid} invalid tokens");
            } else {
                $this->info("🔍 Would clear {$totalInvalid} invalid tokens");
            }
        } else {
            $this->info('✅ No invalid tokens found');
        }
    }

    private function cleanupEmptyTokens($dryRun)
    {
        $this->info('🔍 Cleaning up empty/null tokens...');

        $emptyTokens = DB::table('user_access')
            ->where(function($query) {
                $query->whereNull('fcm_token')
                      ->orWhere('fcm_token', '');
            })
            ->whereNotNull('sns_endpoint_arn')
            ->count();

        if ($emptyTokens > 0) {
            if (!$dryRun) {
                DB::table('user_access')
                    ->where(function($query) {
                        $query->whereNull('fcm_token')
                              ->orWhere('fcm_token', '');
                    })
                    ->update(['sns_endpoint_arn' => null]);

                $this->info("✅ Cleared {$emptyTokens} orphaned endpoint ARNs");
            } else {
                $this->info("🔍 Would clear {$emptyTokens} orphaned endpoint ARNs");
            }
        } else {
            $this->info('✅ No orphaned endpoint ARNs found');
        }
    }
}
