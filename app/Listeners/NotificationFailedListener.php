<?php

namespace App\Listeners;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;

class NotificationFailedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        if(get_class($event->notification) == 'App\Notifications\ProductPriceUpdate' && isset($event->data['message']) && Str::contains($event->data['message'], 'not a valid FCM')) {
            if($event->data['token']) {
                $this->saveFailedFcmNotification($event->data['token'], $event->notifiable);
            } else {
                logger("Error At NotificationFailedListener: ".$event->data['message']);
            }
        } else {
            logger("Error At NotificationFailedListener outside fcm : ".$event->data['message']);
        }
    }

    private function saveFailedFcmNotification($token, $notifiable) {
        // other logic ...
        $notifiable->usersAccessMany()->where('fcm_token', $token)->delete();
    }
}
