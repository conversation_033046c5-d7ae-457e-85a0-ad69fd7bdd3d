<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductDocumentsModel extends Model
{
    protected $table = "product_documents";

    protected $fillable = [
        'product_id', 'document_type', 'document_path'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function getDocumentPathAttribute($value)
    {
        $doc = $this->attributes['document_path'];
        if ($doc != "") {
            return url('storage/' . $doc);
        } else {
            return null;
        }
    }
}
