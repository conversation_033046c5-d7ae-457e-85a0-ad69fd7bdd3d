<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class ProductFavouriteModel extends Model
{
    protected $table = "product_favourite";

    protected $fillable = [
        'product_id', 'user_id'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }

    public function product()
    {
        return $this->hasOne(ProductModel::class, 'id', 'product_id');
    }

    public function usersAccess()
    {
        return $this->hasOne(UserAccess::class, 'user_id', 'user_id');
    }

    public function usersAccessMany()
    {
        return $this->hasMany(UserAccess::class, 'user_id', 'user_id');
    }

    public function user_notification(){
        return $this->hasMany(UserNotificationsModel::class,'user_id','user_id');
    }
}
