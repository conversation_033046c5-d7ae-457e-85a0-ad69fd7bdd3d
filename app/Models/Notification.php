<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    protected $table = "notifications";

    protected $fillable = [
        'message', 'message_type', 'min_price', 'max_price'
    ];

    protected $hidden = [
        'user_id', 'product_id', 'is_read', 'status','updated_at'
    ];

    public function getCreatedAtAttribute($value)
    {
        $createdAt = $this->attributes['created_at'];
        return Carbon::parse($createdAt)->getTimestamp();
    }

    public function user(){
        return $this->hasOne(UserModel::class, 'id', 'user_id');
    }

    public function product(){
        return $this->hasOne(ProductModel::class, 'id', 'product_id');
    }

}
