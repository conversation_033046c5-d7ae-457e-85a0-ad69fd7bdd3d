<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PackingModel extends Model
{
    protected $table = "packing";

    protected $fillable = [
        'packing_type', 'image_path'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function getImagePathAttribute($value)
    {
        $image = $this->attributes['image_path'];
        if ($image != "") {
            return asset('/assets/images/packing/' . $image);
        } else {
            return null;
        }
    }
}
