<?php

namespace App\Models;

use App\Utils\AppConstant;
use Illuminate\Database\Eloquent\Model;

class ProductPriceModel extends Model
{
    protected $table = "product_price";

    protected $fillable = [
        'min_price', 'max_price', 'price_diff', 'status', 'product_id'
    ];

    protected $hidden = [
        'updated_at'
    ];

    public function productName(){
        return $this->hasOne(ProductModel::class, 'id', 'product_id');
    }

    public function scopeActivePrice($query){
        return $query->where('status', AppConstant::STATUS_ACTIVE);
    }
}
