<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductLoadingPortModel extends Model
{
    protected $table = "product_loading_port";

    protected $fillable = [
        'country_id', 'product_id'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function country(){
        return $this->hasOne(CountryModel::class, 'id', 'country_id');
    }

    /*public function product(){
        return $this->hasOne(ProductModel::class, 'id', 'product_id');
    }*/

}
