<?php

namespace App\Models;

use App\Utils\AppConstant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;

class AdminModel extends Authenticatable
{
    use Notifiable;

    protected $table = "admin";

    protected $guard_name = AppConstant::ADMIN_GUARD;

    protected $fillable = [
        'uuid', 'name', 'email_id', 'password', 'status'
    ];

    protected $hidden = [
        'id', 'created_at', 'updated_at'
    ];
}
