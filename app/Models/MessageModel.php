<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class MessageModel extends Model
{
    protected $table = "message";

    protected $fillable = [
        'title', 'body', 'created_at'
    ];

    protected $hidden = [
        'status', 'updated_at'
    ];


    public function getCreatedAtAttribute($value)
    {
        $createdAt = $this->attributes['created_at'];
        return Carbon::parse($createdAt)->getTimestamp();
    }

    public function category(){
        return $this->hasOne(CategoryModel::class, 'id', 'category_id');
    }
}
