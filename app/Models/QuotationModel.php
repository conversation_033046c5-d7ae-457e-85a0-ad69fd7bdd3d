<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class QuotationModel extends Model
{
    protected $table = "cif_quotation";

    protected $fillable = [
        'user_id', 'packing_id','unit_id', 'email_id', 'product_name', 'destination_port', 'country', 'quantity', 'message'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function userName(){
        return $this->hasOne(UserModel::class, 'id', 'user_id');
    }

    public function packing(){
        return $this->hasOne(PackingModel::class, 'id', 'packing_id');
    }

    public function unit(){
        return $this->hasOne(UnitModel::class, 'id', 'unit_id');
    }

}
