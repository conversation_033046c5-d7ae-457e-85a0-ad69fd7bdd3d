<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserNotificationsModel extends Model
{
    protected $table = "user_notifications";

    protected $fillable = [
        'user_id', 'category_id'
    ];

    protected $hidden = [
        'status', 'created_at', 'updated_at'
    ];

    public function user(){
        return $this -> hasOne(UserModel::class, 'id', 'user_id');
    }

    public function category(){
        return $this -> hasOne(CategoryModel::class, 'id', 'category_id');
    }
    // public function category()
    // {
    //     return (object) ['name' => 'General Message'];
    // }

    public function users(){
        return $this -> hasMany(UserAccess::class, 'user_id', 'user_id');
    }
}
