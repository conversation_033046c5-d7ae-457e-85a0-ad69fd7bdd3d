<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SocialUsersModel extends Model
{
    protected $table = 'social_user';
    protected $dates=['created_at', 'updated_at'];

    protected $fillable = [
        'id','user_id', 'provider' ,'provider_id'
    ];

    protected $hidden = [
        'status'
    ];
    public function user()
    {
        return $this->hasOne(UserModel::class, 'id', 'user_id');
    }
}
