<?php

namespace App\Jobs;

use App\Mail\FeedbackReceivedMail;
use App\Utils\AppConstant;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;

class FeedbackReceived implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $feedback;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($email, $message)
    {
        $this->email = $email;
        $this->message = $message;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new FeedbackReceivedMail($this->email, $this->message);
        $clientMail = AppConstant::CLIENT_EMAIL;
        Mail::to($clientMail)->send($email);
    }
}
