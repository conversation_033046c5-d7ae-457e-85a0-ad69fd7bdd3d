<?php

namespace App\Jobs;

use App\Mail\QuotationReceivedMail;
use App\Utils\AppConstant;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;

class QuotationReceived implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $quotation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($quotation)
    {
        $this->quotation = $quotation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new QuotationReceivedMail($this->quotation);
        $clientMail = AppConstant::CLIENT_EMAIL;
        Mail::to($clientMail)->send($email);
    }
}
