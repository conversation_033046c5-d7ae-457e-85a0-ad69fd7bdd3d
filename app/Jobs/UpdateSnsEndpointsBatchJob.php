<?php
namespace App\Jobs;

use App\Services\SNSNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateSnsEndpointsBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userBatch;
    public $tries = 3;
    public $timeout = 300;

    public function __construct($userBatch)
    {
        $this->userBatch = $userBatch;
    }

    public function handle(SNSNotificationService $snsService)
    {
        foreach ($this->userBatch as $user) {
            try {
                $snsService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
            } catch (\Exception $e) {
                Log::error("SNS Endpoint update failed for user {$user->user_id}: " . $e->getMessage());
            }
        }
    }
}