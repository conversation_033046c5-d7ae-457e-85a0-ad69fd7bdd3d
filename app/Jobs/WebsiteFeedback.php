<?php

namespace App\Jobs;

use App\Mail\WebsiteFeedbackMail;
use App\Utils\AppConstant;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;

class WebsiteFeedback implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $userMessage;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userMessage)
    {
        $this->userMessage = $userMessage;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new WebsiteFeedbackMail($this->userMessage);
        $clientMail = AppConstant::CLIENT_EMAIL;
        Mail::to($clientMail)->send($email);
    }
}
