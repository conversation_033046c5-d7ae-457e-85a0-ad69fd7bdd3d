<?php

// 1. Create Job for Price Update Notifications
// File: app/Jobs/SendPriceUpdateNotificationJob.php

namespace App\Jobs;

use App\Utils\AppConstant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\SNSNotificationService;
use App\Models\Notification;

class SendPriceUpdateNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userChunk;
    protected $productId;
    protected $productName;
    protected $notificationTitle;
    protected $notificationBody;
    protected $priceData;

    public $timeout = 600; // 10 minutes
    public $tries = 1;

    public function __construct($userChunk, $productId, $productName, $notificationTitle, $notificationBody, $priceData)
    {
        $this->userChunk = $userChunk;
        $this->productId = $productId;
        $this->productName = $productName;
        $this->notificationTitle = $notificationTitle;
        $this->notificationBody = $notificationBody;
        $this->priceData = $priceData;
        $this->onQueue('sendPriceUpdateAlerts');
    }

    public function handle(): void
    {
        Log::info("🚀 Processing price update notification chunk with " . count($this->userChunk) . " users for product: {$this->productName}");

        $snsNotificationService = app(SNSNotificationService::class);

        $successCount = 0;
        $failureCount = 0;

        foreach ($this->userChunk as $user) {
            try {
                // Send notification via SNS
                $success = $snsNotificationService->sendPushNotification(
                    $user['user_id'],
                    $this->notificationTitle,
                    $this->notificationBody . ' Price Updated',
                    NULL,
                    $this->productId
                );

                // Save notification to database if SNS push was successful
                if ($success) {
                    $notification = new Notification();
                    $notification->user_id = $user['user_id'];
                    $notification->product_id = $this->productId;
                    $notification->message = $this->notificationTitle;

                    if (!$this->priceData['max_price']) {
                        $notification->min_price = $this->priceData['min_price'];
                        $notification->price_diff = $this->priceData['price_diff'];
                    } else {
                        $notification->min_price = $this->priceData['min_price'];
                        $notification->max_price = $this->priceData['max_price'];
                        $notification->price_diff = $this->priceData['price_diff'];
                    }

                    $notification->message_type = AppConstant::NOTIFICATION_ALL;
                    $notification->save();

                    $successCount++;
                    Log::debug("✅ Price update notification saved for User ID: {$user['user_id']}, Product: {$this->productName}");
                } else {
                    $failureCount++;
                    Log::warning("⚠️ Failed to send notification to User ID: {$user['user_id']}");
                }
            } catch (\Exception $e) {
                $failureCount++;
                Log::error("❌ Error sending price update notification", [
                    'user_id' => $user['user_id'],
                    'product_id' => $this->productId,
                    'product_name' => $this->productName,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info("📊 Price update chunk processing completed - Success: {$successCount}, Failures: {$failureCount}");
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("❌ SendPriceUpdateNotificationJob failed", [
            'error' => $exception->getMessage(),
            'product_id' => $this->productId,
            'product_name' => $this->productName,
            'chunk_size' => count($this->userChunk)
        ]);
    }
}