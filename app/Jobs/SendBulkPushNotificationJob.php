<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use App\Services\SNSNotificationService;

class SendBulkPushNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userChunk;
    protected $title;
    protected $body;
    protected $messageId;
    protected $productId;

    // Laravel 5.4 compatibility
    public $timeout = 300;
    public $tries = 1;

    public function __construct($userChunk, $title, $body, $messageId, $productId = null)
    {
        $this->userChunk = $userChunk;
        $this->title = $title;
        $this->body = $body;
        $this->messageId = $messageId;
        $this->productId = $productId;
    }

    public function handle()
    {
        // Resolve SNSNotificationService from container
        $snsNotificationService = app(SNSNotificationService::class);

        Log::info("🚀 Processing notification chunk with " . count($this->userChunk) . " users");

        $successCount = 0;
        $failureCount = 0;

        foreach ($this->userChunk as $user) {
            try {
                $result = $snsNotificationService->sendPushNotification(
                    $user->user_id,
                    $this->title,
                    $this->body,
                    $this->messageId,
                    $this->productId
                );

                if ($result && !empty($result)) {
                    $successCount++;
                    Log::debug("✅ Notification sent successfully to User ID: {$user->user_id}");
                } else {
                    $failureCount++;
                    Log::warning("⚠️ Failed to send notification to User ID: {$user->user_id}");
                }
            } catch (\Exception $e) {
                $failureCount++;
                Log::error("❌ Exception sending notification to User ID: {$user->user_id}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        Log::info("📊 Chunk processing completed - Success: {$successCount}, Failures: {$failureCount}");
    }

    public function failed(\Exception $exception)
    {
        Log::error("❌ SendBulkPushNotificationJob failed", [
            'error' => $exception->getMessage(),
            'chunk_size' => count($this->userChunk),
            'message_id' => $this->messageId
        ]);
    }
}