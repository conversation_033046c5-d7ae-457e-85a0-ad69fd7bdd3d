<?php

namespace App\Jobs;

use App\Models\ProductModel;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class DividePriceUpdateAlerts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $changedProductIds;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($changedProductIds)
    {
        $this->changedProductIds = $changedProductIds;
    }

    /**
     * Add tags to the job.
     * 
     * @return array
     */
    public function tags()
    {
        return ['DividePriceUpdateAlerts ' . implode(',', $this->changedProductIds)];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        logger('DividePriceUpdateAlerts job started');
        $allProducts = ProductModel::find($this->changedProductIds);
        foreach ($allProducts as $product) {
            SendPriceUpdateAlerts::dispatch($product)->onQueue('sendPriceUpdateAlerts');
        }
        logger('DividePriceUpdateAlerts job finished');
    }
}
