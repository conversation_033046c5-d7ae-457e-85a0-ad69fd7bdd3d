<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class NotificationMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($message)
    {
        $this->message = $message;
    }

    public function tags(){
        return ['TYPE =>  Promotional Push.', 'MESSAGE => ' . $this->message];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // $response = $this->sendTopics('Promotional', AppConstant::NOTIFICATION_ALL, $this->message, AppConstant::FCM_TOPIC_ALL);
        // if ($response == AppConstant::STATUS_ACTIVE) {
        //     $notification = new Notification();
        //     $notification->message = $this->message;
        //     $notification->message_type = AppConstant::NOTIFICATION_ALL;
        //     $notification->save();
        // }
    }
}
