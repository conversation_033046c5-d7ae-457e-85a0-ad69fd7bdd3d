<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class NotificationMonitoringService
{
    /**
     * Track notification attempt
     */
    public function trackNotificationAttempt($userId, $type, $status, $details = [])
    {
        try {
            DB::table('notification_logs')->insert([
                'user_id' => $userId,
                'notification_type' => $type,
                'status' => $status, // 'success', 'failed', 'retry'
                'details' => json_encode($details),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Update real-time metrics
            $this->updateMetrics($status);
            
        } catch (\Exception $e) {
            Log::error('Failed to track notification: ' . $e->getMessage());
        }
    }

    /**
     * Get success rate for the last 24 hours
     */
    public function getSuccessRate($hours = 24)
    {
        $since = Carbon::now()->subHours($hours);
        
        $total = DB::table('notification_logs')
            ->where('created_at', '>=', $since)
            ->count();
            
        $successful = DB::table('notification_logs')
            ->where('created_at', '>=', $since)
            ->where('status', 'success')
            ->count();
            
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Get failure statistics
     */
    public function getFailureStats($hours = 24)
    {
        $since = Carbon::now()->subHours($hours);
        
        return DB::table('notification_logs')
            ->select('details', DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', $since)
            ->where('status', 'failed')
            ->groupBy('details')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function($item) {
                $details = json_decode($item->details, true);
                return [
                    'error' => $details['error'] ?? 'Unknown error',
                    'count' => $item->count
                ];
            });
    }

    /**
     * Update real-time metrics in cache
     */
    private function updateMetrics($status)
    {
        $key = 'notification_metrics_' . date('Y-m-d-H');
        $metrics = Cache::get($key, ['total' => 0, 'success' => 0, 'failed' => 0]);
        
        $metrics['total']++;
        if ($status === 'success') {
            $metrics['success']++;
        } elseif ($status === 'failed') {
            $metrics['failed']++;
        }
        
        Cache::put($key, $metrics, 3600); // Cache for 1 hour
    }

    /**
     * Get current hour metrics
     */
    public function getCurrentMetrics()
    {
        $key = 'notification_metrics_' . date('Y-m-d-H');
        return Cache::get($key, ['total' => 0, 'success' => 0, 'failed' => 0]);
    }

    /**
     * Check if user has failed notifications that need retry
     */
    public function getUsersNeedingRetry($maxRetries = 3)
    {
        return DB::table('notification_logs')
            ->select('user_id', DB::raw('COUNT(*) as retry_count'))
            ->where('status', 'failed')
            ->where('created_at', '>=', Carbon::now()->subHours(24))
            ->groupBy('user_id')
            ->having('retry_count', '<', $maxRetries)
            ->get();
    }

    /**
     * Mark notification for retry
     */
    public function markForRetry($userId, $details = [])
    {
        $this->trackNotificationAttempt($userId, 'retry', 'retry', $details);
    }

    /**
     * Get notification statistics dashboard data
     */
    public function getDashboardStats()
    {
        $last24h = $this->getSuccessRate(24);
        $last1h = $this->getSuccessRate(1);
        $currentMetrics = $this->getCurrentMetrics();
        $failureStats = $this->getFailureStats(24);
        
        return [
            'success_rate_24h' => $last24h,
            'success_rate_1h' => $last1h,
            'current_hour' => $currentMetrics,
            'top_failures' => $failureStats,
            'total_users_with_tokens' => DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->where('fcm_token', '!=', '')
                ->distinct('user_id')
                ->count(),
            'total_endpoints' => DB::table('user_access')
                ->whereNotNull('sns_endpoint_arn')
                ->count()
        ];
    }

    /**
     * Clean up old notification logs
     */
    public function cleanupOldLogs($daysToKeep = 30)
    {
        $cutoff = Carbon::now()->subDays($daysToKeep);
        
        $deleted = DB::table('notification_logs')
            ->where('created_at', '<', $cutoff)
            ->delete();
            
        Log::info("Cleaned up {$deleted} old notification logs");
        return $deleted;
    }

    /**
     * Alert if success rate drops below threshold
     */
    public function checkAlerts($threshold = 95)
    {
        $successRate = $this->getSuccessRate(1); // Last hour
        
        if ($successRate < $threshold && $successRate > 0) {
            $this->sendAlert([
                'type' => 'low_success_rate',
                'success_rate' => $successRate,
                'threshold' => $threshold,
                'time' => now()
            ]);
        }
    }

    /**
     * Send alert (can be extended to send emails, Slack, etc.)
     */
    private function sendAlert($alertData)
    {
        Log::critical('NOTIFICATION ALERT', $alertData);
        
        // Here you can add email notifications, Slack webhooks, etc.
        // Example:
        // Mail::to('<EMAIL>')->send(new NotificationAlert($alertData));
    }
}
