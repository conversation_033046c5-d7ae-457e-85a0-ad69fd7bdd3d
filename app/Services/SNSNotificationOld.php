<?php
namespace App\Services;

use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class SNSNotificationService
{
    protected $snsClient;

    public function __construct()
    {
        // Use standard AWS environment variables with fallbacks
        $region = env('AWS_DEFAULT_REGION', 'us-east-1');
        $accessKey = env('AWS_ACCESS_KEY_ID');
        $secretKey = env('AWS_SECRET_ACCESS_KEY');

        // Validate required configuration
        if (empty($region)) {
            Log::error('AWS Region is not configured. Please set AWS_DEFAULT_REGION in .env');
            throw new \Exception('AWS Region is not configured');
        }

        if (empty($accessKey) || empty($secretKey)) {
            Log::error('AWS credentials are not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in .env');
            throw new \Exception('AWS credentials are not configured');
        }

        $this->snsClient = new SnsClient([
            'version' => 'latest',
            'region' => $region,
            'credentials' => [
                'key'    => $accessKey,
                'secret' => $secretKey,
            ],
        ]);

        // Validate Platform Application ARN
        $platformArn = env('AWS_SNS_FCM_APPLICATION_ARN');
        if (empty($platformArn)) {
            Log::error('SNS Platform Application ARN is not configured in services.sns.fcm_application_arn');
        }

        Log::info('SNS Client initialized successfully', [
            'region' => $region,
            'platform_arn_configured' => !empty($platformArn)
        ]);
    }


    /**
     * Fetch FCM token from database for a given user.
     *
     * @param int $userId
     * @return \Illuminate\Support\Collection
     */
    public function getFcmToken($userId)
    {
        return DB::table('user_access')
            ->where('user_id', $userId)
            ->whereNotNull('fcm_token')
            ->select('fcm_token', 'sns_endpoint_arn')
            ->get();
    }

    /**
     * Create SNS Endpoint for the FCM token.
     *
     * @param string $fcmToken
     * @param int $userId
     * @return string|null
     */
    public function createSnsEndpoint($fcmToken, $userId): ?string
    {
        try {
            $platformArn = config('services.sns.fcm_application_arn');

            // Add this validation
            if (empty($platformArn)) {
                Log::error('Platform Application ARN is not configured');
                return null;
            }

            // Check if endpoint already exists for this user and token
            $existingEndpoint = $this->getExistingEndpoint($userId, $fcmToken);
            if ($existingEndpoint) {
                return $existingEndpoint->sns_endpoint_arn;
            }

            // Create new endpoint
            $result = $this->snsClient->createPlatformEndpoint([
                'PlatformApplicationArn' => $platformArn,
                'Token' => $fcmToken
            ]);

            if (isset($result['EndpointArn'])) {
                $endpointArn = $result['EndpointArn'];
                $this->saveEndpointArn($userId, $fcmToken, $endpointArn);
                Log::info("Created SNS endpoint successfully", [
                    'UserId' => $userId,
                    'EndpointArn' => $endpointArn
                ]);
                return $endpointArn;
            }

            return null;
        } catch (AwsException $e) {
            Log::error('Error creating SNS Endpoint: ' . $e->getMessage(), [
                'UserId' => $userId,
                'Token' => substr($fcmToken, 0, 20) . '...',
                'ErrorCode' => $e->getAwsErrorCode()
            ]);
            return null;
        }
    }

    /**
     * Subscribe a user's endpoint to an SNS topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return string|null Subscription ARN if successful, null otherwise
     */
    public function subscribeUserToTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                Log::error("No FCM token found for user ID: $userId");
                return null;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                Log::error("Failed to get/create endpoint for user ID: $userId");
                return null;
            }

            if ($this->isUserSubscribedToTopic($userId, $topicArn)) {
                Log::info("User already subscribed to topic", [
                    'UserId' => $userId,
                    'TopicArn' => $topicArn
                ]);
                return true;
            }

            $result = $this->snsClient->subscribe([
                'TopicArn' => $topicArn,
                'Protocol' => 'application',
                'Endpoint' => $endpointArn
            ]);

            $this->updateSubscriptionArn($userId, $result['SubscriptionArn']);

            Log::info("Successfully subscribed endpoint to topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'SubscriptionArn' => $result['SubscriptionArn']
            ]);

            return $result['SubscriptionArn'];

        } catch (AwsException $e) {
            Log::error("Error subscribing to topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if a user is subscribed to a topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return bool
     */
    public function isUserSubscribedToTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                return false;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                return false;
            }

            return $this->checkSubscriptionExists($topicArn, $endpointArn);

        } catch (AwsException $e) {
            Log::error("Error checking topic subscription", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Unsubscribe a user from a topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return bool
     */
    public function unsubscribeUserFromTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                return false;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                return false;
            }

            $subscriptionArn = $this->findSubscriptionArn($topicArn, $endpointArn);
            if (!$subscriptionArn) {
                Log::warning("No subscription found for user", [
                    'UserId' => $userId,
                    'TopicArn' => $topicArn
                ]);
                return false;
            }

            $this->snsClient->unsubscribe(['SubscriptionArn' => $subscriptionArn]);
            $this->clearSubscriptionArn($userId);

            Log::info("Successfully unsubscribed from topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'SubscriptionArn' => $subscriptionArn
            ]);

            return true;

        } catch (AwsException $e) {
            Log::error("Error unsubscribing from topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send push notification to a user using SNS.
     *
     * @param int $userId
     * @param string $title
     * @param string $message
     * @param mixed $messageId
     * @return array|null
     */
    public function sendPushNotification($userId, $title, $message, $messageId, $productId)
    {
        $deviceTokens = $this->getFcmToken($userId);
        if ($deviceTokens->isEmpty()) {
            Log::error("No FCM tokens found for user ID: $userId");
            return null;
        }

        $results = [];

        foreach ($deviceTokens as $device) {
            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $device->fcm_token);

            if (!$endpointArn) {
                Log::warning("No endpoint ARN created for token: {$device->fcm_token}");
                continue;
            }

            try {
                $payload = $this->buildNotificationPayload($title, $message, $messageId, $productId, $endpointArn);
                $result = $this->publishNotification($payload, $endpointArn);

                if ($result) {
                    $results[] = $result;
                }

            } catch (AwsException $e) {
                Log::error("SNS Notification Failed for User ID: {$userId}", [
                    'error' => $e->getAwsErrorMessage(),
                    'token' => $device->fcm_token,
                    'endpoint' => $endpointArn,
                ]);
            }
        }

        return $results;
    }

    /**
     * Check if the endpoint exists for the user or create a new one.
     *
     * @param int $userId
     * @param string $fcmToken
     * @return string|null
     */
    public function getOrCreateSnsEndpoint($userId, $fcmToken)
    {
        try {
            $record = DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->first();

            if ($record && !empty($record->sns_endpoint_arn)) {
                if ($this->isEndpointValid($record->sns_endpoint_arn)) {
                    Log::info("Using existing SNS endpoint: {$record->sns_endpoint_arn}");
                    return $record->sns_endpoint_arn;
                }

                $this->cleanupInvalidEndpoint($userId, $fcmToken, $record->sns_endpoint_arn);
            }

            return $this->createNewEndpoint($userId, $fcmToken);

        } catch (AwsException $e) {
            Log::error("AWS SNS Error for User ID: {$userId}, FCM Token: {$fcmToken} - " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update all SNS endpoints for all users.
     *
     * @return void
     */
    public function updateAllSnsEndpoints()
    {
        try {
            $users = DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->select('user_id', 'fcm_token', 'sns_endpoint_arn')
                ->get();

            if ($users->isEmpty()) {
                Log::warning("⚠️ No users with valid FCM tokens found.");
                return;
            }

            foreach ($users as $user) {
                Log::info("🔄 Updating SNS Endpoint for User ID: {$user->user_id}, Token: {$user->fcm_token}");
                $this->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
            }

            Log::info("✅ All SNS endpoints updated successfully.");
        } catch (Exception $e) {
            Log::error("❌ Error updating SNS endpoints: " . $e->getMessage());
        }
    }

    /**
     * Send a notification to a topic.
     *
     * @param string $topicArn
     * @param string $title
     * @param string $message
     * @return mixed
     */
    public function sendTopicNotification($topicArn, $title, $message)
    {
        try {
            $payload = $this->buildTopicNotificationPayload($title, $message);

            Log::info("Publishing SNS Topic Notification", [
                'TopicArn' => $topicArn,
                'RawPayload' => json_encode($payload, JSON_PRETTY_PRINT)
            ]);

            $result = $this->snsClient->publish([
                'TopicArn' => $topicArn,
                'Message' => json_encode($payload, JSON_UNESCAPED_SLASHES),
                'MessageStructure' => 'json'
            ]);

            Log::info("SNS Topic Notification Sent Successfully!", [
                'MessageId' => $result['MessageId'],
                'TopicArn' => $topicArn
            ]);

            return $result;
        } catch (AwsException $e) {
            Log::error("Error sending SNS Topic Notification", [
                'ErrorCode' => $e->getAwsErrorCode(),
                'ErrorMessage' => $e->getAwsErrorMessage(),
                'TopicArn' => $topicArn
            ]);
            return null;
        }
    }

    /**
     * Check if an endpoint is for an iOS device
     *
     * @param string $endpointArn
     * @return bool
     */
    private function isIosEndpoint($endpointArn)
    {
        return (strpos($endpointArn, '/APNS/') !== false || strpos($endpointArn, '/APNS_SANDBOX/') !== false);
    }

    /**
     * Get existing endpoint for user and token
     *
     * @param int $userId
     * @param string $fcmToken
     * @return object|null
     */
    private function getExistingEndpoint($userId, $fcmToken)
    {
        return DB::table('user_access')
            ->where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->first();
    }

    /**
     * Save endpoint ARN to database
     *
     * @param int $userId
     * @param string $fcmToken
     * @param string $endpointArn
     * @return void
     */
    private function saveEndpointArn($userId, $fcmToken, $endpointArn)
    {
        DB::table('user_access')
            ->updateOrInsert(
                ['user_id' => $userId, 'fcm_token' => $fcmToken],
                ['sns_endpoint_arn' => $endpointArn]
            );
    }

    /**
     * Check if subscription exists for topic and endpoint
     *
     * @param string $topicArn
     * @param string $endpointArn
     * @return bool
     */
    private function checkSubscriptionExists($topicArn, $endpointArn)
    {
        $nextToken = null;
        do {
            $params = ['TopicArn' => $topicArn];
            if ($nextToken) {
                $params['NextToken'] = $nextToken;
            }

            $result = $this->snsClient->listSubscriptionsByTopic($params);

            foreach ($result['Subscriptions'] as $subscription) {
                if ($subscription['Endpoint'] === $endpointArn &&
                    $subscription['SubscriptionArn'] !== 'PendingConfirmation') {
                    return true;
                }
            }

            $nextToken = isset($result['NextToken']) ? $result['NextToken'] : null;
        } while ($nextToken);

        return false;
    }

    /**
     * Find subscription ARN for topic and endpoint
     *
     * @param string $topicArn
     * @param string $endpointArn
     * @return string|null
     */
    private function findSubscriptionArn($topicArn, $endpointArn)
    {
        $nextToken = null;
        do {
            $params = ['TopicArn' => $topicArn];
            if ($nextToken) {
                $params['NextToken'] = $nextToken;
            }

            $result = $this->snsClient->listSubscriptionsByTopic($params);

            foreach ($result['Subscriptions'] as $subscription) {
                if ($subscription['Endpoint'] === $endpointArn) {
                    return $subscription['SubscriptionArn'];
                }
            }

            $nextToken = isset($result['NextToken']) ? $result['NextToken'] : null;
        } while ($nextToken);

        return null;
    }

    /**
     * Update subscription ARN in database
     *
     * @param int $userId
     * @param string $subscriptionArn
     * @return void
     */
    private function updateSubscriptionArn($userId, $subscriptionArn)
    {
        DB::table('user_access')
            ->where('user_id', $userId)
            ->update(['sns_subscription_arn' => $subscriptionArn]);
    }

    /**
     * Clear subscription ARN from database
     *
     * @param int $userId
     * @return void
     */
    private function clearSubscriptionArn($userId)
    {
        DB::table('user_access')
            ->where('user_id', $userId)
            ->update(['sns_subscription_arn' => null]);
    }

    /**
     * Build notification payload for individual notifications
     *
     * @param string $title
     * @param string $message
     * @param mixed $messageId
     * @param string $endpointArn
     * @return array
     */
    private function buildNotificationPayload($title, $message, $messageId, $productId, $endpointArn)
    {
        $isIos = $this->isIosEndpoint($endpointArn);

        $payload = [
            'default' => 'Notification fallback body',
            'GCM' => json_encode([
                'notification' => [
                    'title' => $title,
                    'body' => $message,
                ],
                'data' => [
                    'dataGen' => 'priority message',
                    'msg_id' => (string)$messageId,
                    'product_id' => (string)$productId,
                ],
                'android' => [
                    'priority' => 'high',
                    'notification' => [
                        'sound' => 'default',
                        'title' => $title,
                        'body' => $message,
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    ],
                    'data' => [
                        'dataAndroid' => 'priority message',
                    ],
                    'ttl' => '10023.32s',
                ],
            ], JSON_UNESCAPED_SLASHES)
        ];

        // Add APNS payload for iOS
        $apnsPayload = [
            'aps' => [
                'alert' => [
                    'title' => $title,
                    'body' => $message,
                ],
                'sound' => 'default',
                'badge' => 1,
                'content-available' => 1,
            ],
            'msg_id' => (string)$messageId,
            'custom_data' => 'priority message',
        ];

        $payload['APNS'] = json_encode($apnsPayload, JSON_UNESCAPED_SLASHES);
        $payload['APNS_SANDBOX'] = json_encode($apnsPayload, JSON_UNESCAPED_SLASHES);

        return $payload;
    }

    /**
     * Build notification payload for topic notifications
     *
     * @param string $title
     * @param string $message
     * @return array
     */
    private function buildTopicNotificationPayload($title, $message)
    {
        $fcmPayload = [
            'fcmV1Message' => [
                'message' => [
                    'notification' => [
                        'title' => $title,
                        'body' => $message
                    ],
                    'android' => [
                        'priority' => 'high',
                        'notification' => [
                            'sound' => 'default',
                            'channel_id' => 'high_importance_channel',
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ],
                        'data' => [
                            'message_type' => 'notification',
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ]
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $title,
                                    'body' => $message
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                                'content-available' => 1,
                                'mutable-content' => 1
                            ]
                        ],
                        'headers' => [
                            'apns-push-type' => 'alert',
                            'apns-priority' => '10'
                        ]
                    ],
                    'data' => [
                        'message_type' => 'notification',
                        'title' => $title,
                        'body' => $message
                    ]
                ]
            ]
        ];

        return [
            'default' => $message,
            'GCM' => json_encode($fcmPayload),
            'APNS' => json_encode(['aps' => [
                'alert' => [
                    'title' => $title,
                    'body' => $message
                ],
                'sound' => 'default'
            ]])
        ];
    }

    /**
     * Publish notification to SNS
     *
     * @param array $payload
     * @param string $endpointArn
     * @return mixed
     */
    private function publishNotification(array $payload, $endpointArn)
    {
        $finalPayload = [
            'Message' => json_encode($payload, JSON_UNESCAPED_SLASHES),
            'TargetArn' => $endpointArn,
            'MessageStructure' => 'json'
        ];

        Log::info("Sending SNS Notification", [
            'endpointArn' => $endpointArn,
            'platform' => $this->isIosEndpoint($endpointArn) ? 'iOS' : 'Android',
            'payload' => $finalPayload
        ]);

        return $this->snsClient->publish($finalPayload);
    }

    /**
     * Check if endpoint is valid and enabled
     *
     * @param string $endpointArn
     * @return bool
     */
    private function isEndpointValid($endpointArn)
    {
        try {
            $this->snsClient->getEndpointAttributes(['EndpointArn' => $endpointArn]);
            // Also check if endpoint is enabled for more robust validation
            return $this->isEndpointEnabled($endpointArn);
        } catch (AwsException $e) {
            return false;
        }
    }

    /**
     * Cleanup invalid endpoint with retry mechanism
     *
     * @param int $userId
     * @param string $fcmToken
     * @param string $endpointArn
     * @return void
     */
    private function cleanupInvalidEndpoint($userId, $fcmToken, $endpointArn)
    {
        Log::warning("Invalid SNS Endpoint for User ID: {$userId}, attempting recovery...");

        // First try to re-enable the endpoint with the current token
        if ($this->reEnableEndpoint($endpointArn, $fcmToken)) {
            Log::info("Successfully re-enabled endpoint for User ID: {$userId}");
            return;
        }

        // If re-enabling fails, delete the endpoint
        try {
            $this->snsClient->deleteEndpoint(['EndpointArn' => $endpointArn]);
            Log::info("Deleted invalid endpoint for User ID: {$userId}");
        } catch (AwsException $e) {
            // Endpoint might already be deleted
            Log::warning("Could not delete endpoint, may already be removed: " . $e->getMessage());
        }

        // Clear the endpoint from database
        DB::table('user_access')
            ->where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->update(['sns_endpoint_arn' => null]);
    }

    /**
     * Create new endpoint
     *
     * @param int $userId
     * @param string $fcmToken
     * @return string|null
     */
    private function createNewEndpoint($userId, $fcmToken): ?string
    {
        Log::info("Creating SNS Endpoint for User ID: {$userId}, Token: " . substr($fcmToken, 0, 20) . "...");

        // Check if endpoint already exists for this token to prevent duplicates
        $existingEndpoint = $this->findExistingEndpoint($fcmToken);
        if ($existingEndpoint) {
            Log::info("Found existing endpoint for token, updating database record", [
                'EndpointArn' => $existingEndpoint,
                'UserId' => $userId
            ]);

            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update(['sns_endpoint_arn' => $existingEndpoint]);
            return $existingEndpoint;
        }

        $platformArn = config('services.sns.fcm_application_arn');

        // Add this validation
        if (empty($platformArn)) {
            Log::error('Platform Application ARN is not configured');
            return null;
        }

        $result = $this->snsClient->createPlatformEndpoint([
            'PlatformApplicationArn' => $platformArn,
            'Token' => $fcmToken
        ]);

        if (isset($result['EndpointArn'])) {
            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update(['sns_endpoint_arn' => $result['EndpointArn']]);

            Log::info("Successfully created new SNS endpoint", [
                'UserId' => $userId,
                'EndpointArn' => $result['EndpointArn']
            ]);

            return $result['EndpointArn'];
        }

        return null;
    }

    /**
     * Check if an endpoint is enabled
     *
     * @param string $endpointArn The endpoint ARN to check
     * @return bool Whether the endpoint is enabled
     */
    public function isEndpointEnabled($endpointArn)
    {
        try {
            $result = $this->snsClient->getEndpointAttributes([
                'EndpointArn' => $endpointArn
            ]);

            return isset($result['Attributes']['Enabled']) &&
                $result['Attributes']['Enabled'] === 'true';
        } catch (\Aws\Sns\Exception\SnsException $e) {
            Log::warning('Error checking endpoint status: ' . $e->getMessage(), [
                'EndpointArn' => $endpointArn
            ]);

            return false;
        }
    }

    /**
     * Re-enable a disabled endpoint with a new token
     *
     * @param string $endpointArn The endpoint ARN to re-enable
     * @param string $token The new FCM token to use
     * @return bool Whether re-enabling was successful
     */
    public function reEnableEndpoint($endpointArn, $token)
    {
        try {
            $this->snsClient->setEndpointAttributes([
                'EndpointArn' => $endpointArn,
                'Attributes' => [
                    'Enabled' => 'true',
                    'Token' => $token
                ]
            ]);

            Log::info('Successfully re-enabled endpoint', [
                'EndpointArn' => $endpointArn
            ]);

            return true;
        } catch (\Aws\Sns\Exception\SnsException $e) {
            Log::error('Failed to re-enable endpoint: ' . $e->getMessage(), [
                'EndpointArn' => $endpointArn,
                'ErrorCode' => $e->getAwsErrorCode()
            ]);

            return false;
        }
    }

    /**
     * Check if token is already registered to prevent duplicates
     *
     * @param string $token The FCM token to check
     * @return string|null The existing endpoint ARN or null if not found
     */
    public function findExistingEndpoint($token): ?string
    {
//        try {
        $platformApplicationArn = config('services.sns.fcm_application_arn');

        Log::info('[SNS] Checking for existing endpoint using token.', [
            'token_start' => substr($token, 0, 20) . '...',
            'application_arn' => $platformApplicationArn,
        ]);

        // Validate configuration
        if (empty($platformApplicationArn)) {
            Log::error('[SNS] Platform Application ARN is not configured.', [
                'method' => 'findExistingEndpoint',
            ]);
            return null;
        }

        // List endpoints by PlatformApplicationArn
        $result = $this->snsClient->listEndpointsByPlatformApplication([
            'PlatformApplicationArn' => $platformApplicationArn,
        ]);

        Log::info('[SNS] Retrieved endpoints from AWS SNS.', [
            'endpoint_count' => isset($result['Endpoints']) ? count($result['Endpoints']) : 0,
        ]);

        // Search for matching token
        foreach ($result['Endpoints'] as $endpoint) {
            $endpointToken = $endpoint['Attributes']['Token'] ?? null;
            $enabledStatus = $endpoint['Attributes']['Enabled'] ?? null;

            Log::debug('[SNS] Inspecting endpoint.', [
                'EndpointArn' => $endpoint['EndpointArn'] ?? 'N/A',
                'Token' => $endpointToken,
                'Enabled' => $enabledStatus,
            ]);

            if ($endpointToken === $token && $enabledStatus === 'true') {
                Log::info('[SNS] Matching enabled endpoint found.', [
                    'EndpointArn' => $endpoint['EndpointArn'],
                ]);
                return $endpoint['EndpointArn'];
            }
        }

        Log::info('[SNS] No matching endpoint found for token.', [
            'token_start' => substr($token, 0, 20) . '...',
        ]);

        return null;
//        } catch (\Exception $e) {
//            Log::error('[SNS] Error occurred while checking for existing endpoint.', [
//                'message' => $e->getMessage(),
//                'token_start' => substr($token, 0, 20) . '...',
//                'trace' => $e->getTraceAsString(),
//            ]);
//            return null;
//        }
    }


}