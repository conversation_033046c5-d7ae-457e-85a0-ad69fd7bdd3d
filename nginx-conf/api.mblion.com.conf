upstream api {
	server mblion-php-fpm:9000;

}
map $http_x_forwarded_proto $fastcgi_param_https_variable {
	default '';
	https 'on';
}
server {
	server_name **************;
	listen 80;
	listen [::]:80 ;
    client_max_body_size 100M;

	index index.php index.html;
	root /var/www/public;


	location / {
		try_files $uri /index.php?$query_string;
	}

	location ~ \.php$ {
		fastcgi_split_path_info ^(.+\.php)(/.+)$;
		fastcgi_pass api;
		fastcgi_index index.php;
		include fastcgi_params;
		fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
		fastcgi_param HTTPS $fastcgi_param_https_variable;
		fastcgi_param PATH_INFO $fastcgi_path_info;
	}
}