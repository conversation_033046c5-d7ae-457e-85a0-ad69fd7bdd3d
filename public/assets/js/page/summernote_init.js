function summernoteInit(url) {
    axios({
        method: 'GET',
        url: url,
    }).then(function (response) {
        var IconButton = function (context) {
            var ui = $.summernote.ui;
            var button = ui.buttonGroup([
                ui.button({
                    className: 'dropdown-toggle',
                    contents: '<span class="fa fa-smile-o"></span><i class="icon icon-images3"></i><span class="caret"></span>',
                    // tooltip: "Icons",
                    data: {
                        toggle: 'dropdown'
                    }
                }),
                ui.dropdown({
                    className: 'dropdown-style dropdownBox',
                    contents: response.data,
                    callback: function ($dropdown) {
                        $dropdown.find('li').each(function () {
                            $(this).click(function () {
                                context.invoke("editor.pasteHTML", $(this).html() + " ");
                            });
                        });
                    }
                })
            ])
            return button.render();
        }
        $('.summernote').summernote({
            toolbar: [
                ["style", ["style"]],
                ["font", ["bold", "underline", "clear"]],
                // ["fontname", ["fontname"]],
                ["color", ["color"]],
                ["para", ["ul", "ol", "paragraph"]],
                //["table", ["table"]],
                //["insert", ["link", "picture", "video"]],
                ["view", ["link", "fullscreen", "codeview", "help"]],
                ["mybutton", ['icons']]
            ],
            buttons: {
                icons: IconButton
            },
            focus: true,
            disableResizeEditor: true,
            disableDragAndDrop: true
        });
    });
}