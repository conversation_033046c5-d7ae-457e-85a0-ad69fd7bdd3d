function filter(url, values) {
    axios({
        url: url,
        method: 'POST',
        data: values
    }).then(function (response) {
        $('.datatable-basic').DataTable().destroy();
        $('.datatable-basic tbody').html(response.data);
        $('.datatable-basic').DataTable({
            "aoColumnDefs": [{"bSortable": false, "aTargets": [1, 2, 3, 4, 6]}],
            "columnDefs": [
                { "targets": [4,6], "searchable": false }
            ]
        }).draw();
        $(".timezone").each(function (i, item) {
            $(item).text(moment.unix($(item).data('time')).format("DD/MM/YYYY hh:mm A"));
        });
        messagePopup();
    }).catch(function (error) {
        console.log(error.message)
    });
}