function filter(url, values) {
    axios({
        url: url,
        method: 'POST',
        data: values
    }).then(function (response) {
        $('.datatable-basic').DataTable().destroy();
        $('.datatable-basic tbody').html(response.data);
        $('.datatable-basic').DataTable({
            "aoColumnDefs": [{"bSortable": false, "aTargets": [1, 2, 3, 4, 5, 6, 7, 9]}],
        }).draw();
        $(".timezone").each(function (i, item) {
            $(item).text(moment.unix($(item).data('time')).format("DD/MM/YYYY hh:mm A"));
        });
    }).catch(function (error) {
        console.log(error.message)
    });
}