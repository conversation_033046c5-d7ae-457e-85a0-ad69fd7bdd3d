
$.validator.addMethod('valid_email', function(value, element) {
    let filter = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
    return filter.test(value)
}, 'Please enter a valid email address.');

$.validator.addMethod("alphaSpace", function (value, element) {
    return this.optional(element) || /^[a-zA-Z][a-zA-Z\s]*$/i.test(value);
}, "Please start with letter and enter only letter and spaces.");


function addRules(formId) {
    let addForm = {
        name:{
            required:true,
            minlength:2,
            maxlength:50,
            alphaSpace: true
        },
        email:{
            required:true,
            email:true,
            valid_email: true,

        },
        message:{
            minlength:2,
            maxlength:150,
            required: true
        }

    };
    let message = {
       contact: {
           minlength:'Please enter at least 6 digit',
           maxlength:'Please enter no more than 14 digit.',
       }
    }
    validateForm(formId, addForm,message);
}

function MailRule(formId){
    var editForm = {
        email:{
            required:true,
            email:true,
            valid_email: true,

        },

    };
    validateForm(formId, editForm);
}
