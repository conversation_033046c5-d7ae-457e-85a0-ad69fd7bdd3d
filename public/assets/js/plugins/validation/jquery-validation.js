/**
 * Created by jay<PERSON> on 30/9/16.
 */

function validateForm(formId, formRules, msg) {

    $(formId).validate({
        rules: formRules,
        messages: msg,
        ignore: ".hide",
        /*  messages: {
         minlength: jQuery.validator.format("At least {0} characters required")
         },*/
        // Different components require proper error label placement
        errorPlacement: function (error, element) {
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice")
                || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                }
                else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }
            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }


            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            }

            else {
                // console.log(element.parent())
                if ($(element.parent()).hasClass('validator-class')) {
                    // console.log('if');
                    error.insertAfter(element);
                } else {
                    // console.log('else');
                  /*  if($(element.parent()).hasClass('multi-select-full')){
                        element.parent().parent().addClass('has-error');
                        element.parent().find('.note-editor').css('border-color','red');
                    } else {
                        element.parent().addClass('has-error');
                    }
                    if($(element.parent()).hasClass('btn-file')){
                        //console.log(element.parent().parent().parent());
                        element.parent().parent().parent().addClass('has-error');
                    }*/
                    // element.parent().addClass('has-error');
                    error.insertAfter(element.parent());
                }
            }
        },
        validClass: "validation-valid-label",
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            // console.log('highlight');
            // $(element).parent().addClass('has-error');
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass, validClass) {
            // console.log('unhighlight');
            $(element).removeClass(errorClass);
            // $(element).parent().removeClass('has-error');
            /*$(element).parent().parent().removeClass('has-error');
            $(element).parent().find('.note-editor').css('border-color','');
            $(element).parent().parent().parent().removeClass('has-error');*/

        },

        /* success: function (label,element) {
             label.addClass("validation-valid-label").text("Successfully")
         },*/
    });
    jQuery.validator.addMethod("html_validate", function (value, element) {
        if (this.optional(element) || /<\/?[^>]+(>|$)/g.test(value)) {
            return false;
        } else {
            return true;
        }
    });

    jQuery.validator.addMethod("lettersAndSpace", function (value, element) {
        return this.optional(element) || /^[A-z][A-z\s]*$/.test(value);
    }, "Please enter letters only.");

    jQuery.validator.addMethod("strongPassword", function (value, element) {
        return this.optional(element) || /(?=^.{8,}$)(?=.*\d)(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/u.test(value);
    }, "Password must be at least 8 characters including 1 uppercase character, 1 lowercase and 1 numeric value.");

    jQuery.validator.addMethod("vimeoLink", function (value, element) {
        return this.optional(element) || /https:\/\/vimeo\.com\/*\d*/.test(value);
    }, "Invalid video url. Allow only Vimeo Video url.");

    $.validator.addMethod( "greaterThan", function ( value, element, param ) {
        var target = $( param );

        if ( this.settings.onfocusout && target.not( ".validate-greaterThan-blur" ).length ) {
            target.addClass( "validate-greaterThan-blur" ).on( "blur.validate-greaterThan", function () {
                $(element).valid();
            });
        }

        return value > target.val();
    }, "Please enter a greater value.");

    $.validator.addMethod("gt",
        function(value, max, min){
            return parseInt(value) > parseInt($(min).val());
        }, "Max price must be greater than min price."
    );
}
