var seletbasic = '.select-basic';
function load_data(id, index) {
    let urlRequest;
    if (id == "") {
        urlRequest = location.origin+"/loaddata/" + index;
    } else {
        urlRequest = location.origin+"/loaddata/" + index + "/" + id;
    }
    $.ajax({
        url: urlRequest,
        type: 'get',
        complete: function () {
        },
        success: function (data) {
            $("#" + index).html(data);
            if ($.fn.selectpicker) {
                $("#" + index).selectpicker('refresh');
            } else if ($.fn.selectmenu) {
                $(seletbasic).selectmenu("destroy");
                $(seletbasic).selectmenu();
            }
        },
        error: function (xhr) {
            //alert(xhr.responseText);
            console.log(xhr.responseText);
        }
    });
}
function load_seldata(id, index, selId) {
    let urlRequest;
    if (id == "") {
        urlRequest = location.origin+"/loadseldata/" + index + "/" + selId;
    } else {
        urlRequest = location.origin+"/loadseldata/" + index + "/" + selId + "/" + id;
    }

    $.ajax({
        url: urlRequest,
        type: 'get',
        complete: function () {
        },
        success: function (data) {
            $("#" + index).html(data);
            if ($.fn.selectpicker) {
                $("#" + index).selectpicker('refresh');
            } else if ($.fn.selectmenu) {
                $(seletbasic).selectmenu("destroy");
                $(seletbasic).selectmenu();
            }
        },
        error: function (xhr) {
            //alert(xhr.responseText);
            console.log(xhr.responseText);
        }
    })
    ;
}
