!function (e) {
    var t = function (e, t) {
        var a, n = e.charCodeAt(t);
        return 55296 > n || n > 56319 || e.length <= t + 1 || (a = e.charCodeAt(t + 1), 56320 > a || a > 57343) ? e[t] : e.substring(t, t + 2)
    }, a = function (e, a, n) {
        for (var i, r = "", o = 0, c = 0, d = e.length; d > o;) i = t(e, o), c >= a && n > c && (r += i), o += i.length, c += 1;
        return r
    };
    e.fn.initial = function (t) {
        var n,
            i = ["#1abc9c", "#16a085", "#f1c40f", "#f39c12", "#2ecc71", "#27ae60", "#e67e22", "#d35400", "#3498db", "#2980b9", "#e74c3c", "#c0392b", "#9b59b6", "#8e44ad", "#bdc3c7", "#34495e", "#2c3e50", "#95a5a6", "#7f8c8d", "#ec87bf", "#d870ad", "#f69785", "#9ba37e", "#b49255", "#b49255", "#a94136"];
            // i = ["#fff"];
        return this.each(function () {
            var r = e(this), o = e.extend({
                name: "Name",
                color: null,
                seed: 0,
                charCount: 1,
                textColor: "#ffffff",
                height: 100,
                width: 100,
                fontSize: 60,
                fontWeight: 400,
                fontFamily: "HelveticaNeue-Light,Helvetica Neue Light,Helvetica Neue,Helvetica, Arial,Lucida Grande, sans-serif",
                radius: 0
            }, t);
            o = e.extend(o, r.data());
            var c = a(o.name, 0, o.charCount).toUpperCase(), d = e('<text text-anchor="middle"></text>').attr({
                y: "50%",
                x: "50%",
                dy: "0.35em",
                "pointer-events": "auto",
                fill: o.textColor,
                "font-family": o.fontFamily
            }).html(c).css({"font-weight": o.fontWeight, "font-size": o.fontSize + "px"});
            if (null == o.color) {
                var h = Math.floor((c.charCodeAt(0) + o.seed) % i.length);
                n = i[h]
            } else n = o.color;
            var f = e("<svg></svg>").attr({
                xmlns: "http://www.w3.org/2000/svg",
                "pointer-events": "none",
                width: o.width,
                height: o.height
            }).css({
                "background-color": n,
                width: o.width + "px",
                height: o.height + "px",
                "border-radius": o.radius + "px",
                "-moz-border-radius": o.radius + "px"
            });
            f.append(d);
            var l = window.btoa(unescape(encodeURIComponent(e("<div>").append(f.clone()).html())));
            r.attr("src", "data:image/svg+xml;base64," + l)
        })
    }
}(jQuery);