$(function () {
    $.extend($.fn.dataTable.defaults, {
        autoWidth: false,
        columnDefs: [{
            orderable: false,
            width: '100px',
        }],
        dom: '<"datatable-header"fl><"datatable-scroll"t><"datatable-footer"ip>',
        language: {
            search: '<span>Filter:</span> _INPUT_',
            searchPlaceholder: 'Type to filter...',
            lengthMenu: '<span>Show:</span> _MENU_',
            paginate: {'first': 'First', 'last': 'Last', 'next': '&rarr;', 'previous': '&larr;'}
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });
});

function statusSpan(status) {
    let className = '';
    let statusTitle = '';
    switch (parseInt(status)) {
        case 1:
            className = 'label-success';
            statusTitle = 'Active';
            break;
        case 0:
            className = 'label-danger';
            statusTitle = 'Inactive';
            break;
    }
    return '<span class="label ' + className + '">' + statusTitle + '</span>';
}

function serialNo(meta) {
    return meta.row + meta.settings._iDisplayStart + 1;
}