

.portal-logo {
    width: 360px;
}

.reset-form{
    margin-top: 125px;
}

.portal-btn {
    border-radius: 25px;
    background-color: #15AA59;
    border-color: #15AA59;
    width: 100%;
    color: #fff;
}

.portal-btn:hover {
    border-radius: 25px;
    background-color: #15AA59;
    border-color: #15AA59;
    width: 100%;
    color: #fff;
}

.portal-btn:focus {
    border-radius: 25px;
    background-color: #15AA59;
    border-color: #15AA59;
    width: 100%;
    color: #fff;
}

.form-control {
    border-radius: 25px;
}

.form-control:focus {
    border-color: #E57373 !important;
    outline: none;
}

a:hover {
    color: #e8002a;
}

.validation-error-label {
    font-size: 13px;
    color: red;
    font-weight: 400;
    padding-left: 10px;
    margin-bottom: 0px;
}

.header-logo {
    width: 54px;
    height: 50px;
    border-radius: 50%;
}

.logo {
    background-color: #e8002a;
    color: #fff;
    border-bottom: 0 solid transparent;
    padding-top: 10px;
}

.logo-size {
    width: 30px;
    height: 30px;
}

.navbar .navbar-custom-menu {
    margin-top: 10px;
}

.navbar .navbar-custom-menu a {
    margin-right: 7px;
}

.sidebar .sidebar-menu li a img {
    width: 26px !important;
    padding-bottom: 10px;
    display: block;
    margin: 0 auto;
}

aside {
    background-color: black;
}

.sidebar ul li {
    background-color: #494949;
    border-bottom: 1px solid black;
}

.sidebar ul li:hover {
    background-color: #e8002a;
}

.content-wrapper {
    background-color: #fff;
}

#contact .content-header .row .col-lg-10 {
    padding-top: 5px;
}

#card.row {
    padding: 20px;
}

#csv.row {
    padding: 0 5px;
}

@media only screen and (max-width: 425px) {
    #contact .content-header .row .col-lg-10 {
        padding-top: 15px;
    }
}

.cardHolder {
    width: 60px;
}

.socialRow input[type="text"] {
    border-radius: 0px 25px 25px 0px !important;
}

.radius-right {
    border-radius: 0px 25px 25px 0px !important;
}

.radius-left {
    border-radius: 25px 0px 0px 25px !important;
}

.setting-image {
    width: 150px;
    height: 150px;
}

.setting-btn-padd {
    padding-top: 15px;
}

.font-size {
    font-size: 20px;
}

.top-margin {
    margin-top: 15px;
}

#profilePic.top-margin {
    margin-top: 10px;
}

.margin-top {
    margin-top: 15px;
}

.content-wrapper {
    background-color: #fff !important;
}

.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 25px !important;
}

#deleteProfile, #previewProfile {
    padding-top: 15px;
}

.border-radius-btn {
    border-radius: 25px !important;
}

.preview {
    margin-bottom: 0px;
}

#deleteProfile .btn-primary {
    background-color: transparent !important;
    color: #999999 !important;
    border: 1px solid #999999 !important;
}

#deleteProfile .btn-primary:hover {
    background-color: #999999 !important;
    color: #fff !important;
    border: 1px solid #fff !important;
}

#previewProfile .btn-primary {
    background-color: transparent !important;
    color: #999999 !important;
    border: 1px solid #999999 !important;
}

#previewProfile .btn-primary:hover {
    background-color: #999999 !important;
    color: #fff !important;
    border: 1px solid #fff !important;
}

.socialImg {
    width: 40px;
}

.socialSection {
    margin-top: 20px !important;
}

#contactDetail.content-wrapper {
    padding-top: 20px;
}

.portal-textarea {
    margin-top: 10px;
    margin-bottom: 10px;
    resize: none;
    width: 100%;
}

.card_share {
    border-bottom: 1px solid #ccc
}

.padd-bottom {
    padding-bottom: 20px !important;
}

.padd-top {
    padding-top: 20px !important;
}

.main-header .sidebar-toggle {
    background-color: #e8002a;
    padding: 20px 15px;
    height: 72px !important;
}

.main-header .sidebar-toggle:hover {
    background-color: #e8002a;
    padding: 20px 15px;
    height: 72px !important;
}

.icon_social {
    width: 40px;
}

.skin-red .main-header .navbar {
    background-color: #fff;
}

.skin-red .wrapper, .skin-red .main-sidebar, .skin-red .left-side {
    background-color: black;
}

.main-header .navbar {
    margin-bottom: -2px !important;
}

.skin-red .sidebar-menu > li:hover > a, .skin-red .sidebar-menu > li.active > a, .skin-red .sidebar-menu > li.menu-open > a {
    background-color: #e8002a !important;
}

.fa_icon {
    padding-left: 10px;
    font-size: 20px;
    padding-top: 5px;
}

.calender_border_bottom {
    border-bottom: 1px solid #CCC !important;
}

.contactEmail {
    color: dodgerblue;
}

.contactEmail:hover {
    color: #e8002a;
}

/*  CRM Switch  */

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    display: none;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #e8002a;
}

input:focus + .slider {
    box-shadow: 0 0 1px #e8002a;
}

input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.social-Portal {
    float: left;
    padding: 10px;
    width: 50px;
}

.wrapping {
    word-wrap: break-word !important;
}

.thumblr input[type="text"] {
    border-radius: 25px 0 0 25px !important;
}