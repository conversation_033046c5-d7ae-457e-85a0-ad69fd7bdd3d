html, body
 {
   font-family: 'poppins-regular' !important;
   font-size:14px;
   font-weight:400;
 }

 .heading-basic {
  font-family: 'poppins-regular' !important;
  margin:5px 0 !important;
  }
  .heading-basic-link {
  font-family: 'poppins-regular' !important;
  margin-top:15px;
  font-size:18px !important;
  }
  .heading-social-basic-link {
  font-family: 'poppins-regular' !important;
  margin-top:15px;
  font-size:18px !important;
  display:-webkit-box;
  display:-moz-box;
  display:-ms-flexbox;
  display:-webkit-flexbox;
  }
  .heading-social-basic-link > small > a:hover, .heading-social-basic-link > small > a:focus, .heading-social-basic-link > small > a:active, .heading-social-basic-link > small > a:visited {
	color:inherit;
  }
  
 span.form-control-feedback > img.pwd,img.emailId {
 margin-bottom:0; 
 }
 input#deleteProfile, button#preViewBtn,input#deleteContact, input#deleteProfile-xs, button#preViewBtn-xs,input#deleteContact {
 background-color:transparent !important;
 color:#999999 !important;
 border: 1px solid #999999 !important;
 }
 
 input#deleteProfile:hover, button#preViewBtn:hover,input#deleteContact:hover, input#deleteProfile-xs:hover, button#preViewBtn-xs:hover,input#deleteContact:hover {
 background-color:#999999 !important;
 color:#fff !important;
 border: 1px solid #fff !important;
 }
 
/* ---------------------------login css---------------------------*/
#errorMsgModal {
    color: red;
}

.has-feedback .form-control {
    padding-left: 42.5px !important;
    padding-right: 0 !important;
}

.emailId, .pwd {
    width: 100%;
    height: 100%;
    /*padding: 5px;*/
    padding: 8px;
}

.form-control-feedback {
    left: 0 !important;
}

 .orLIne {
	font-family: 'poppins-regular', sans-serif !important;
   /*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
   width: 100%; 
   text-align: center; 
   /*border-bottom: 1px solid #000; */
   line-height: 0.1em;
   margin: 10px 0 20px; 
   font-size:12px;
} 

.orLIne span {
    background:#fff;
    vertical-align:bottom;
}
 .alert
 {
     background-color:#78BE20 !important;
 }
 
 

 @media only screen and (min-width: 768px) {

     .modal-dialog {
     width: 340px;
     margin: 30px auto;
	}
 }
 
 @media only screen and (min-width: 350px) and (max-width:767px) {

     .modal-dialog {
     width: auto !important;
    
 }
 }
 /* ---------------------------contacts css---------------------------*/
 .small-box
 {
     background-color:#f3f3f3;
 }
 .contactsData {
	margin-bottom:15px;
 }
 .flexContainer {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-box-orient:horizontal;
     -webkit-box-direction: normal;
     width: 100%;
/*     -webkit-box-orient: horizontal;
     -webkit-box-direction: normal;*/
     -ms-flex-flow: row wrap;
     flex-flow: row wrap;
     -webkit-flex-flow: row wrap;
     padding-bottom: 30px;
 }
 .flexContainer .flexItem
 {
     -webkit-box-float: 1;
     -webkit-flex : 1 1 0;
     -ms-flex : 1 1 0;
     flex : 1 1 0;
 }
 .flexContainer .flexItem:not(:last-child) {
     margin-right: 15px;
 }
 /*.flexContainer .flexItem:not(:first-child) {
     margin-left: 15px;
 }*/
 .flexItem {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     display: -webkit-flex;
     -webkit-box-orient:vertical;
     -webkit-box-direction: normal;
     -webkit-flex-direction: column;
     -ms-flex-direction: column;
     background-color: #f3f3f3;
     border: 1px solid rgba(0,0,0,0.125);
     border-radius: 0;
     /*flex: 0 0 33.33%;*/
     flex-direction: column;
     position: relative;
     /*box-shadow: 5px 0px 40px rgba(0,0,0, .1);*/
 }
 .flex-block
 {
     -webkit-box-float: 1;
     -webkit-flex : 1 1 0%;
     -ms-flex : 1 1 0%;
     flex : 1 1 0;
     padding: 1.25em;
 }
/*.contactsData div.contactColumn:last-of-type .flexContainer .flexItem:nth-child(1) {
     -webkit-box-float: 1;
     -webkit-flex : 0 0 31.86% !important;
     -ms-flex : 0 0 31.86% !important;
     flex : 0 0 31.86% !important;
}
.contactsData div.contactColumn:last-of-type .flexContainer .flexItem:nth-child(2) {
    -webkit-box-float: 1;
    -webkit-flex : 0 0 31.86% !important;
    -ms-flex : 0 0 31.86% !important;
    flex : 0 0 31.86% !important;
}*/
.loadMore-basic {
	margin-bottom:30px;
}

 @media only screen
 and (max-width: 767px)
 and (orientation: portrait){
     .flexContainer {
         display: block;
         padding-bottom:0;
     }
     .flexContainer .flexItem {
         margin-top: 15px;
     }
     .flexContainer .flexItem:not(:last-child) {
         margin-right: 0;
     }
     .flexContainer .flexItem:not(:first-child) {
         margin-left: 0;
     }
 }
 @media only screen
 and (min-width: 320px)
 and (max-width: 767px)
 and (orientation: landscape) {
     .flexContainer .flexItem {
         margin-top: 15px;
     }
     .flexContainer .flexItem:not(:last-child) {
         margin-right: 15px;
     }
     .flexContainer .flexItem:not(:first-child) {
         margin-left: 0 !important;
     }
 }


  @media only screen
 and (min-width: 320px)
 and (max-width: 800px)
 and (orientation: portrait){
	.flexContainer .flexItem {
         flex:1 1 100% !important;
     }
     .flexContainer .flexItem:not(:last-child) {
         margin-bottom: 30px;
         margin-right:0px;
     }
 }

 .contactImage
 {
     max-width: 100%;
     width: 100%;
     height: auto;
     display: block;
     padding-top: 100%;
     border-radius: 50%;
     background-position-y: center;
     background-position-x: center;
     background-repeat: no-repeat;
     background-size: cover;
     margin: 0 auto;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
 }
 #scheduler {
	cursor:pointer;
 }
.fa-basic {
padding-right:5px;
}
/* ---------------------------fetchContact css---------------------------*/

.topStyle-basic
{
    margin-top: 20px;
}

.boxShadowEffect{
    box-shadow: 1px 1px 1px #D7D7D7 !important;
    padding:2px;
    padding-left:14px;
    font-family: inherit;
    background-color:#eee;
}
.col-lg-offset-3.col-lg-6.delete-btn {
    padding-top: 10px;
    margin-bottom: 20px;
}
.imgArea
{
    position: relative;
    border-radius:50%;
    width:150px;
    height: 150px;
}
.w3-btn-floating:hover, .w3-btn-floating:focus
{
    color:white;
}
.addImg {
    position: absolute;
    top: 0;
    right: 0;
}
@media screen and (min-width:320px) and (max-width: 767px) {
    .imgArea {
        margin: 0 auto  ;
    }
    .margin-xs-basic {
		margin-left:12px;
	}
}

/* ---------------------------fetchUserDetail css---------------------------*/

#errorMsg
{
    color:red;
    text-align: center;
    font-size:15px;
}

/*.addImg
{
    position: absolute;
    z-index: 999;
    top: -10px;
    right: -2px;
}*/

/*@media screen and (min-width: 980px) and (max-width: 991px)
{
    .addImg
    {
        position: absolute;
        z-index: 999;
        top: -10px;
        right: 12px;
    }
}
@media screen and (min-width: 950px) and (max-width: 979px) {
    .addImg {
        position: absolute;
        z-index: 999;
        top: -10px;
        right: 2px;
    }
}*/
select#countryCode ~ span.select2.select2-container.select2-container--default {
    width: 100% !important;
    font-size:12px !important;
}

/* ---------------------------fetchProfileDetail css---------------------------*/

.select2-container .select2-selection--single
{
    height:34px !important;
}
.select2-container
{
    width: 90px !important;
    font-size:12px !important;
}
.select2-container--default .select2-selection--single
{
    border-radius: 0 !important;
    padding: 6px 3px !important;
    border-color: #d2d6de;
}
.select2-container--default .select2-selection--single .select2-selection__rendered
{
    width:24px !important;
}
#select2-profileLanguageCode-cr-container
{
    font-size: 12px !important;
}
.select2-container .select2-selection--single .select2-selection__rendered
{
    overflow: visible !important;
}
@media screen and (min-width:320px)
{
    .code, .phone {
        padding:0;
    }
}
.input-basic {
    padding:0;
    margin-bottom: 15px;
}
.input-basic-alter {
    padding-left:0;
}
.input-basic-5 {
    padding-right:15px;
    padding-left:0;
    margin-top: 5px;
    margin-bottom:5px;
}

.input-basic-alter-5 {
    padding:0;
    margin-top: 5px;
    margin-bottom:5px;
}
label.boxShadowEffect.label-basic {
    width:100% !important;
    font-weight: 400;
    font-size: 20px;
}
.save, .delete, .preview {
    margin-bottom:10px;
}
.imagePreview {
    margin-bottom:15px;
}

@media screen and (max-width:768px) and (min-width:320px)
{
    .socialDiv
    {
        margin-top:0 !important;
        width:0;
        height:0;
        padding-top:10px;
    }
    .input-basic-5 {
        padding:0;
    }
    .input-basic-alter {
        padding:0;
    }
}
.socialSection
{
    margin-top:10px !important;
}
.socialRow
{
    margin-left:0 !important;
    margin-right:0 !important;
    margin-top:10px;
}
@media screen and (min-width: 320px) and (max-width: 991px) {
    .social-basic {
        padding-right:0;
    }
}

 /* ---------------------------validation css---------------------------*/
 #errorMsg
 {
     color:red;
     text-align: center;
     font-size:15px;
 }
 .required
 {
     color: red;
 }
a.mailTo, a.mailTo:visited{
    color:dodgerblue;
    text-decoration: none;
    cursor: pointer;
}

a.mailTo:hover, a.mailTo:focus
{
    color:#78BE20;
}
/* ---------------------------modal css---------------------------*/
.modal-open
{
    padding-right:0 !important;
}
@media screen and (max-width:767px) and (min-width:320px){
	.modal-dialog-basic {
		margin:0;
	}
}
.modal-basic.modal-body {
    padding:0 !important;
}
.modalGraphics {
    width:100% !important;
    height:192px !important;
    min-width: 100% !important;
    min-height: 192px !important;
    object-fit:fill;
    -o-object-fit:fill;
}
@media screen and (min-color-index:0) and(-webkit-min-device-pixel-ratio:0) 
{ @media {
    .modalGraphics { 

    width:100% !important;
    height:192px !important;
    min-width: 100% !important;
    min-height: 192px !important;
    object-fit:cover;
    -o-object-fit:fill;
} 
    }
}
@media not all and (min-resolution:.001dpcm) { @media {

    .modalGraphics { 
    width:100% !important;
    height:192px !important;
    min-width: 100% !important;
    min-height: 192px !important;
    object-fit:cover;
    -o-object-fit:fill;
	} 
}}
.center-cropped {
	max-width: 124px;
    width: 124px;
    height: 124px;
    max-height:124px;
    display: block;
    background-position-y: center;
    background-position-x: center;
    background-repeat: no-repeat;
    background-size: cover;
    margin: 0 auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
.graphics-basic {
    padding:0;
    margin:0;
   /* border-bottom:1px solid white;*/
}
.titleRow {
    height:80px;
}
/*.avatar {
	margin:5px 0;
	height:55px !important;
	width:55px !important;
}*/
.avatar {
  float: left;
  height: 55px;
  margin-bottom: 0;
  margin-right: 10px !important;
  margin-top: 8px;
  width: 55px;
}
@media screen and (max-width:320px) {
	/*.avatar {
	margin:10px 0;
	height:45px !important;
	width:45px !important;
	}*/
}

.cardContent {
    /*margin-top:5px;*/
    height: inherit;
    padding-top:5px;
    padding-bottom:5px;
    /*border-bottom:1px solid white;*/
}
.imgIcons
{
	float:left;
	width:25px; 
	height:25px;
	margin-right:10px;
	}
.cardTitle {
	font-weight:300 !important;
    color:white;
    font-size:13px !important;
}

.cardTitle-borders > a, .cardTitle-borders > a:hover, .cardTitle-borders > a:active .cardTitle-borders > a:visited,.cardTitle-borders > a:focus {
	color:white;
}
.heading-basic-link > small > a, .heading-basic-link > small > a:hover, .heading-basic-link > small > a:active .heading-basic-link > small > a:visited, .heading-basic-link > small > a:focus {
	color:#777;
	font-weight:300 !important;
}
.saveToPhone > a,.saveToPhone > a:hover, .saveToPhone > a:active, .saveToPhone > a:visited, .saveToPhone > a:focus {
	color:white;
}
a.tel_link:hover, a.tel_link:active, a.tel_link:visited, a.tel_link:focus, a.tel_link {
	color:white;
}
.cardTitle-semi-bold-title {
    color:white;
    font-weight: 600 !important;
    font-size: 12px !important;
}
.cardTitle-semi-bold {
    color:white;
    font-weight: 600 !important;
    font-size: 14px !important;
}
.cardTitle-medium {
	color:white;
	font-weight:500 !important;
	font-size: 14px !important;
}
.cardTitle-borders {
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	font-family: 'poppins-regular' !important;
	margin-top:5px !important;
	margin-bottom:10px !important;
}
.cardTitle-borders-12 {
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	font-family: 'poppins-regular' !important;
	margin-top:5px !important;
	margin-bottom:12px !important;
}
.heading-basic-10 {
	font-family: 'poppins-regular' !important;
	margin:10px 0 !important;
}
.heading-basic-10-top {
	font-family: 'poppins-regular' !important;
	margin-top:10px	 !important;
	margin-bottom:5px !important;
}
.heading-basic-15-top {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-bold !important;*/
	margin:15px 0 0 0 !important;
	font-size:16px !important;
	font-weight:700!important;
}
.heading-basic-25-top {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-bold !important;*/
	margin:25px 0 0 0 !important;
	font-size:16px !important;
	font-weight:700!important;
}
.heading-basic-20-top {
	font-family: 'poppins-semibold' !important;
	/*font-family: Poppins-bold !important;*/
	margin:20px 0 0 0 !important;
	font-size:16px !important;
	font-weight:700!important;
}

.hack {
	/*font-family: 'Poppins' !important;*/
	margin:2% 0 5% 0 !important;
}

@media screen and (min-width:376px) and (max-width:414px) {
	.titlebar {
		margin-left: -10px !important;
	}
}
@media screen and (min-width:768px) and (max-width:1024px) {
	/*.avatar {
	margin:10px 0 !important;
	}*/
}
.titlebar{
	margin-left: -1%;
	}
.heading-basic-15-bottom {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	margin:5px 0 15px 0 !important;
	/*font-size:14px !important;*/
}
.heading-basic-16-bottom {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	margin:5px 0 16px 0 !important;
	/*font-size:14px !important;*/
}
.heading-basic-17-bottom {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	margin:5px 0 17px 0 !important;
	/*font-size:14px !important;*/
}
.heading-basic-10-top {
	font-family: 'poppins-regular' !important;
	margin:10px 0 5px 0 !important;
	/*font-size:14px !important;*/
}
.heading-basic-5-top {
	font-family: 'poppins-regular' !important;
	/*font-family: Poppins-regular, Poppins-bold, Poppins-light, Poppins-medium, Poppins-semiBold !important;*/
	margin:5px 0 0 0 !important;
	/*font-size:14px !important; */
}
.cardDetails {
    padding-left:15px;
}
#contactDetail.cardDetails {
    padding-left:15px;
    height:40px !important;	
}
/*.border-basic {
    border-bottom: 1px solid white;
    height:inherit;
}*/
.contactNoCol {
    padding-right:0;
    height:41px;
}
.contactRow
{
    height:38px;
}
.contactCol {
    border-left:1px solid white;
    height: inherit;
    padding-left:0
}
.contactCol > .border-basic> a >img{
    padding: 7px;
    margin-left:-2px;
}
.socialCol {
    background-color:white;
}
.socialDetails {
    padding-left:15px;
    padding-right:15px;
    margin-bottom:10px;
    margin-top:10px;
}
.socialInfo {
    background-color: white;
    overflow-y: auto;
    overflow-x: auto;
    /*min-height: 100px;*/
    max-height: 100px;
}
.socialInfo::-webkit-scrollbar {
  -webkit-appearance: none;
  display:inherit;
  width: 5px;
  height: 5px;
  background-color: rgba(255,255,255, 0.5);
}
.socialInfo::-webkit-scrollbar-thumb {
  border-radius: 7px;
  background-color: rgba(97, 97, 97, .4);
  -webkit-box-shadow: 0 0 1px rgba(158, 158, 158, .5);
}
.socialDataRow {
  /*margin: 5px 0;*/
  /*padding-left: 5px;*/
}
.socialLinks {
    padding:0;
}
.itunesSection {
    background-color: white;
}
/*.itunesBtn {
    padding:15px 0;
}*/
.saveToPhone {
    cursor: pointer;
    color:white;
    padding:5px 0;
    font-size: 16px;
}
.socialDataRow .img-responsive {
  margin: 0;
  padding: 8px;
}
.row:nth-child(5)>.switchItApp>.cardDetails>.border-basic
{
    border-bottom: 0;
}
@media screen and (min-width: 375px) and (max-width:414px) {
	.profilePic {
		margin: 10px 0 !important; 
		height:55px !important;
		width:55px !important;
		}
	}
@media screen and (min-width: 768px) and (max-width:1024px) {
	.profilePic {
		height:50px !important;
		width:50px !important;
		}
	}	
/*-----------------------------snakbar alert---------------------*/
#snackbar {
    visibility: hidden;
    min-width: 250px;
    margin-left: -125px;
    background-color: #78BE20;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    padding: 16px;
    position: fixed;
    z-index: 1;
    left: 50%;
    top: 30px;
    font-size: 17px;
    z-index:9999;
}

#snackbar.show {
    visibility: visible;
    -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
    animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
    from {top: 0; opacity: 0;}
    to {top: 30px; opacity: 1;}
}

@keyframes fadein {
    from {top: 0; opacity: 0;}
    to {top: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
    from {top: 30px; opacity: 1;}
    to {top: 0; opacity: 0;}
}

@keyframes fadeout {
    from {top: 30px; opacity: 1;}
    to {top: 0; opacity: 0;}
}

/*----------------------------end of snakbar alert---------------*/

 /*----------------------note tooltip css-----------------*/
 .tooltipPlus  {
     text-align:left !important;
     color:#78BE20;
     margin-left:4px;
     font-size:16px;
     margin-top: 1px;
 }
 input:focus
 {
     border-color:#78BE20 !important;
 }
  /*-------------------login page----------------*/
 .login-page{
     background:#fff !important;
 }
 .login-box{
     border: solid 1px #828282;
    padding-top: 20px;
 }
 
 
 
 /*-------------------sidebar menu and top header------------------*/

/*.skin-green .main-header .logo {
background-color:#a0d162;
}
.skin-green .main-header .logo:hover {
background-color:#a0d162;
}*/

.main-header .logo {
    -webkit-transition: width .3s ease-in-out;
    -o-transition: width .3s ease-in-out;
    transition: width .3s ease-in-out;
    display: block;
    float: left;
    height: 50px;
    font-size: 20px;
    line-height: 50px;
    text-align: center;
    width: 100px !important;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    padding: 10px 0 15px;
    font-weight: 300;
    overflow: hidden;
}

.sidebar-toggle
{
    visibility:visible !important;
}

@media screen and (min-width:768px)
{
    .main-header>.navbar
    {
        margin-left:100px !important;
    }
    .sidebar-toggle
    {
        visibility:hidden !important;
    }
}

.main-header .logo {
    height: 72px !important;

}
.skin-green .main-header .navbar {
    background-color: #FAFAFA;
}
.navbar-nav>.user-image {
    border: 1px solid #BFBFBF;
    float: left;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: 4px;
    margin-left: 6px;
}
@media (max-width: 767px){
    .navbar-custom-menu > .navbar-nav
    {
        margin-right: 100px !important;
    }
    .navbar-custom-menu .navbar-nav > li > a
    {
        padding: 14px;
        line-height: 4px;
        margin-top: 10px;
    }
}
@media (min-width: 768px){
    .navbar-nav>li>a {
        padding-top: 5px !important;
        padding-bottom: 5px !important;
        margin-top: 8px !important;
    }

}
.customLogout
{
    color:#757575 !important;

}
.downCaret
{
    margin-right: 5px;
    margin-top: 15px;
    color: #757575;
    font-size:20px;
}


  .main-header .sidebar-toggle
 {
     background-color:#78be20;
     padding: 20px 15px;
     height:72px !important;
 }
 .skin-green .main-header .navbar .sidebar-toggle:hover
 {
     background-color:#78be20;
 }
  .skin-green .main-header .navbar .sidebar-toggle
  {
    background-color:#78be20;
    height:72px !important;
  }


.main-header > .navbar{margin-left:100px !important}
.skin-green .sidebar a
{
    background-color:#474747 !important;
}

@media screen and (min-width:768px)
{
    .content-wrapper, .right-side, .main-footer
    {
        margin-left:100px !important;
    }
    span.links
    {
        display: block !important;
        text-align: center !important;
    }
}
@media screen and (min-width:320px) and (max-width:767px)
{
    .content-wrapper, .right-side, .main-footer
    {
        margin-left:0px;
    }
}
.footer-basic {
color:grey;
}

@media (max-width: 767px){
    .sidebar-open .content-wrapper, .sidebar-open .right-side, .sidebar-open .main-footer {
        -webkit-transform: translate(100px, 0) !important;
        -ms-transform: translate(100px, 0) !important;
        -o-transform: translate(100px, 0) !important;
        transform: translate(100px, 0) !important;
    }
}

span.links
{
    display: block !important;
    text-align: center !important;
}

.active
{
    color: #fff;
    background: #1e282c;
    border-left-color: #a0d162;
}
/*.faceDisableMenuClick
{
    pointer-events : none;

}*/

.cardTitle-borders.cardTitle-medium {		
	  display: -webkit-box !important;				
}



/*make box rounded*/
/*login page*/
#logInForm input[type="email"],#logInForm input[type="password"],#logInForm input[type="button"]{
	border-radius: 25px;
}
/*forgot password*/
#forgotPasswordForm  input[type="email"],#forgotPasswordForm  input[type="button"]{
	border-radius: 25px;
}

/*Add Profile*/
#profileAddForm input[type="email"],#profileAddForm input[type="text"],#profileAddForm input[type="button"]{
	border-radius: 25px;
}
.socialRow input[type="text"] {
  border-radius: 0px 25px 25px 0px !important;
}
.thumblr input[type="text"] {
  border-radius: 25px 0 0 25px !important;
}
.socialSection span {
  border-radius: 25px 0 0 25px !important;
}
.thumblr span {
  border-radius: 0 25px 25px 0 !important;
}
#profileAddForm .select2-container span {
  border-radius: 25px !important;
}


/*Update Profile*/
#UpdateProfileForm input[type="email"],#UpdateProfileForm input[type="text"],#UpdateProfileForm input[type="button"],#UpdateProfileForm input[type="submit"],#UpdateProfileForm button{
	border-radius: 25px;
}
#UpdateProfileForm .select2-container span {
  border-radius: 25px !important;
}

/*fetchUserDetail*/
#fnameInput,#lnameInput,#phoneNo span.select2-selection,#phoneInput,#saveUserDetail,#updateImageSetting #file_up{
  border-radius: 25px !important;
}

/*fetchContact*/
#editNoteForm textarea#note,#editNoteForm input[type="submit"],#editNoteForm input[type="button"],#updateImageFetchUser #changeProfilePic{
  border-radius: 25px !important;
}

/*scheduleForm*/
#schedulerModal #profileNameDiv #profileName,#schedulerModal #startDateDiv #startDate,#schedulerModal #startDateDiv #endDate,#schedulerModal #descriptionDiv #description,#schedulerModal #saveBtnDiv #addScheduler{
  border-radius: 25px !important;
}

/*logout*/
.customLogout{border-radius: 25px !important;}
/*end make box rounded*/

.web span {
  border-radius:  25px  0px 0px 25px !important;
}
.web input[type="text"] {
  border-radius: 0 25px 25px 0px !important;
}

/*add social icons*/

#skype {
  border-radius: 25px !important;
  width: 100% !important;
}
.slack span:last-child {
  border-radius: 0 25px 25px 0 !important;
}
#slack {
  border-radius: 0 !important;
}

/*for mobile 360*/
@media only screen and (min-width: 300px) and (max-width: 360px) {

     .socialRow div:first-child {
  padding-left: 0px;
}
.socialRow div:last-child {
  padding-left: 0;
}
.socialRow .input-group span {
  font-size: 12px;
}
.select2.select2-container.select2-container--default.select2-container--focus {
  width: 100% !important;
}
#slack {
  padding: 5px;
}
 }
/*end for mobile 360*/

/*contact screen export to csv and load more button make round*/
#export_csv,#contactsLoadMore {
    border-radius: 25px !important;
}
/*end contact screen button format*/

.wrapping {
    word-wrap: break-word;
}

.tel_link {
    color: #fff;
}
.tel_link:visited {
    color: #fff;
}
.tel_link:focus {
    color: #fff;
}
.tel_link:active {
    color: #fff;
}