.theme-btn {
    background-color: #e8002a;
    border-color: #e8002a;
    color: #fff !important;
    font-weight: 600;
}

.theme-btn:hover {
    background-color: #e8002a;
    border-color: #e8002a;
    color: #fff;
    font-weight: 600;
}

.theme-logo {
    width: 120px;
    height: auto;
    margin-bottom: 10px;
}

p.login-box-msg {
    font-size: 14px !important;
    font-weight: 400 !important;
}

span.glyphicon.glyphicon-envelope.form-control-feedback,
span.glyphicon.glyphicon-lock.form-control-feedback {
    font-size: 14px !important;
}

.skin-red .main-header .navbar {
    background-color: #e8002a !important;
}

.logo-large {
    width: 170px;
}

.logo-small {
    width: 2em;
    height: 1.8em;
    float: right;
    margin-right: -9px;
    margin-top: 7px;
}

.validation-error-label {
    font-size: 14px !important;
    font-weight: 600;
    color: #e8002a;
    float: left !important;
}

.required {
    color: #e8002a;
}

.box.box-primary {
    border-top-color: #e8002a !important;
}

.theme-color {
    color: #e8002a;
}

.theme-btn {
    background-color: #e8002a;
    border-color: #e8002a;
    color: #ffff !important;
    font-weight: 600;
}

.login-page, .register-page {
    background: #EEEEEE !important;
}

.font-weight-700 {
    font-weight: 700;
}

.btn.btn-custom.btn-file {
    background-color: #e8002a;
    border-color: #e8002a;
    color: #fff;
    font-weight: 700;
}

.img {
    width: 20%;
}

.btn-danger {
    color: #ffffff !important;
}

.theme-a {
    font-size: 16px;
    color: #263238;
}

.theme-a:hover {
    font-size: 16px;
    color: #263238;
}

#insertUser .form-group {
    margin-bottom: 25px;
}

.common-font {
    font-size: 14px;
}

.flexItem {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    display: -webkit-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    background-color: #f3f3f3;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0;
    flex-direction: column;
    position: relative;
}

#flex-row .row {
    padding-bottom: 15px;
}

#flex-col .col-lg-4 {
    padding-bottom: 15px;
}

#flex-col .col-md-4 {
    padding-bottom: 15px;
}

#flex-col .col-sm-4 {
    padding-bottom: 15px;
}

#flex-col .col-xs-12 {
    padding-bottom: 15px;
}

.flex-block h3:hover {
    color: #e8002a;
}

.border-none {
    border: none !important;
}

.form-control-radius {
    border-radius: 25px
}

.form-control-width {
    width: 50%;
}

.padding-right {
    padding-right: 0px;
}

.padding-left {
    padding-left: 0px;
}

.padding-bottom {
    padding-bottom: 0px;
}

.padding-top {
    padding-top: 0px;
}

.facebook {
    font-size: 30px;
    color: #2D5A96;
}

.social .row {
    padding-bottom: 10px;
}

#cardForm .form-group {
    padding-bottom: 10px;
}

.image {
    width: 35px;
}

.submit-btn {
    width: 100px;
}

.social .col-lg-4 {
    padding-right: 0px !important;
}

.social .col-lg-4 {
    padding-bottom: 15px;
}

.common-bottom {
    padding-bottom: 25px;
}

.dropdown-menu a {
    color: #333 !important;
    white-space: normal;
}

.dropdown-menu > li {
    color: #333 !important;
    position: relative;
}

.dropdown-menu > li > i {
    color: #333 !important;
    position: absolute;
    left: 0;
    top: 3px;
}

.icon-color {
    color: #333;
}

.action {
    list-style-type: none;
    padding-left: 10px;
}

.dropdown-menu li {
    margin-top: 10px;
    margin-bottom: 10px;
}

#userButton a {
    color: #fff !important;
}

.common-margin {
    margin-bottom: 15px;
}

.common-bottom-padding {
    padding-bottom: 20px;
}

.remove-left-padding {
    padding-left: 0px;
}

.remove-right-padding {
    padding-right: 0px;
}

.margin-top {
    margin-top: 5px;
}

.padd-bottom {
    padding-bottom: 20px !important;
}

.cardHolder {
    width: 60px;
}

.file-top {
    padding-top: 15px;
}

.img_file {
    width: 60px;
}

.reset_title {
    background-color: #15AA59 !important;
}

.password_note {
    font-size: 12px;
    color: black;
}

.addImg:hover {
    color: #e8002a;
}

.addImg {
    position: absolute;
    top: 0px;
    right: 0px;
}

.w3-btn-floating {
    width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    text-align: center;
    color: #e8002a;
    background-color: transparent;
    z-index: 1;
    padding: 0;
    cursor: pointer;
    font-size: 24px;
}