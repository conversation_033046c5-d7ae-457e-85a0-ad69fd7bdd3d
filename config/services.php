<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],

    'facebook' => [
        'client_id' => env('FACEBOOK_APP_ID'),
        'client_secret' => env('FACEBOOK_APP_SECRET'),
        'redirect' => '',
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => '',
    ],
    'sns' => [
        'region' => env('AWS_SNS_REGION', 'us-east-1'),
        'key'    => env('AWS_SNS_KEY', env('AWS_ACCESS_KEY_ID')),
        'secret' => env('AWS_SNS_SECRET', env('AWS_SECRET_ACCESS_KEY')),
        'fcm_application_arn' => env('AWS_SNS_FCM_APPLICATION_ARN', 'arn:aws:sns:us-east-1:306313726991:app/GCM/mblion-development'),
        'platform_application_arn' => env('AWS_SNS_FCM_APPLICATION_ARN', 'arn:aws:sns:us-east-1:306313726991:app/GCM/mblion-development'),
        'general_topic_arn' => env('AWS_SNS_GENERAL_TOPIC_ARN', 'arn:aws:sns:us-east-1:306313726991:dev-mblion'),
    ],

];
