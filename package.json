{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.18", "bootstrap": "^4.0.0", "cross-env": "^5.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "jquery": "^3.2", "laravel-mix": "^2.0", "lodash": "^4.17.4", "popper.js": "^1.12", "vue": "^2.5.7"}, "dependencies": {"node-sass": "^4.11.0"}}